# LI-RAGE (Late Interaction Retrieval Augmented Generation) Experiments

## 论文信息
**"LI-RAGE: Late Interaction Retrieval Augmented Generation with Explicit Signals for Open-Domain Table Question Answering"**
- 会议: ACL 2023
- 作者: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
- 机构: Amazon Science

## LI-RAGE 核心创新

### 1. 晚交互模型 (Late Interaction Models)
- 使用 **ColBERT** 作为检索器
- 在检索时实现问题和表格嵌入的**细粒度交互**
- MaxSim 操作：每个查询token与文档token的最大相似度

### 2. 联合训练机制 (Joint Training)
- **检索器和生成器端到端联合训练**
- 使用显式的表格级监督信号
- RAVQA损失函数优化检索和生成的联合目标

### 3. 二元相关性标记 (Binary Relevance Token)
- 在生成的答案前添加 `[RELEVANT]` 或 `[IRRELEVANT]` 标记
- **推理时可以判断用于回答问题的表格是否可靠**
- 根据相关性标记过滤不可靠的答案

## 支持的数据集
- **NQ-Tables** (Natural Questions Tables) - 开放域表格问答
- **E2E-WTQ** (End-to-End WikiTableQuestions) - 开放域表格问答

## 快速开始

### 环境设置
```bash
# 安装依赖
pip install -r ../requirements.txt

# 或使用Apple Silicon安装脚本
python ../setup_robust_tableqa_m4.py
```

### ColBERT 检索器训练

#### NQ-Tables数据集
```bash
python src/main.py configs/nq_tables/colbert.jsonnet \
  --accelerator gpu --devices 8 --strategy ddp \
  --experiment_name ColBERT_NQTables_bz4_negative4 \
  --mode train \
  --opts train.batch_size=6 train.lr=0.00001 \
  model_config.num_negative_samples=4 model_config.nbits=2
```

#### E2E-WTQ数据集
```bash
python src/main.py configs/e2e_wtq/colbert.jsonnet \
  --accelerator gpu --devices 8 --strategy ddp \
  --experiment_name ColBERT_E2EWTQ_bz4_negative4 \
  --mode train \
  --modules exhaustive_search_in_testing \
  --opts train.batch_size=6 train.lr=0.00001
```

### LI-RAGE 联合训练

#### NQ-Tables + LI-RAGE
```bash
python src/main.py configs/nq_tables/colbert_rag.jsonnet \
  --accelerator gpu --devices 8 --strategy ddp \
  --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt \
  --mode train \
  --modules add_binary_labels_as_prompt \
  --opts train.batch_size=1 train.lr=0.00002 train.retriever_lr=0.00001 \
  model_config.GeneratorModelVersion=microsoft/tapex-large \
  data_loader.additional.num_knowledge_passages=5
```

#### E2E-WTQ + LI-RAGE
```bash
python src/main.py configs/e2e_wtq/colbert_rag.jsonnet \
  --accelerator gpu --devices 8 --strategy ddp \
  --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt \
  --modules add_binary_labels_as_prompt \
  --mode train \
  --opts train.batch_size=1 train.lr=0.000015 train.retriever_lr=0.00001 \
  model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq
```

### LI-RAGE 评估

#### NQ-Tables评估
```bash
python src/main.py configs/nq_tables/colbert_rag.jsonnet \
  --accelerator gpu --devices 1 --strategy ddp \
  --experiment_name RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt \
  --mode test \
  --test_evaluation_name alternative_answers \
  --modules add_binary_labels_as_prompt \
  --opts test.batch_size=1 test.load_epoch=[] \
  model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5
```

#### E2E-WTQ评估
```bash
python src/main.py configs/e2e_wtq/colbert_rag.jsonnet \
  --accelerator gpu --devices 1 --strategy ddp \
  --experiment_name RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt \
  --mode test \
  --test_evaluation_name K5 \
  --modules add_binary_labels_as_prompt \
  --opts test.batch_size=1 test.load_epoch=176 \
  model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5
```

## 配置文件说明

### ColBERT配置
- `colbert.jsonnet`: ColBERT检索器训练配置
- `model_config.nbits`: 嵌入量化位数（2/4/8）
- `model_config.num_negative_samples`: 负样本数量

### LI-RAGE配置
- `colbert_rag.jsonnet`: ColBERT + TaPEx联合训练配置
- `--modules add_binary_labels_as_prompt`: 启用二元相关性标记
- `model_config.RAVQA_loss_type`: LI-RAGE损失函数类型

### DPR基线
- `dpr.jsonnet`: DPR检索器配置
- `rag.jsonnet`: DPR + TaPEx RAG配置

## 关键特性

### 1. 晚交互检索
- **细粒度交互**: token级别的问题-表格交互
- **高效检索**: 预计算文档嵌入，查询时动态交互
- **量化压缩**: 支持2/4/8位量化减少存储

### 2. 联合优化
- **端到端训练**: 检索器和生成器同时优化
- **多任务损失**: 检索损失 + 生成损失 + RAVQA损失
- **显式信号**: 表格级相关性监督

### 3. 可靠性过滤
- **二元标记**: 答案前的相关性标记
- **置信度评估**: 根据标记过滤不可靠答案
- **鲁棒性提升**: 减少错误答案的影响

## 实验结果

LI-RAGE在开放域表格问答上取得了显著提升：
- **NQ-Tables**: 相比基线提升 X%
- **E2E-WTQ**: 相比基线提升 Y%
- **可靠性**: 二元标记有效过滤不相关答案

## 引用
```bibtex
@inproceedings{lin2023lirage,
  title={LI-RAGE: Late interaction retrieval augmented generation with explicit signals for open-domain table question answering},
  author={Lin, Weizhe and Blloshmi, Rexhina and Byrne, Bill and de Gispert, Adrià and Iglesias, Gonzalo},
  booktitle={ACL 2023},
  year={2023},
  url={https://www.amazon.science/publications/li-rage-late-interaction-retrieval-augmented-generation-with-explicit-signals-for-open-domain-table-question-answering}
}
```
