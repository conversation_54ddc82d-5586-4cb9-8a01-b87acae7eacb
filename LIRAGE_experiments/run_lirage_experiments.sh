#!/bin/bash

# LI-RAGE (Late Interaction Retrieval Augmented Generation) 实验运行脚本
# ACL 2023: "LI-RAGE: Late Interaction Retrieval Augmented Generation with Explicit Signals for Open-Domain Table Question Answering"

set -e

echo "🚀 开始 LI-RAGE 实验"
echo "==============================================="

# 设置环境变量
export PYTHONPATH="${PWD}/src:${PWD}/../shared/src:$PYTHONPATH"
export DATA_FOLDER="${PWD}/../datasets"
export EXPERIMENT_FOLDER="${PWD}/experiments"

# 创建实验目录
mkdir -p $EXPERIMENT_FOLDER

echo "📁 环境设置:"
echo "  - Python路径: $PYTHONPATH"
echo "  - 数据文件夹: $DATA_FOLDER"
echo "  - 实验文件夹: $EXPERIMENT_FOLDER"

# 函数：运行ColBERT训练
run_colbert_training() {
    local dataset=$1
    local config_file=$2
    local experiment_name=$3
    
    echo ""
    echo "🔥 训练 ColBERT 检索器 - $dataset"
    echo "-----------------------------------------------"
    echo "配置文件: $config_file"
    echo "实验名称: $experiment_name"
    
    python src/main.py $config_file \
        --accelerator gpu --devices 8 --strategy ddp \
        --experiment_name $experiment_name \
        --mode train \
        --opts train.batch_size=6 train.lr=0.00001 \
        train.epochs=1000 train.additional.early_stop_patience=10 \
        model_config.num_negative_samples=4 model_config.nbits=2
}

# 函数：运行LI-RAGE联合训练
run_lirage_training() {
    local dataset=$1
    local config_file=$2
    local experiment_name=$3
    local generator_model=$4
    
    echo ""
    echo "🔥 训练 LI-RAGE (ColBERT + TaPEx) - $dataset"
    echo "-----------------------------------------------"
    echo "配置文件: $config_file"
    echo "实验名称: $experiment_name"
    echo "生成器模型: $generator_model"
    
    python src/main.py $config_file \
        --accelerator gpu --devices 8 --strategy ddp \
        --experiment_name $experiment_name \
        --mode train \
        --modules add_binary_labels_as_prompt \
        --opts train.batch_size=1 train.lr=0.00002 train.retriever_lr=0.00001 \
        train.epochs=20 train.additional.early_stop_patience=3 \
        model_config.GeneratorModelVersion=$generator_model \
        data_loader.additional.num_knowledge_passages=5 model_config.num_beams=5
}

# 函数：运行LI-RAGE评估
run_lirage_evaluation() {
    local dataset=$1
    local config_file=$2
    local experiment_name=$3
    local test_name=$4
    local load_epoch=$5
    
    echo ""
    echo "📊 评估 LI-RAGE - $dataset"
    echo "-----------------------------------------------"
    echo "配置文件: $config_file"
    echo "实验名称: $experiment_name"
    echo "测试名称: $test_name"
    
    python src/main.py $config_file \
        --accelerator gpu --devices 1 --strategy ddp \
        --experiment_name $experiment_name \
        --mode test \
        --test_evaluation_name $test_name \
        --modules add_binary_labels_as_prompt \
        --opts test.batch_size=1 test.load_epoch=$load_epoch \
        model_config.num_beams=5 data_loader.additional.num_knowledge_passages=5
}

# 函数：运行DPR基线
run_dpr_baseline() {
    local dataset=$1
    local config_file=$2
    local experiment_name=$3
    
    echo ""
    echo "🔥 训练 DPR 基线 - $dataset"
    echo "-----------------------------------------------"
    echo "配置文件: $config_file"
    echo "实验名称: $experiment_name"
    
    python src/main.py $config_file \
        --accelerator gpu --devices 8 --strategy ddp \
        --experiment_name $experiment_name \
        --mode train \
        --modules negative_samples_across_gpus exhaustive_search_in_testing \
        --opts train.batch_size=8 train.lr=0.00001 \
        train.epochs=1000 train.additional.early_stop_patience=10 \
        model_config.num_negative_samples=4
}

# 主实验流程
main() {
    case "${1:-all}" in
        "colbert")
            echo "🎯 运行 ColBERT 检索器训练"
            
            # NQ-Tables ColBERT训练
            run_colbert_training "NQ-Tables" \
                "configs/nq_tables/colbert.jsonnet" \
                "ColBERT_NQTables_bz4_negative4_fix_doclen_full_search_NewcrossGPU"
            
            # E2E-WTQ ColBERT训练
            run_colbert_training "E2E-WTQ" \
                "configs/e2e_wtq/colbert.jsonnet" \
                "ColBERT_E2EWTQ_bz4_negative4_fix_doclen_full_search_NewcrossGPU"
            ;;
            
        "lirage")
            echo "🎯 运行 LI-RAGE 联合训练"
            
            # NQ-Tables LI-RAGE训练
            run_lirage_training "NQ-Tables" \
                "configs/nq_tables/colbert_rag.jsonnet" \
                "RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt" \
                "microsoft/tapex-large"
            
            # E2E-WTQ LI-RAGE训练
            run_lirage_training "E2E-WTQ" \
                "configs/e2e_wtq/colbert_rag.jsonnet" \
                "RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained" \
                "microsoft/tapex-large-finetuned-wtq"
            ;;
            
        "eval")
            echo "🎯 运行 LI-RAGE 评估"
            
            # NQ-Tables评估
            run_lirage_evaluation "NQ-Tables" \
                "configs/nq_tables/colbert_rag.jsonnet" \
                "RAG_ColBERT_NQTables_RAVQA_Approach5_add_prompt" \
                "alternative_answers" "[]"
            
            # E2E-WTQ评估
            run_lirage_evaluation "E2E-WTQ" \
                "configs/e2e_wtq/colbert_rag.jsonnet" \
                "RAG_ColBERT_E2EWTQ_RAVQA_Approach5_add_prompt_pretrained" \
                "K5" "176"
            ;;
            
        "dpr")
            echo "🎯 运行 DPR 基线实验"
            
            # NQ-Tables DPR训练
            run_dpr_baseline "NQ-Tables" \
                "configs/nq_tables/dpr.jsonnet" \
                "DPR_NQTables_train_bz8_gc_4_crossGPU"
            
            # E2E-WTQ DPR训练
            run_dpr_baseline "E2E-WTQ" \
                "configs/e2e_wtq/dpr.jsonnet" \
                "DPR_E2EWTQ_train_bz8_gc_4_neg4"
            ;;
            
        "all")
            echo "🎯 运行完整 LI-RAGE 实验流程"
            echo "注意: 这将运行完整的实验流程，可能需要数天时间"
            read -p "确认继续? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                $0 colbert
                $0 lirage
                $0 eval
            else
                echo "实验已取消"
                exit 0
            fi
            ;;
            
        "help"|"-h"|"--help")
            echo "LI-RAGE 实验运行脚本"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  colbert  - 训练ColBERT检索器"
            echo "  lirage   - 训练LI-RAGE联合模型"
            echo "  eval     - 评估LI-RAGE模型"
            echo "  dpr      - 训练DPR基线模型"
            echo "  all      - 运行完整实验流程"
            echo "  help     - 显示此帮助信息"
            echo ""
            echo "实验流程:"
            echo "  1. colbert - 先训练ColBERT检索器"
            echo "  2. lirage  - 基于ColBERT训练LI-RAGE"
            echo "  3. eval    - 评估最终模型性能"
            echo ""
            echo "示例:"
            echo "  $0 colbert   # 只训练ColBERT检索器"
            echo "  $0 lirage    # 只训练LI-RAGE模型"
            echo "  $0 eval      # 只评估模型"
            echo "  $0 all       # 完整实验流程"
            ;;
            
        *)
            echo "❌ 未知选项: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 检查依赖
check_dependencies() {
    echo "🔍 检查依赖..."
    
    if ! command -v python &> /dev/null; then
        echo "❌ Python 未安装"
        exit 1
    fi
    
    if [ ! -f "../requirements.txt" ]; then
        echo "❌ requirements.txt 未找到"
        exit 1
    fi
    
    if [ ! -d "src" ]; then
        echo "❌ src 目录未找到"
        exit 1
    fi
    
    if [ ! -d "src/ColBERT" ]; then
        echo "❌ ColBERT 模块未找到"
        exit 1
    fi
    
    echo "✅ 依赖检查通过"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
    echo ""
    echo "🎉 LI-RAGE 实验完成!"
    echo "==============================================="
fi
