#!/bin/bash

# LI-RAGE 服务器实验脚本
# 使用 Intel/ColBERT-NQ 预训练模型

set -e

echo "🚀 LI-RAGE 服务器实验 - 使用 Intel ColBERT-NQ 模型"
echo "==============================================="

# 配置变量 - 需要根据服务器环境修改
COLBERT_MODEL_PATH="/path/to/your/server/Intel/ColBERT-NQ"  # 修改为服务器上的路径
EXPERIMENT_BASE_NAME="RAG_Intel_ColBERT_NQ"
DISABLE_WANDB="--disable_wandb_logging"  # 如果服务器支持 wandb，可以删除这个参数

# 设置环境变量
export PYTHONPATH="${PWD}/src:${PWD}/../shared/src:$PYTHONPATH"
export DATA_FOLDER="${PWD}/../datasets"
export EXPERIMENT_FOLDER="${PWD}/experiments"

echo "📁 环境设置:"
echo "  - ColBERT 模型路径: $COLBERT_MODEL_PATH"
echo "  - 实验文件夹: $EXPERIMENT_FOLDER"

# 函数：NQ-Tables LI-RAGE 训练
run_nq_tables_lirage() {
    echo ""
    echo "🔥 训练 LI-RAGE - NQ-Tables (使用 Intel ColBERT-NQ)"
    echo "-----------------------------------------------"
    
    python src/main.py configs/nq_tables/colbert_rag.jsonnet \
        --accelerator gpu --devices 8 --strategy ddp \
        --experiment_name "${EXPERIMENT_BASE_NAME}_NQTables" \
        --mode train \
        $DISABLE_WANDB \
        --modules add_binary_labels_as_prompt \
        --opts model_config.QueryEncoderModelVersion="$COLBERT_MODEL_PATH" \
        train.batch_size=1 train.lr=0.00002 train.retriever_lr=0.00001 \
        train.epochs=20 train.additional.early_stop_patience=3 \
        model_config.GeneratorModelVersion=microsoft/tapex-large \
        data_loader.additional.num_knowledge_passages=5 \
        model_config.num_beams=5
}

# 函数：E2E-WTQ LI-RAGE 训练
run_e2e_wtq_lirage() {
    echo ""
    echo "🔥 训练 LI-RAGE - E2E-WTQ (使用 Intel ColBERT-NQ)"
    echo "-----------------------------------------------"
    
    python src/main.py configs/e2e_wtq/colbert_rag.jsonnet \
        --accelerator gpu --devices 8 --strategy ddp \
        --experiment_name "${EXPERIMENT_BASE_NAME}_E2EWTQ" \
        --mode train \
        $DISABLE_WANDB \
        --modules add_binary_labels_as_prompt \
        --opts model_config.QueryEncoderModelVersion="$COLBERT_MODEL_PATH" \
        train.batch_size=1 train.lr=0.000015 train.retriever_lr=0.00001 \
        train.epochs=20 train.additional.early_stop_patience=3 \
        model_config.GeneratorModelVersion=microsoft/tapex-large-finetuned-wtq \
        data_loader.additional.num_knowledge_passages=5 \
        model_config.num_beams=5
}

# 主函数
main() {
    case "${1:-help}" in
        "nq")
            echo "🎯 运行 NQ-Tables LI-RAGE 训练"
            run_nq_tables_lirage
            ;;
            
        "wtq")
            echo "🎯 运行 E2E-WTQ LI-RAGE 训练"
            run_e2e_wtq_lirage
            ;;
            
        "help"|"-h"|"--help")
            echo "LI-RAGE 服务器实验脚本 (使用 Intel ColBERT-NQ)"
            echo ""
            echo "使用前请修改脚本中的 COLBERT_MODEL_PATH 变量"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  nq       - 训练 NQ-Tables LI-RAGE"
            echo "  wtq      - 训练 E2E-WTQ LI-RAGE"
            echo "  help     - 显示此帮助信息"
            echo ""
            echo "配置:"
            echo "  ColBERT 模型: $COLBERT_MODEL_PATH"
            echo "  实验名称前缀: $EXPERIMENT_BASE_NAME"
            ;;
            
        *)
            echo "❌ 未知选项: $1"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 检查 ColBERT 模型路径
check_model_path() {
    if [[ "$COLBERT_MODEL_PATH" == "/path/to/your/server/Intel/ColBERT-NQ" ]]; then
        echo "⚠️  警告: 请先修改脚本中的 COLBERT_MODEL_PATH 变量"
        echo "   当前路径: $COLBERT_MODEL_PATH"
        echo "   请改为服务器上 Intel/ColBERT-NQ 模型的实际路径"
        echo ""
        echo "   例如: COLBERT_MODEL_PATH=\"/home/<USER>/.cache/modelscope/hub/models/Intel/ColBERT-NQ\""
        echo ""
        exit 1
    fi
    
    echo "✅ ColBERT 模型路径已配置: $COLBERT_MODEL_PATH"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_model_path
    main "$@"
    echo ""
    echo "🎉 LI-RAGE 服务器实验完成!"
fi
