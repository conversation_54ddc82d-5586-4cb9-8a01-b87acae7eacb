"Payload type (PT)","Name","Type","No. of channels","Clock rate (Hz)","Frame size (ms)","Default packet size (ms)","Description","References"
"0","PCMU","audio","1","8000","any","20","ITU-T G.711 PCM µ-Law Audio 64 kbit/s","RFC 3551"
"1","reserved (previously 1016)","audio","1","8000","","","reserved, previously CELP Audio 4.8 kbit/s","RFC 3551, previously RFC 1890"
"2","reserved (previously G721)","audio","1","8000","","","reserved, previously ITU-T G.721 ADPCM Audio 32 kbit/s","RFC 3551, previously RFC 1890"
"3","GSM","audio","1","8000","20","20","European GSM Full Rate Audio 13 kbit/s (GSM 06.10)","RFC 3551"
"4","G723","audio","1","8000","30","30","ITU-T G.723.1","RFC 3551"
"5","DVI4","audio","1","8000","any","20","IMA ADPCM Audio 32 kbit/s","RFC 3551"
"6","DVI4","audio","1","16000","any","20","IMA ADPCM 64 kbit/s","RFC 3551"
"7","LPC","audio","1","8000","any","20","Experimental Linear Predictive Coding Audio","RFC 3551"
"8","PCMA","audio","1","8000","any","20","ITU-T G.711 PCM A-Law Audio 64 kbit/s","RFC 3551"
"9","G722","audio","1","8000","any","20","ITU-T G.722 Audio","RFC 3551 - Page 14"
"10","L16","audio","2","44100","any","20","Linear PCM 16-bit Stereo Audio 1411.2 kbit/s, uncompressed","RFC 3551, Page 27"
"11","L16","audio","1","44100","any","20","Linear PCM 16-bit Audio 705.6 kbit/s, uncompressed","RFC 3551, Page 27"
"12","QCELP","audio","1","8000","20","20","Qualcomm Code Excited Linear Prediction","RFC 2658, RFC 3551"
"13","CN","audio","1","8000","","","Comfort noise. Payload type used with audio codecs that do not support comfort noise as part of the codec itself such as G.711, G.722.1, G.722, G.726, G.727, G.728, GSM 06.10, Siren, and RTAudio.","RFC 3389"
"14","MPA","audio","1","90000","","","MPEG-1 or MPEG-2 Audio Only","RFC 3551, RFC 2250"
"15","G728","audio","1","8000","2.5","20","ITU-T G.728 Audio 16 kbit/s","RFC 3551"
"16","DVI4","audio","1","11025","any","20","IMA ADPCM","RFC 3551"
"17","DVI4","audio","1","22050","any","20","IMA ADPCM","RFC 3551"
"18","G729","audio","1","8000","10","20","ITU-T G.729 and G.729a","RFC 3551, Page 20"
"25","CELB","video","1","90000","","","Sun's CellB Video Encoding","RFC 2029"
"26","JPEG","video","1","90000","","","JPEG Video","RFC 2435"
"28","NV","video","1","90000","","","Xerox PARC's Network Video (nv)","RFC 3551, Page 32"
"31","H261","video","1","90000","","","ITU-T H.261 Video","RFC 4587"
"32","MPV","video","1","90000","","","MPEG-1 and MPEG-2 Video","RFC 2250"
"33","MP2T","audio/video","1","90000","","","MPEG-2 transport stream Video","RFC 2250"
"34","H263","video","","90000","","","H.263 video, first version (1996)","RFC 3551, RFC 2190"
"35 - 71","unassigned","","","","","","","RFC 3551, Page 32"
"72 - 76","Reserved for RTCP conflict avoidance","N/A","","N/A","","","","RFC 3551, Page 32"
"77 - 95","unassigned","","","","","","","RFC 3551, Page 32"
"dynamic","H263-1998","video","","90000","","","H.263 video, second version (1998)","RFC 3551, RFC 4629, RFC 2190"
"dynamic","H263-2000","video","","90000","","","H.263 video, third version (2000)","RFC 4629"
"dynamic (or profile)","H264","video","","90000","","","H.264 video (MPEG-4 Part 10)","RFC 6184, previously RFC 3984"
"dynamic (or profile)","theora","video","","90000","","","Theora video","draft-barbato-avt-rtp-theora-01"
"dynamic","iLBC","audio","1","8000","20 or 30","20 or 30, respectively","Internet low Bitrate Codec 13.33 or 15.2 kbit/s","RFC 3952"
"dynamic","PCMA-WB","audio","","16000","5","","ITU-T G.711.1, A-law","RFC 5391"
"dynamic","PCMU-WB","audio","","16000","5","","ITU-T G.711.1, µ-law","RFC 5391"
"dynamic","G718","audio","","32000 (placeholder)","20","","ITU-T G.718","draft-ietf-avt-rtp-g718-03"
"dynamic","G719","audio","(various)","48000","20","","ITU-T G.719","RFC 5404"
"dynamic","G7221","audio","","32000, 16000","20","","ITU-T G.722.1","RFC 5577"
"dynamic","G726-16","audio","1","8000","any","20","ITU-T G.726 audio with 16 kbit/s","RFC 3551"
"dynamic","G726-24","audio","1","8000","any","20","ITU-T G.726 audio with 24 kbit/s","RFC 3551"
"dynamic","G726-32","audio","1","8000","any","20","ITU-T G.726 audio with 32 kbit/s","RFC 3551"
"dynamic","G726-40","audio","1","8000","any","20","ITU-T G.726 audio with 40 kbit/s","RFC 3551"
"dynamic","G729D","audio","1","8000","10","20","ITU-T G.729 Annex D","RFC 3551"
"dynamic","G729E","audio","1","8000","10","20","ITU-T G.729 Annex E","RFC 3551"
"dynamic","G7291","audio","","16000","20","","ITU-T G.729.1","RFC 4749"
"dynamic","GSM-EFR","audio","1","8000","20","20","ITU-T GSM-EFR (GSM 06.60)","RFC 3551"
"dynamic","GSM-HR-08","audio","1","8000","20","","ITU-T GSM-HR (GSM 06.20)","RFC 5993"
"dynamic (or profile)","AMR","audio","(various)","8000","20","","Adaptive Multi-Rate audio","RFC 4867"
"dynamic (or profile)","AMR-WB","audio","(various)","16000","20","","Adaptive Multi-Rate Wideband audio (ITU-T G.722.2)","RFC 4867"
"dynamic (or profile)","AMR-WB+","audio","1, 2 or omit","72000","80 (super-frame; internally divided in to transport frames of 13.33, 14.22, 15, 16, 17.78, 20, 21.33, 24, 26.67, 30, 35.55, or 40)","","Extended Adaptive Multi Rate – WideBand audio","RFC 4352"
"dynamic (or profile)","vorbis","audio","(various)","any (must be a multiple of sample rate)","","as many Vorbis packets as fit within the path MTU, unless it exceeds an application's desired transmission latency","RTP Payload Format for Vorbis Encoded Audio","RFC 5215"
"dynamic (or profile)","opus","audio","1, 2","48000","2.5, 5, 10, 20, 40, or 60","20, minimum allowed value 3 (rounded from 2.5), maximum allowed value 120 (allowed values are 3, 5, 10, 20, 40, or 60 or an arbitrary multiple of Opus frame sizes rounded up to the next full integer value up to a maximum value of 120)","RTP Payload Format for Opus Speech and Audio Codec","draft"
"dynamic (or profile)","speex","audio","1","8000, 16000 or 32000","20","","RTP Payload Format for the Speex Codec","RFC 5574"
"dynamic (96-127)","mpa-robust","audio","","90000","","","A More Loss-Tolerant RTP Payload Format for MP3 Audio","RFC 5219"
"dynamic (or profile)","MP4A-LATM","audio","","90000 or others","","recommended same as frame size","RTP Payload Format for MPEG-4 Audio","RFC 6416 (previously RFC 3016)"
"dynamic (or profile)","MP4V-ES","video","","90000 or others","","recommended same as frame size","RTP Payload Format for MPEG-4 Visual","RFC 6416 (previously RFC 3016)"
"dynamic (or profile)","mpeg4-generic","audio/video","","90000 or other","","","RTP Payload Format for Transport of MPEG-4 Elementary Streams","RFC 3640"
"dynamic","VP8","video","","90000","","","RTP Payload Format for Transport of VP8 Streams","draft-ietf-payload-vp8-08"
"dynamic","L8","audio","(various)","(various)","any","20","Linear PCM 8-bit audio with 128 offset","RFC 3551 Section 4.5.10 and Table 5"
"dynamic","DAT12","audio","(various)","8000, 11025, 16000, 22050, 24000, 32000, 44100, 48000 or others","any","20 (by analogy with L16)","IEC 61119 12-bit nonlinear audio","RFC 3190 Section 3"
"dynamic","L16","audio","(various)","8000, 11025, 16000, 22050, 24000, 32000, 44100, 48000 or others","any","20","Linear PCM 16-bit audio","RFC 3551 Section 4.5.11, RFC 2586"
"dynamic","L20","audio","(various)","8000, 11025, 16000, 22050, 24000, 32000, 44100, 48000 or others","any","20 (by analogy with L16)","Linear PCM 20-bit audio","RFC 3190 Section 4"
"dynamic","L24","audio","(various)","8000, 11025, 16000, 22050, 24000, 32000, 44100, 48000 or others","any","20 (by analogy with L16)","Linear PCM 24-bit audio","RFC 3190 Section 4"
