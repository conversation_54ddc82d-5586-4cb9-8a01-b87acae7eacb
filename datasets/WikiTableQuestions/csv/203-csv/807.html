<table class="wikitable">
<tr>
<th></th>
<th>'be'</th>
<th>'remain'</th>
<th>'give'</th>
<th>'save'</th>
</tr>
<tr>
<td>1st sg</td>
<td>esmì</td>
<td>liekmì</td>
<td>dúomi</td>
<td>gélbmi</td>
</tr>
<tr>
<td>2nd sg</td>
<td>esì</td>
<td>lieksì</td>
<td>dúosi</td>
<td>gélbsi</td>
</tr>
<tr>
<td>3rd sg</td>
<td>ẽst(i)</td>
<td>liẽkt(i)</td>
<td>dúost(i)</td>
<td>gélbt(i)</td>
</tr>
<tr>
<td>1st dual</td>
<td>esvà</td>
<td>liekvà</td>
<td>dúova</td>
<td>gélbva</td>
</tr>
<tr>
<td>2nd dual</td>
<td>està</td>
<td>liektà</td>
<td>dúosta</td>
<td>gélbta</td>
</tr>
<tr>
<td>1st pl</td>
<td>esmè</td>
<td>liekmè</td>
<td>dúome</td>
<td>gélbme</td>
</tr>
<tr>
<td>2nd pl</td>
<td>estè</td>
<td>liektè</td>
<td>dúoste</td>
<td>gélbte</td>
</tr>
<tr>
<td>3rd pl</td>
<td>ẽsti</td>
<td>liẽkt(i)</td>
<td>dúost(i)</td>
<td>gélbt(i)</td>
</tr>
</table>
