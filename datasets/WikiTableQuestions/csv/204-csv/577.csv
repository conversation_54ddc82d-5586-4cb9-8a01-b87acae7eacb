"Name","First operational","Numeral system","Computing mechanism","Programming","Turing complete"
"Zuse Z3 (Germany)","May 1941","Binary floating point","Electro-mechanical","Program-controlled by punched 35 mm film stock (but no conditional branch)","In theory (1998)"
"Atana<PERSON>ff–Berry Computer (US)","1942","Binary","Electronic","Not programmable—single purpose","No"
"Colossus Mark 1 (UK)","February 1944","Binary","Electronic","Program-controlled by patch cables and switches","No"
"Harvard Mark I – IBM ASCC (US)","May 1944","Decimal","Electro-mechanical","Program-controlled by 24-channel punched paper tape (but no conditional branch)","Debatable"
"Colossus Mark 2 (UK)","June 1944","Binary","Electronic","Program-controlled by patch cables and switches","In theory (2011)"
"Zuse Z4 (Germany)","March 1945","Binary floating point","Electro-mechanical","Program-controlled by punched 35 mm film stock","Yes"
"ENIAC (US)","July 1946","Decimal","Electronic","Program-controlled by patch cables and switches","Yes"
"Manchester Small-Scale Experimental Machine (Baby) (UK)","June 1948","Binary","Electronic","Stored-program in Williams cathode ray tube memory","Yes"
"Modified ENIAC (US)","September 1948","Decimal","Electronic","Read-only stored programming mechanism using the Function Tables as program ROM","Yes"
"Manchester Mark 1 (UK)","April 1949","Binary","Electronic","Stored-program in Williams cathode ray tube memory and magnetic drum memory","Yes"
"EDSAC (UK)","May 1949","Binary","Electronic","Stored-program in mercury delay line memory","Yes"
"CSIRAC (Australia)","November 1949","Binary","Electronic","Stored-program in mercury delay line memory","Yes"
