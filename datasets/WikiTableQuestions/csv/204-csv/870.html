<table class="wikitable sortable" style="font-size: 85%; text-align: left; width: auto;">
<tr valign="top">
<th style="width:12em">Product</th>
<th>Main Functionality</th>
<th>Input Format</th>
<th>Output Format</th>
<th>Platform</th>
<th>License and cost</th>
<th>Notes</th>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/AllegroGraph" title="AllegroGraph">AllegroGraph</a></th>
<td>Graph Database. <a class="mw-redirect" href="//en.wikipedia.org/wiki/RDF_schema" title="RDF schema">RDF</a> with <PERSON>ruff visualization tool</td>
<td><a class="mw-redirect" href="//en.wikipedia.org/wiki/RDF_schema" title="RDF schema">RDF</a></td>
<td>RDF</td>
<td>Linux, Mac, Windows</td>
<td>Free and Commercial</td>
<td>AllegroGraph is a graph database. It is disk-based, fully transactional OLTP database that stores data structured in graphs rather than in tables. AllegroGraph includes a Social Networking Analytics library.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/EgoNet" title="EgoNet">EgoNet</a></th>
<td>Ego-centric network analysis</td>
<td>Conducts interviews or takes any valid XML file</td>
<td>Output to CSV and convertible to almost any other format</td>
<td>Any system supporting Java</td>
<td>Open Source, seeking contributors</td>
<td>Egonet is a program for the collection and analysis of egocentric network data. Egonet contains facilities to assist in creating the questionnaire, collecting the data and providing general global network measures and data matrixes that can be used in further analysis by other software programs.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Gephi" title="Gephi">Gephi</a></th>
<td>Graph exploration and manipulation software</td>
<td>GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), Pajek(.net, .gml), Sonivis(.graphml), Tulip(.tlp, .dot), UCINET(.dl), yEd(.gml), Gephi (.gexf), Edge list(.csv), databases</td>
<td>GUESS(.gdf), Gephi(.gexf), .svg, .png</td>
<td>Any system supporting Java 1.6 and OpenGL</td>
<td>Open Source (GPL3), seeking contributors</td>
<td>Gephi<sup class="reference" id="cite_ref-9"><a href="#cite_note-9"><span>[</span>9<span>]</span></a></sup>is an interactive visualization and exploration platform for all kinds of networks and complex systems, dynamic and hierarchical graphs. It is a tool for people that have to explore and understand graphs. The user interacts with the representation, manipulate the structures, shapes and colors to reveal hidden properties. It uses a 3D render engine to display large networks in real-time and to speed up the exploration. A flexible and multi-task architecture brings new possibilities to work with complex data sets and produce valuable visual results.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/GraphStream" title="GraphStream">GraphStream</a></th>
<td>Dynamic Graph Library</td>
<td>GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), edge list</td>
<td>GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), image sequence</td>
<td>Any system supporting Java</td>
<td>Open Source</td>
<td>With <a href="//en.wikipedia.org/wiki/GraphStream" title="GraphStream">GraphStream</a> you deal with graphs. Static and Dynamic.
<p>You create them from scratch, from a file or any source. You display and render them.</p>
</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Graph-tool" title="Graph-tool">Graph-tool</a></th>
<td>Python module for efficient analysis and visualization of graphs.</td>
<td>GraphViz(.dot), <a href="//en.wikipedia.org/wiki/GraphML" title="GraphML">GraphML</a></td>
<td>GraphViz(.dot), <a href="//en.wikipedia.org/wiki/GraphML" title="GraphML">GraphML</a>, .bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib</td>
<td>GNU/Linux, Mac</td>
<td>Free Software (GPL3)</td>
<td><a href="//en.wikipedia.org/wiki/Graph-tool" title="Graph-tool">Graph-tool</a> is a python module for efficient analysis of graphs. Its core data structures and algorithms are implemented in C++, with heavy use of <a href="//en.wikipedia.org/wiki/Template_metaprogramming" title="Template metaprogramming">Template metaprogramming</a>, based on the <a class="mw-redirect" href="//en.wikipedia.org/wiki/Boost_Graph_Library" title="Boost Graph Library">Boost Graph Library</a>. It contains a comprehensive list of algorithms.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Graphviz" title="Graphviz">Graphviz</a></th>
<td>Graph vizualisation software</td>
<td>GraphViz(.dot)</td>
<td>.bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib</td>
<td>Linux, Mac, Windows</td>
<td>Open Source (CPL)</td>
<td>Graphviz is open source graph visualization framework. It has several main graph layout programs suitable for social network visualization.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/JUNG" title="JUNG">Java Universal Network/Graph (JUNG) Framework</a></th>
<td>network and graph manipulation, analysis, and visualization</td>
<td>built-in support for GraphML, Pajek, and some text formats; user can create parsers for any desired format</td>
<td>built-in support for GraphML, Pajek, and some text formats; user can create exporters for any desired format</td>
<td>Any platform supporting Java</td>
<td>Open source (BSD license)</td>
<td>JUNG is a Java API and library that provides a common and extensible language for the modeling, analysis, and visualization of relational data. It supports a variety of graph types (including hypergraphs), supports graph elements of any type and with any properties, enables customizable visualizations, and includes algorithms from graph theory, data mining, and social network analysis (e.g., clustering, decomposition, optimization, random graph generation, statistical analysis, distances, flows, and centrality (PageRank, HITS, etc.)). It is limited only by the amount of memory allocated to Java.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Mathematica" title="Mathematica">Mathematica</a></th>
<td>Graph analysis, statistics, data visualization, optimization, image recognition.</td>
<td>3DS, ACO, Affymetrix, AIFF, ApacheLog, ArcGRID, AU, AVI, Base64, BDF, Binary, Bit, BMP, Byte, BYU, BZIP2, CDED, CDF, Character16, Character8, CIF, Complex128, Complex256, Complex64, CSV, CUR, DBF, DICOM, DIF, DIMACS, Directory, DOT, DXF, EDF, EPS, ExpressionML, FASTA, FITS, FLAC, GenBank, GeoTIFF, GIF, GPX, Graph6, Graphlet, GraphML, GRIB, GTOPO30, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, ICO, ICS, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LaTeX, LEDA, List, LWO, MAT, MathML, MBOX, MDB, MGF, MMCIF, MOL, MOL2, MPS, MTP, MTX, MX, NASACDF, NB, NDK, NetCDF, NEXUS, NOFF, OBJ, ODS, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RSS, RTF, SCT, SDF, SDTS, SDTSDEM, SHP, SMILES, SND, SP3, Sparse6, STL, String, SurferGrid, SXC, Table, TAR, TerminatedString, Text, TGA, TGF, TIFF, TIGER, TLE, TSV, USGSDEM, UUE, VCF, VCS, VTK, WAV, Wave64, WDX, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XPORT, XYZ, ZIP</td>
<td>3DS, ACO, AIFF, AU, AVI, Base64, Binary, Bit, BMP, Byte, BYU, BZIP2, C, CDF, Character16, Character8, Complex128, Complex256, Complex64, CSV, DICOM, DIF, DIMACS, DOT, DXF, EMF, EPS, ExpressionML, FASTA, FITS, FLAC, FLV, GIF, Graph6, Graphlet, GraphML, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LEDA, List, LWO, MAT, MathML, Maya, MGF, MIDI, MOL, MOL2, MTX, MX, NASACDF, NB, NetCDF, NEXUS, NOFF, OBJ, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, POV, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RTF, SCT, SDF, SND, Sparse6, STL, String, SurferGrid, SVG, SWF, Table, TAR, TerminatedString, TeX, Text, TGA, TGF, TIFF, TSV, UUE, VideoFrames, VRML, VTK, WAV, Wave64, WDX, WMF, X3D, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XYZ, ZIP, ZPR</td>
<td>Windows, Macintosh, Linux</td>
<td>Commercial</td>
<td><a href="//en.wikipedia.org/wiki/Mathematica" title="Mathematica">Mathematica</a> is a general purpose computation and analysis environment.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a class="external text" href="http://netlytic.org/" rel="nofollow">Netlytic</a></th>
<td>Cloud based text &amp; social network analyzer</td>
<td>RSS, Google Drive, Twitter, YouTube comments, .csv, .txt</td>
<td>.csv, .mds, .dl (UCINET), .net (Pajek)</td>
<td>Windows, Linux, Mac</td>
<td>Freemium</td>
<td>Netlytic allows users to automatically summarize large volumes of text &amp; discover social networks from conversations on social media such as Twitter, YouTube, blogs, online forums &amp; chats. Netlytic can automatically build chain networks &amp; personal name networks, based on who replies to whom &amp; who mentioned whom.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/NodeXL" title="NodeXL">Network Overview Discovery Exploration for Excel (NodeXL)</a></th>
<td>Network overview, discovery and exploration</td>
<td>email, .csv (text), .txt, .xls (Excel), .xslt (Excel 2007, 2010, 2013), .net (Pajek), .dl (UCINet), GraphML</td>
<td>.csv (text), .txt, .xls (Excel), .xslt (Excel 2007), .dl (UCINet), GraphML</td>
<td>Windows XP/Vista/7</td>
<td>Free (Ms-PL)</td>
<td>NodeXL is a free and open Excel 2007, 2010, 2013 Add-in and C#/.Net library for network analysis and visualization. It integrates into Excel 2007, 2010, 2013 and adds directed graph as a chart type to the spreadsheet and calculates a core set of network metrics and scores. Supports extracting email, Twitter, YouTube, Facebook, WWW, Wiki and flickr social networks. Accepts edge lists and matrix representations of graphs. Allows for easy and automated manipulation and filtering of underlying data in spreadsheet format. Multiple network visualization layouts. Reads and writes Pajek, UCINet and GraphML files.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/NetMiner" title="NetMiner">NetMiner</a> 4</th>
<td>All-in-one Software for Network Analysis and Visualization</td>
<td>.xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), .gml; NMF(proprietary)</td>
<td>.xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), NMF(proprietary)</td>
<td>Microsoft Windows</td>
<td>Commercial with free trial</td>
<td>NetMiner is a software tool for exploratory analysis and visualization of large network data. NetMiner 4 embed internal Python-based script engine which equipped with the automatic Script Generator for unskilled users. Then the users can operate NetMiner 4 with existing GUI or programmable script language.
<p>Main features include : analysis of large networks(+10,000,000 nodes), comprehensive network measures and models, both exploratory &amp; confirmatory analysis, interactive visual analytics, what-if network analysis, built-in statistical procedures and charts, full documentation(1,000+ pages of User's Manual), expressive network data model, facilities for data &amp; workflow management, Python-based Script workbench and user-friendliness.</p>
</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/NetworkX" title="NetworkX">NetworkX</a></th>
<td>Python package for the creation, manipulation, and study of the structure, dynamics, and functions of complex networks.</td>
<td>GML, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA</td>
<td>GML, Gnome Dia, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA, and assorted image formats (.jpg, .png, .ps, .svg, et al.)</td>
<td>Open source (GPL and similar)</td>
<td>Free</td>
<td>NetworkX (NX) is a toolset for graph creation, manipulation, analysis, and visualization. User interface is through scripting/command-line provided by Python. NX includes a several algorithms, metrics and graph generators. Visualization is provided through pylab and graphviz.
<p>NX is an open-source project, in active development since 2004 with an open bug-tracking site, and user forums. Development is sponsored by Los Alamos National Lab.</p>
</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/R_(programming_language)" title="R (programming language)">R</a></th>
<td>Social network analysis within the versatile and popular R environment</td>
<td>R will read in almost any format data file</td>
<td>R has write capability for most data formats</td>
<td>Windows, Linux, Mac</td>
<td>Open source</td>
<td>R contains several packages relevant for social network analysis: <i>igraph</i> is a generic network analysis package; <i>sna</i> performs sociometric analysis of networks; <i>network</i> manipulates and displays network objects; <i>tnet</i> performs analysis of weighted networks, two-mode networks, and longitudinal networks; <i>ergm</i> is a set of tools to analyze and simulate networks based on exponential random graph models exponential random graph models; <i>Bergm</i> provides tools for Bayesian analysis for exponential random graph models, <i>hergm</i> implements hierarchical exponential random graph models; 'RSiena' allows the analyses of the evolution of social networks using dynamic actor-oriented models; <i>latentnet</i> has functions for network latent position and cluster models; <i>degreenet</i> provides tools for statistical modeling of network degree distributions; and <i>networksis</i> provides tools for simulating bipartite networks with fixed marginals.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a class="new" href="//en.wikipedia.org/w/index.php?title=SVAT&amp;action=edit&amp;redlink=1" title="SVAT (page does not exist)">SVAT</a></th>
<td>Visual analytics for investigation</td>
<td>GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), Pajek(.net, .gml), Sonivis(.graphml), Tulip(.tlp, .dot), UCINET(.dl), yEd(.gml), Gephi (.gexf), Edge list(.csv), databases - Oracle, MSSQL, PostgreSQL, MySQL, Webservices...</td>
<td>GUESS(.gdf), Gephi(.gexf), .svg, .png</td>
<td>Any system supporting Java 1.6 and OpenGL</td>
<td>Closed source modules, Open Source modules from Gephi</td>
<td>Commercial tool based on Gephi.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Tulip_(software)" title="Tulip (software)">Tulip</a></th>
<td>Social Network Analysis tool</td>
<td>Tulip format (.tlp), GraphViz (.dot), GML, txt, adjacency matrix</td>
<td>.tlp, .gml</td>
<td>Windows Vista, XP, 7/ Linux / Mac OS</td>
<td>LGPL</td>
<td>Tulip is an information visualization framework dedicated to the analysis and visualization of relational data. Tulip aims to provide the developer with a complete library, supporting the design of interactive information visualization applications for relational data that can be tailored to the problems he or she is addressing.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Visone_(software)" title="Visone (software)">visone</a></th>
<td>Visual Social Network Analyses and Exploration</td>
<td>many formats</td>
<td>many formats</td>
<td>Windows, Linux, Mac OS (Java based)</td>
<td>Free (also for commercial use)</td>
<td><a href="//en.wikipedia.org/wiki/Visone_(software)" title="Visone (software)">visone</a> is a software for the analysis and visualization of social networks. It is currently developed by Algorithmics group at the University of Konstanz.</td>
</tr>
<tr valign="top">
<th class="table-rh" style="background: #ececec; color: black; font-weight: bold; vertical-align: middle; text-align: left;"><a href="//en.wikipedia.org/wiki/Wolfram_Alpha" title="Wolfram Alpha">Wolfram Alpha</a></th>
<td>Graph analysis, time series analysis, categorical data analysis</td>
<td>Facebook API</td>
<td>Many formats</td>
<td>Web service</td>
<td>Free</td>
<td><a href="//en.wikipedia.org/wiki/Wolfram_Alpha" title="Wolfram Alpha">Wolfram Alpha</a> is a general computational knowledge engine answering queries on many knowledge domains. Give it the input "Facebook report" and it will answer queries on analysis of your social network data,<sup class="reference" id="cite_ref-10"><a href="#cite_note-10"><span>[</span>10<span>]</span></a></sup><sup class="reference" id="cite_ref-11"><a href="#cite_note-11"><span>[</span>11<span>]</span></a></sup></td>
</tr>
</table>
