row	col	id	content	tokens	lemmaTokens	posTags	nerTags	nerValues	number	date	num2	list	listId
-1	0	fb:row.row.iso_iec_standard	ISO/IEC Standard	iso\\/iec|standard	iso\\/iec|Standard	NN|NNP	O|O	|					
-1	1	fb:row.row.title	Title	title	title	NN	O						
-1	2	fb:row.row.status	Status	status	status	NN	O						
-1	3	fb:row.row.description	Description	description	description	NN	O						
-1	4	fb:row.row.wg	WG	wg	wg	NN	O						
0	0	fb:cell.iso_iec_tr_19759	ISO/IEC TR 19759	iso\\/iec|tr|19759	iso\\/iec|tr|19759	NN|NN|CD	O|O|DATE	||1975	19759.0			ISO|IEC TR 19759	fb:part.iso|fb:part.iec_tr_19759
0	1	fb:cell.software_engineering_guide_to_the_software_engineering_body_of_knowledge_swebok	Software Engineering – Guide to the Software Engineering Body of Knowledge (SWEBOK)	software|engineering|--|guide|to|the|software|engineering|body|of|knowledge|-lrb-|swebok|-rrb-	Software|Engineering|--|Guide|to|the|Software|Engineering|Body|of|Knowledge|-lrb-|SWEBOK|-rrb-	NNP|NNP|:|NNP|TO|DT|NNP|NNP|NNP|IN|NNP|-LRB-|NNP|-RRB-	O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||					
0	2	fb:cell.published_2005	Published (2005)	published|-lrb-|2005|-rrb-	publish|-lrb-|2005|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2005|	2005.0				
0	3	fb:cell.identifies_and_describes_the_subset_of_body_of_knowledge_of_software_engineering_that_is_generally_accepted	Identifies and describes the subset of body of knowledge of software engineering that is generally accepted	identifies|and|describes|the|subset|of|body|of|knowledge|of|software|engineering|that|is|generally|accepted	identify|and|describe|the|subset|of|body|of|knowledge|of|software|engineering|that|be|generally|accept	VBZ|CC|VBZ|DT|NN|IN|NN|IN|NN|IN|NN|NN|WDT|VBD-AUX|RB|VBN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||				Identifies and describes the subset of body of knowledge of software engineering that is generally accepted	fb:part.identifies_and_describes_the_subset_of_body_of_knowledge_of_software_engineering_that_is_generally_accepted
0	4	fb:cell.20	20	20	20	CD	NUMBER	20.0	20.0				
1	0	fb:cell.iso_iec_15288	ISO/IEC 15288	iso\\/iec|15288	iso\\/iec|15288	NN|CD	O|NUMBER	|15288.0	15288.0			ISO|IEC 15288	fb:part.iso|fb:part.iec_15288
1	1	fb:cell.systems_and_software_engineering_system_life_cycle_processes	Systems and software engineering – System life cycle processes	systems|and|software|engineering|--|system|life|cycle|processes	Systems|and|software|engineering|--|System|life|cycle|process	NNPS|CC|NN|NN|:|NNP|NN|NN|NNS	O|O|O|O|O|O|O|O|O	||||||||					
1	2	fb:cell.published_2008	Published (2008)	published|-lrb-|2008|-rrb-	publish|-lrb-|2008|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2008|	2008.0				
1	3	fb:cell.establishes_a_common_framework_for_describing_the_life_cycle_of_systems_created_by_humans_and_defines_a_set_of_processes_and_associated_terminology	Establishes a common framework for describing the life cycle of systems created by humans and defines a set of processes and associated terminology	establishes|a|common|framework|for|describing|the|life|cycle|of|systems|created|by|humans|and|defines|a|set|of|processes|and|associated|terminology	establish|a|common|framework|for|describe|the|life|cycle|of|system|create|by|human|and|define|a|set|of|process|and|associate|terminology	VBZ|DT|JJ|NN|IN|VBG|DT|NN|NN|IN|NNS|VBN|IN|NNS|CC|VBZ|DT|NN|IN|NNS|CC|VBN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||				Establishes a common framework for describing the life cycle of systems created by humans and defines a set of processes and associated terminology	fb:part.establishes_a_common_framework_for_describing_the_life_cycle_of_systems_created_by_humans_and_defines_a_set_of_processes_and_associated_terminology
1	4	fb:cell.7	7	7	7	CD	NUMBER	7.0	7.0				
2	0	fb:cell.iso_iec_12207	ISO/IEC 12207	iso\\/iec|12207	iso\\/iec|12207	NN|CD	O|NUMBER	|12207.0	12207.0			ISO|IEC 12207	fb:part.iso|fb:part.iec_12207
2	1	fb:cell.systems_and_software_engineering_software_life_cycle_processes	Systems and software engineering – Software life cycle processes	systems|and|software|engineering|--|software|life|cycle|processes	Systems|and|software|engineering|--|Software|life|cycle|process	NNPS|CC|NN|NN|:|NNP|NN|NN|NNS	O|O|O|O|O|O|O|O|O	||||||||					
2	2	fb:cell.published_2008	Published (2008)	published|-lrb-|2008|-rrb-	publish|-lrb-|2008|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2008|	2008.0				
2	3	fb:cell.establishes_a_common_framework_for_software_life_cycle_processes_with_well_defined_terminology	Establishes a common framework for software life cycle processes with well-defined terminology	establishes|a|common|framework|for|software|life|cycle|processes|with|well|defined|terminology	establish|a|common|framework|for|software|life|cycle|process|with|well|define|terminology	VBZ|DT|JJ|NN|IN|NN|NN|NN|NNS|IN|RB|VBN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||				Establishes a common framework for software life cycle processes with well-defined terminology	fb:part.establishes_a_common_framework_for_software_life_cycle_processes_with_well_defined_terminology
2	4	fb:cell.7	7	7	7	CD	NUMBER	7.0	7.0				
3	0	fb:cell.iso_iec_20000_1	ISO/IEC 20000-1	iso\\/iec|20000-1	iso\\/iec|20000-1	NN|CD	O|NUMBER	|20000.0 - 1.0	20000.0		1.0	ISO|IEC 20000-1	fb:part.iso|fb:part.iec_20000_1
3	1	fb:cell.information_technology_service_management_part_1_service_management_system_requirements	Information technology – Service management – Part 1: Service management system requirements	information|technology|--|service|management|--|part|1|:|service|management|system|requirements	Information|technology|--|Service|management|--|Part|1|:|Service|management|system|requirement	NNP|NN|:|NNP|NN|:|NNP|CD|:|NNP|NN|NN|NNS	O|O|O|O|O|O|O|NUMBER|O|O|O|O|O	|||||||1.0|||||	1.0				
3	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
3	3	fb:cell.specifies_requirements_for_the_service_provider_to_plan_establish_implement_operate_monitor_review_maintain_and_improve_a_service_management_system_sms	Specifies requirements for the service provider to plan, establish, implement, operate, monitor, review, maintain, and improve a service management system (SMS)	specifies|requirements|for|the|service|provider|to|plan|,|establish|,|implement|,|operate|,|monitor|,|review|,|maintain|,|and|improve|a|service|management|system|-lrb-|sms|-rrb-	specify|requirement|for|the|service|provider|to|plan|,|establish|,|implement|,|operate|,|monitor|,|review|,|maintain|,|and|improve|a|service|management|system|-lrb-|sm|-rrb-	VBZ|NNS|IN|DT|NN|NN|TO|NN|,|VB|,|VB|,|VBP|,|VBP|,|VBP|,|VBP|,|CC|VB|DT|NN|NN|NN|-LRB-|NN|-RRB-	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||				Specifies requirements for the service provider to plan|establish|implement|operate|monitor|review|maintain|and improve a service management system (SMS)	fb:part.specifies_requirements_for_the_service_provider_to_plan|fb:part.establish|fb:part.implement|fb:part.operate|fb:part.monitor|fb:part.review|fb:part.maintain|fb:part.and_improve_a_service_management_system_sms
3	4	fb:cell.25	25	25	25	CD	NUMBER	25.0	25.0				
4	0	fb:cell.iso_iec_15504_1	ISO/IEC 15504-1	iso\\/iec|15504-1	iso\\/iec|15504-1	NN|CD	O|NUMBER	|15504.0 - 1.0	15504.0		1.0	ISO|IEC 15504-1	fb:part.iso|fb:part.iec_15504_1
4	1	fb:cell.information_technology_process_assessment_part_1_concepts_and_vocabulary	Information technology – Process assessment – Part 1: Concepts and vocabulary	information|technology|--|process|assessment|--|part|1|:|concepts|and|vocabulary	Information|technology|--|process|assessment|--|Part|1|:|concept|and|vocabulary	NNP|NN|:|VB|NN|:|NNP|CD|:|NNS|CC|NN	O|O|O|O|O|O|O|NUMBER|O|O|O|O	|||||||1.0||||	1.0				
4	2	fb:cell.published_2004	Published (2004)	published|-lrb-|2004|-rrb-	publish|-lrb-|2004|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2004|	2004.0				
4	3	fb:cell.provides_overall_information_on_the_concepts_of_process_assessment_and_its_use_in_the_two_contexts_of_process_improvement_and_process_capability_determination	Provides overall information on the concepts of process assessment and its use in the two contexts of process improvement and process capability determination	provides|overall|information|on|the|concepts|of|process|assessment|and|its|use|in|the|two|contexts|of|process|improvement|and|process|capability|determination	provide|overall|information|on|the|concept|of|process|assessment|and|its|use|in|the|two|context|of|process|improvement|and|process|capability|determination	VBZ|JJ|NN|IN|DT|NNS|IN|NN|NN|CC|PRP$|NN|IN|DT|CD|NNS|IN|NN|NN|CC|NN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O	||||||||||||||2.0||||||||				Provides overall information on the concepts of process assessment and its use in the two contexts of process improvement and process capability determination	fb:part.provides_overall_information_on_the_concepts_of_process_assessment_and_its_use_in_the_two_contexts_of_process_improvement_and_process_capability_determination
4	4	fb:cell.10	10	10	10	CD	NUMBER	10.0	10.0				
5	0	fb:cell.iso_iec_ieee_42010	ISO/IEC/IEEE 42010	iso\\/iec\\/ieee|42010	iso\\/iec\\/ieee|42010	NN|CD	O|NUMBER	|42010.0	42010.0			ISO|IEC|IEEE 42010	fb:part.iso|fb:part.iec|fb:part.ieee_42010
5	1	fb:cell.systems_and_software_engineering_architecture_description	Systems and software engineering – Architecture description	systems|and|software|engineering|--|architecture|description	Systems|and|software|engineering|--|Architecture|description	NNPS|CC|NN|NN|:|NNP|NN	O|O|O|O|O|O|O	||||||					
5	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
5	3	fb:cell.addresses_the_creation_analysis_and_sustainment_of_architectures_of_systems_through_the_use_of_architecture_descriptions	Addresses the creation, analysis, and sustainment of architectures of systems through the use of architecture descriptions	addresses|the|creation|,|analysis|,|and|sustainment|of|architectures|of|systems|through|the|use|of|architecture|descriptions	address|the|creation|,|analysis|,|and|sustainment|of|architecture|of|system|through|the|use|of|architecture|description	NNS|DT|NN|,|NN|,|CC|NN|IN|NNS|IN|NNS|IN|DT|NN|IN|NN|NNS	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||				Addresses the creation|analysis|and sustainment of architectures of systems through the use of architecture descriptions	fb:part.addresses_the_creation|fb:part.analysis|fb:part.and_sustainment_of_architectures_of_systems_through_the_use_of_architecture_descriptions
5	4	fb:cell.42	42	42	42	CD	NUMBER	42.0	42.0				
6	0	fb:cell.iso_iec_tr_29110_1	ISO/IEC TR 29110-1	iso\\/iec|tr|29110-1	iso\\/iec|tr|29110-1	NN|NN|CD	O|O|NUMBER	||29110.0 - 1.0	29110.0		1.0	ISO|IEC TR 29110-1	fb:part.iso|fb:part.iec_tr_29110_1
6	1	fb:cell.software_engineering_lifecycle_profiles_for_very_small_entities_vses_part_1_overview	Software engineering – Lifecycle profiles for Very Small Entities (VSEs) – Part 1: Overview	software|engineering|--|lifecycle|profiles|for|very|small|entities|-lrb-|vses|-rrb-|--|part|1|:|overview	Software|engineering|--|lifecycle|profile|for|very|small|entity|-lrb-|vs|-rrb-|--|Part|1|:|overview	NNP|NN|:|NN|NNS|IN|RB|JJ|NNS|-LRB-|NNS|-RRB-|:|NNP|CD|:|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O	||||||||||||||1.0||	1.0				
6	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
6	3	fb:cell.introduces_the_characteristics_and_requirements_of_a_vse_and_clarifies_the_rationale_for_vse_specific_profiles_documents_standards_and_guides	Introduces the characteristics and requirements of a VSE and clarifies the rationale for VSE-specific profiles, documents, standards, and guides	introduces|the|characteristics|and|requirements|of|a|vse|and|clarifies|the|rationale|for|vse|specific|profiles|,|documents|,|standards|,|and|guides	introduce|the|characteristic|and|requirement|of|a|vse|and|clarify|the|rationale|for|VSE|specific|profile|,|document|,|standard|,|and|guide	VBZ|DT|NNS|CC|NNS|IN|DT|NN|CC|VBZ|DT|NN|IN|NNP|JJ|NNS|,|NNS|,|NNS|,|CC|NNS	O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||				Introduces the characteristics and requirements of a VSE and clarifies the rationale for VSE-specific profiles|documents|standards|and guides	fb:part.introduces_the_characteristics_and_requirements_of_a_vse_and_clarifies_the_rationale_for_vse_specific_profiles|fb:part.documents|fb:part.standards|fb:part.and_guides
6	4	fb:cell.24	24	24	24	CD	NUMBER	24.0	24.0				
7	0	fb:cell.iso_iec_tr_9126_2	ISO/IEC TR 9126-2	iso\\/iec|tr|9126-2	iso\\/iec|tr|9126-2	NN|NN|CD	O|O|NUMBER	||9126.0 - 2.0	9126.0		2.0	ISO|IEC TR 9126-2	fb:part.iso|fb:part.iec_tr_9126_2
7	1	fb:cell.software_engineering_product_quality_part_2_external_metrics	Software engineering – Product quality – Part 2: External metrics	software|engineering|--|product|quality|--|part|2|:|external|metrics	Software|engineering|--|Product|quality|--|Part|2|:|external|metric	NNP|NN|:|NNP|NN|:|NNP|CD|:|JJ|NNS	O|O|O|O|O|O|O|NUMBER|O|O|O	|||||||2.0|||	2.0				
7	2	fb:cell.published_2003	Published (2003)	published|-lrb-|2003|-rrb-	publish|-lrb-|2003|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2003|	2003.0				
7	3	fb:cell.provides_external_metrics_for_measuring_attributes_of_six_external_quality_characteristics_defined_in_iso_iec_9126_1	Provides external metrics for measuring attributes of six external quality characteristics defined in ISO/IEC 9126-1	provides|external|metrics|for|measuring|attributes|of|six|external|quality|characteristics|defined|in|iso\\/iec|9126-1	provide|external|metric|for|measure|attribute|of|six|external|quality|characteristic|define|in|iso\\/iec|9126-1	VBZ|JJ|NNS|IN|VBG|NNS|IN|CD|JJ|NN|NNS|VBN|IN|NN|CD	O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|NUMBER	|||||||6.0|||||||9126.0 - 1.0	9126.0		1.0	Provides external metrics for measuring attributes of six external quality characteristics defined in ISO|IEC 9126-1	fb:part.provides_external_metrics_for_measuring_attributes_of_six_external_quality_characteristics_defined_in_iso|fb:part.iec_9126_1
7	4	fb:cell.null											
8	0	fb:cell.iso_iec_10746_1	ISO/IEC 10746-1	iso\\/iec|10746-1	iso\\/iec|10746-1	NN|CD	O|NUMBER	|10746.0 - 1.0	10746.0		1.0	ISO|IEC 10746-1	fb:part.iso|fb:part.iec_10746_1
8	1	fb:cell.information_technology_open_distributed_processing_reference_model_overview	Information technology – Open Distributed Processing – Reference model: Overview	information|technology|--|open|distributed|processing|--|reference|model|:|overview	Information|technology|--|Open|distribute|Processing|--|Reference|model|:|overview	NNP|NN|:|NNP|VBD|NNP|:|NNP|NN|:|NN	O|O|O|O|O|O|O|O|O|O|O	||||||||||					
8	2	fb:cell.published_1998	Published (1998)	published|-lrb-|1998|-rrb-	publish|-lrb-|1998|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||1998|	1998.0				
8	3	fb:cell.provides_an_introduction_and_motivation_for_odp_an_overview_of_the_reference_model_of_open_distributed_processing_rm_odp_and_an_explanation_of_its_key_concepts_gives_guidance_on_the_application_of_rm_odp	Provides:\n\nAn introduction and motivation for ODP\nAn overview of the Reference Model of Open Distributed Processing (RM-ODP) and an explanation of its key concepts\nGives guidance on the application of RM-ODP	provides|:|an|introduction|and|motivation|for|odp|an|overview|of|the|reference|model|of|open|distributed|processing|-lrb-|rm|odp|-rrb-|and|an|explanation|of|its|key|concepts|gives|guidance|on|the|application|of|rm|odp	provide|:|a|introduction|and|motivation|for|odp|a|overview|of|the|Reference|Model|of|Open|distribute|processing|-lrb-|rm|odp|-rrb-|and|a|explanation|of|its|key|concept|give|guidance|on|the|application|of|rm|odp	VBZ|:|DT|NN|CC|NN|IN|NN|DT|NN|IN|DT|NNP|NNP|IN|NNP|VBD|NN|-LRB-|NN|NN|-RRB-|CC|DT|NN|IN|PRP$|JJ|NNS|VBZ|NN|IN|DT|NN|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|MISC|MISC|MISC|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||				Provides:|An introduction and motivation for ODP|An overview of the Reference Model of Open Distributed Processing (RM-ODP) and an explanation of its key concepts|Gives guidance on the application of RM-ODP	fb:part.provides|fb:part.an_introduction_and_motivation_for_odp|fb:part.an_overview_of_the_reference_model_of_open_distributed_processing_rm_odp_and_an_explanation_of_its_key_concepts|fb:part.gives_guidance_on_the_application_of_rm_odp
8	4	fb:cell.19	19	19	19	CD	NUMBER	19.0	19.0				
9	0	fb:cell.iso_iec_19770_1	ISO/IEC 19770-1	iso\\/iec|19770-1	iso\\/iec|19770-1	NN|CD	O|NUMBER	|19770.0 - 1.0	19770.0		1.0	ISO|IEC 19770-1	fb:part.iso|fb:part.iec_19770_1
9	1	fb:cell.information_technology_software_asset_management_part_1_processes_and_tiered_assessment_of_conformance	Information technology – Software asset management – Part 1: Processes and tiered assessment of conformance	information|technology|--|software|asset|management|--|part|1|:|processes|and|tiered|assessment|of|conformance	Information|technology|--|Software|asset|management|--|Part|1|:|process|and|tiered|assessment|of|conformance	NNP|NN|:|NNP|NN|NN|:|NNP|CD|:|NNS|CC|JJ|NN|IN|NN	O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O	||||||||1.0|||||||	1.0				
9	2	fb:cell.published_2012	Published (2012)	published|-lrb-|2012|-rrb-	publish|-lrb-|2012|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2012|	2012.0				
9	3	fb:cell.establishes_a_baseline_for_an_integrated_set_of_processes_for_software_assessment_management_sam_divided_into_tiers_to_allow_for_incremental_implementation_assessment_and_recognition	Establishes a baseline for an integrated set of processes for Software Assessment Management (SAM), divided into tiers to allow for incremental implementation, assessment, and recognition	establishes|a|baseline|for|an|integrated|set|of|processes|for|software|assessment|management|-lrb-|sam|-rrb-|,|divided|into|tiers|to|allow|for|incremental|implementation|,|assessment|,|and|recognition	establish|a|baseline|for|a|integrate|set|of|process|for|Software|Assessment|Management|-lrb-|SAM|-rrb-|,|divide|into|tier|to|allow|for|incremental|implementation|,|assessment|,|and|recognition	VBZ|DT|NN|IN|DT|VBN|NN|IN|NNS|IN|NNP|NNP|NNP|-LRB-|NNP|-RRB-|,|VBN|IN|NNS|TO|VB|IN|JJ|NN|,|NN|,|CC|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||				Establishes a baseline for an integrated set of processes for Software Assessment Management (SAM)|divided into tiers to allow for incremental implementation|assessment|and recognition	fb:part.establishes_a_baseline_for_an_integrated_set_of_processes_for_software_assessment_management_sam|fb:part.divided_into_tiers_to_allow_for_incremental_implementation|fb:part.assessment|fb:part.and_recognition
9	4	fb:cell.21	21	21	21	CD	NUMBER	21.0	21.0				
10	0	fb:cell.iso_iec_ieee_26511	ISO/IEC/IEEE 26511	iso\\/iec\\/ieee|26511	iso\\/iec\\/ieee|26511	NN|CD	O|NUMBER	|26511.0	26511.0			ISO|IEC|IEEE 26511	fb:part.iso|fb:part.iec|fb:part.ieee_26511
10	1	fb:cell.systems_and_software_engineering_requirements_for_managers_of_user_documentation	Systems and software engineering — Requirements for managers of user documentation	systems|and|software|engineering|--|requirements|for|managers|of|user|documentation	Systems|and|software|engineering|--|requirement|for|manager|of|user|documentation	NNPS|CC|NN|NN|:|NNS|IN|NNS|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O	||||||||||					
10	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
10	3	fb:cell.specifies_procedures_for_managing_user_documentation_throughout_the_software_life_cycle	Specifies procedures for managing user documentation throughout the software life cycle.	specifies|procedures|for|managing|user|documentation|throughout|the|software|life|cycle|.	specify|procedure|for|manage|user|documentation|throughout|the|software|life|cycle|.	VBZ|NNS|IN|VBG|NN|NN|IN|DT|NN|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||				Specifies procedures for managing user documentation throughout the software life cycle.	fb:part.specifies_procedures_for_managing_user_documentation_throughout_the_software_life_cycle
10	4	fb:cell.2	2	2	2	CD	NUMBER	2.0	2.0				
11	0	fb:cell.iso_iec_ieee_26512	ISO/IEC/IEEE 26512	iso\\/iec\\/ieee|26512	iso\\/iec\\/ieee|26512	NN|CD	O|NUMBER	|26512.0	26512.0			ISO|IEC|IEEE 26512	fb:part.iso|fb:part.iec|fb:part.ieee_26512
11	1	fb:cell.systems_and_software_engineering_requirements_for_acquirers_and_suppliers_of_user_documentation	Systems and software engineering -- Requirements for acquirers and suppliers of user documentation	systems|and|software|engineering|--|requirements|for|acquirers|and|suppliers|of|user|documentation	Systems|and|software|engineering|--|requirement|for|acquirer|and|supplier|of|user|documentation	NNPS|CC|NN|NN|:|NNS|IN|NNS|CC|NNS|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||					
11	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
11	3	fb:cell.defines_the_documentation_process_from_the_acquirer_s_standpoint_and_the_supplier_s_standpoint	Defines the documentation process from the acquirer's standpoint and the supplier's standpoint.	defines|the|documentation|process|from|the|acquirer|'s|standpoint|and|the|supplier|'s|standpoint|.	define|the|documentation|process|from|the|acquirer|'s|standpoint|and|the|supplier|'s|standpoint|.	VBZ|DT|NN|NN|IN|DT|NN|POS|NN|CC|DT|NN|POS|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||				Defines the documentation process from the acquirer's standpoint and the supplier's standpoint.	fb:part.defines_the_documentation_process_from_the_acquirer_s_standpoint_and_the_supplier_s_standpoint
11	4	fb:cell.2	2	2	2	CD	NUMBER	2.0	2.0				
12	0	fb:cell.iso_iec_ieee_26513	ISO/IEC/IEEE 26513	iso\\/iec\\/ieee|26513	iso\\/iec\\/ieee|26513	NN|CD	O|NUMBER	|26513.0	26513.0			ISO|IEC|IEEE 26513	fb:part.iso|fb:part.iec|fb:part.ieee_26513
12	1	fb:cell.systems_and_software_engineering_requirements_for_testers_and_reviewers_of_user_documentation	Systems and software engineering — Requirements for testers and reviewers of user documentation	systems|and|software|engineering|--|requirements|for|testers|and|reviewers|of|user|documentation	Systems|and|software|engineering|--|requirement|for|tester|and|reviewer|of|user|documentation	NNPS|CC|NN|NN|:|NNS|IN|NNS|CC|NNS|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||					
12	2	fb:cell.published_2009	Published (2009)	published|-lrb-|2009|-rrb-	publish|-lrb-|2009|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2009|	2009.0				
12	3	fb:cell.defines_the_process_in_which_user_documentation_products_are_tested	Defines the process in which user documentation products are tested.	defines|the|process|in|which|user|documentation|products|are|tested|.	define|the|process|in|which|user|documentation|product|be|test|.	VBZ|DT|NN|IN|WDT|NN|NN|NNS|VBD-AUX|VBN|.	O|O|O|O|O|O|O|O|O|O|O	||||||||||				Defines the process in which user documentation products are tested.	fb:part.defines_the_process_in_which_user_documentation_products_are_tested
12	4	fb:cell.2	2	2	2	CD	NUMBER	2.0	2.0				
13	0	fb:cell.iso_iec_ieee_26514	ISO/IEC/IEEE 26514	iso\\/iec\\/ieee|26514	iso\\/iec\\/ieee|26514	NN|CD	O|NUMBER	|26514.0	26514.0			ISO|IEC|IEEE 26514	fb:part.iso|fb:part.iec|fb:part.ieee_26514
13	1	fb:cell.systems_and_software_engineering_requirements_for_designers_and_developers_of_user_documentation	Systems and software engineering — Requirements for designers and developers of user documentation	systems|and|software|engineering|--|requirements|for|designers|and|developers|of|user|documentation	Systems|and|software|engineering|--|requirement|for|designer|and|developer|of|user|documentation	NNPS|CC|NN|NN|:|NNS|IN|NNS|CC|NNS|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||					
13	2	fb:cell.published_2008	Published (2008)	published|-lrb-|2008|-rrb-	publish|-lrb-|2008|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2008|	2008.0				
13	3	fb:cell.specifies_the_structure_content_and_format_for_user_documentation_and_provides_informative_guidance_for_user_documentation_style	Specifies the structure, content, and format for user documentation, and provides informative guidance for user documentation style.	specifies|the|structure|,|content|,|and|format|for|user|documentation|,|and|provides|informative|guidance|for|user|documentation|style|.	specify|the|structure|,|content|,|and|format|for|user|documentation|,|and|provide|informative|guidance|for|user|documentation|style|.	VBZ|DT|NN|,|NN|,|CC|NN|IN|NN|NN|,|CC|VBZ|JJ|NN|IN|NN|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||				Specifies the structure|content|and format for user documentation|and provides informative guidance for user documentation style.	fb:part.specifies_the_structure|fb:part.content|fb:part.and_format_for_user_documentation|fb:part.and_provides_informative_guidance_for_user_documentation_style
13	4	fb:cell.2	2	2	2	CD	NUMBER	2.0	2.0				
14	0	fb:cell.iso_iec_ieee_26515	ISO/IEC/IEEE 26515	iso\\/iec\\/ieee|26515	iso\\/iec\\/ieee|26515	NN|CD	O|NUMBER	|26515.0	26515.0			ISO|IEC|IEEE 26515	fb:part.iso|fb:part.iec|fb:part.ieee_26515
14	1	fb:cell.systems_and_software_engineering_developing_user_documentation_in_an_agile_environment	Systems and software engineering — Developing user documentation in an agile environment	systems|and|software|engineering|--|developing|user|documentation|in|an|agile|environment	Systems|and|software|engineering|--|developing|user|documentation|in|a|agile|environment	NNPS|CC|NN|NN|:|JJ|NN|NN|IN|DT|JJ|NN	O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||					
14	2	fb:cell.published_2011	Published (2011)	published|-lrb-|2011|-rrb-	publish|-lrb-|2011|-rrb-	VBN|-LRB-|CD|-RRB-	O|O|DATE|O	||2011|	2011.0				
14	3	fb:cell.specifies_the_way_in_which_user_documentation_can_be_developed_in_agile_development_projects	Specifies the way in which user documentation can be developed in agile development projects.	specifies|the|way|in|which|user|documentation|can|be|developed|in|agile|development|projects|.	specify|the|way|in|which|user|documentation|can|be|develop|in|agile|development|project|.	VBZ|DT|NN|IN|WDT|NN|NN|VBD-AUX|VBD-AUX|VBN|IN|JJ|NN|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||				Specifies the way in which user documentation can be developed in agile development projects.	fb:part.specifies_the_way_in_which_user_documentation_can_be_developed_in_agile_development_projects
14	4	fb:cell.2	2	2	2	CD	NUMBER	2.0	2.0				
