row	col	id	content	tokens	lemmaTokens	posTags	nerTags	nerValues	number	date	num2	list	listId
-1	0	fb:row.row.model	Model	model	Model	NNP	O						
-1	1	fb:row.row.released	Released	released	release	VBN	O						
-1	2	fb:row.row.usage	Usage	usage	usage	NN	O						
-1	3	fb:row.row.features	Features	features	feature	NNS	O						
-1	4	fb:row.row.storage	Storage	storage	Storage	NNP	O						
0	0	fb:cell.betsie_dottie	Betsie/Dottie	betsie\\/dottie	betsie\\/dottie	NN	O					Betsie|Dottie	fb:part.betsie|fb:part.dottie
0	1	fb:cell.unknown	Unknown	unknown	unknown	JJ	O						
0	2	fb:cell.used_for_calculating_odds_for_bookmakers	Used for calculating odds for bookmakers	used|for|calculating|odds|for|bookmakers	use|for|calculate|odds|for|bookmaker	VBN|IN|VBG|NNS|IN|NNS	O|O|O|O|O|O	|||||				Used for calculating odds for bookmakers	fb:part.used_for_calculating_odds_for_bookmakers
0	3	fb:cell.null											fb:part.null
0	4	fb:cell.null											fb:part.null
1	0	fb:cell.sadie	SADIE	sadie	SADIE	NNP	PERSON					SADIE	fb:part.sadie
1	1	fb:cell.1966	1966	1966	1966	CD	DATE	1966	1966.0	1966-xx-xx			
1	2	fb:cell.sterling_and_decimal_invoicing_electronically	Sterling And Decimal Invoicing Electronically	sterling|and|decimal|invoicing|electronically	Sterling|and|Decimal|Invoicing|electronically	NNP|CC|NNP|NNP|RB	O|O|O|O|O	||||				Sterling And Decimal Invoicing Electronically	fb:part.sterling_and_decimal_invoicing_electronically
1	3	fb:cell.programming_stored_on_soldered_through_connectors_on_double_sided_printed_circuit_cards_connecting_instruction_type_on_one_side_of_the_card_with_instruction_number_on_the_other_side_ibm_2741_selectric_style_golf_ball_teleprinter_for_user_interface	Programming stored on soldered-through connectors on double-sided printed circuit cards, connecting instruction type on one side of the card with instruction number on the other side. IBM 2741 Selectric-style golf-ball teleprinter for user interface.	programming|stored|on|soldered|through|connectors|on|double|sided|printed|circuit|cards|,|connecting|instruction|type|on|one|side|of|the|card|with|instruction|number|on|the|other|side|.|ibm|2741|selectric|style|golf|ball|teleprinter|for|user|interface|.	program|store|on|solder|through|connector|on|double|side|print|circuit|card|,|connect|instruction|type|on|one|side|of|the|card|with|instruction|number|on|the|other|side|.|IBM|2741|Selectric|style|golf|ball|teleprinter|for|user|interface|.	VBG|VBN|IN|VBN|IN|NNS|IN|JJ|VBD|VBN|NN|NNS|,|VBG|NN|NN|IN|CD|NN|IN|DT|NN|IN|NN|NN|IN|DT|JJ|NN|.|NNP|CD|NNP|NN|NN|NN|NN|IN|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|DATE|MISC|O|O|O|O|O|O|O|O	|||||||||||||||||1.0||||||||||||||2741|||||||||				Programming stored on soldered-through connectors on double-sided printed circuit cards|connecting instruction type on one side of the card with instruction number on the other side. IBM 2741 Selectric-style golf-ball teleprinter for user interface.	fb:part.programming_stored_on_soldered_through_connectors_on_double_sided_printed_circuit_cards|fb:part.connecting_instruction_type_on_one_side_of_the_card_with_instruction_number_on_the_other_side_ibm_2741_selectric_style_golf_ball_teleprinter_for_user_interface
1	4	fb:cell.nickel_acoustic_delay_line_working_storage_8_words_of_decimal_arithmetic_paper_tape_and_edge_punched_cards_magnetic_stripe_cards_for_the_teleprinter_style_user_interface_could_be_switched_between_decimal_and_sterling_at_the_flick_of_a_switch_as_it_said_in_the_publicity	Nickel acoustic delay line working storage (8 words of decimal arithmetic). Paper tape and edge-punched cards. Magnetic stripe cards for the teleprinter-style user interface. Could be switched between decimal and Sterling at the flick of a switch as it said in the publicity.	nickel|acoustic|delay|line|working|storage|-lrb-|8|words|of|decimal|arithmetic|-rrb-|.|paper|tape|and|edge|punched|cards|.|magnetic|stripe|cards|for|the|teleprinter|style|user|interface|.|could|be|switched|between|decimal|and|sterling|at|the|flick|of|a|switch|as|it|said|in|the|publicity|.	nickel|acoustic|delay|line|work|storage|-lrb-|8|word|of|decimal|arithmetic|-rrb-|.|Paper|tape|and|edge|punch|card|.|magnetic|stripe|card|for|the|teleprinter|style|user|interface|.|could|be|switch|between|decimal|and|sterling|at|the|flick|of|a|switch|as|it|say|in|the|publicity|.	NN|JJ|NN|NN|VBG|NN|-LRB-|CD|NNS|IN|JJ|NN|-RRB-|.|NNP|NN|CC|NN|VBD|NNS|.|JJ|NN|NNS|IN|DT|NN|NN|NN|NN|.|VBD-AUX|VBD-AUX|VBN|IN|JJ|CC|NN|IN|DT|NN|IN|DT|NN|IN|PRP|VBD|IN|DT|NN|.	O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||8.0|||||||||||||||||||||||||||||||||||||||||||	8.0			Nickel acoustic delay line working storage (8 words of decimal arithmetic). Paper tape and edge-punched cards. Magnetic stripe cards for the teleprinter-style user interface. Could be switched between decimal and Sterling at the flick of a switch as it said in the publicity.	fb:part.nickel_acoustic_delay_line_working_storage_8_words_of_decimal_arithmetic_paper_tape_and_edge_punched_cards_magnetic_stripe_cards_for_the_teleprinter_style_user_interface_could_be_switched_between_decimal_and_sterling_at_the_flick_of_a_switch_as_it_said_in_the_publicity
2	0	fb:cell.susie	SUSIE	susie	SUSIE	NNP	PERSON					SUSIE	fb:part.susie
2	1	fb:cell.1967	1967	1967	1967	CD	DATE	1967	1967.0	1967-xx-xx			
2	2	fb:cell.stock_updating_and_sales_invoicing_electronically	Stock Updating and Sales Invoicing Electronically	stock|updating|and|sales|invoicing|electronically	Stock|Updating|and|sale|Invoicing|electronically	NNP|NNP|CC|NNS|NNP|RB	O|O|O|O|O|O	|||||				Stock Updating and Sales Invoicing Electronically	fb:part.stock_updating_and_sales_invoicing_electronically
2	3	fb:cell.programmes_2_stored_on_drum_each_with_1000_machine_instructions_programme_could_be_loaded_from_or_written_out_to_paper_tape_user_interface_as_sadie	Programmes (2) stored on drum, each with 1000 machine instructions. Programme could be loaded from or written out to paper tape. User interface as SADIE.	programmes|-lrb-|2|-rrb-|stored|on|drum|,|each|with|1000|machine|instructions|.|programme|could|be|loaded|from|or|written|out|to|paper|tape|.|user|interface|as|sadie|.	programme|-lrb-|2|-rrb-|store|on|drum|,|each|with|1000|machine|instruction|.|programme|could|be|load|from|or|write|out|to|paper|tape|.|user|interface|as|SADIE|.	NNS|-LRB-|CD|-RRB-|VBN|IN|VB|,|DT|IN|CD|NN|NNS|.|NN|VBD-AUX|VBD-AUX|VBN|IN|CC|VBN|RP|TO|NN|NN|.|NN|NN|IN|NNP|.	O|O|NUMBER|O|O|O|O|O|O|O|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O	||2.0||||||||1000||||||||||||||||||||	2.0		1000.0	Programmes (2) stored on drum|each with 1000 machine instructions. Programme could be loaded from or written out to paper tape. User interface as SADIE.	fb:part.programmes_2_stored_on_drum|fb:part.each_with_1000_machine_instructions_programme_could_be_loaded_from_or_written_out_to_paper_tape_user_interface_as_sadie
2	4	fb:cell.as_for_sadie_plus_magnetic_drum_6k_words	As for SADIE, plus magnetic drum (6k words)	as|for|sadie|,|plus|magnetic|drum|-lrb-|6k|words|-rrb-	as|for|SADIE|,|plus|magnetic|drum|-lrb-|6k|word|-rrb-	IN|IN|NNP|,|CC|JJ|VBP|-LRB-|JJ|NNS|-RRB-	O|O|LOCATION|O|O|O|O|O|O|O|O	||||||||||	6.0			As for SADIE|plus magnetic drum (6k words)	fb:part.as_for_sadie|fb:part.plus_magnetic_drum_6k_words
3	0	fb:cell.mark_1	Mark 1	mark|1	Mark|1	NNP|CD	O|NUMBER	|1.0	1.0			Mark 1	fb:part.mark_1
3	1	fb:cell.1970	1970	1970	1970	CD	DATE	1970	1970.0	1970-xx-xx			
3	2	fb:cell.sales_order_processing	Sales Order processing	sales|order|processing	sale|order|processing	NNS|NN|NN	O|O|O	||				Sales Order processing	fb:part.sales_order_processing
3	3	fb:cell.architecture_similar_to_the_data_general_nova_in_that_the_memory_and_i_o_highways_were_separate_whereas_dec_used_a_common_highway_for_memory_and_i_o_with_the_peripherals_occupying_memory_addresses_the_nova_was_a_16_bit_design_but_the_molecular_was_18_bit_allowing_signed_arithmetic_and_a_memory_parity_check_bit_the_instruction_set_was_in_some_ways_similar_to_the_nova_the_m18_was_designed_by_a_small_team_headed_by_colin_chapman_ian_miller_operating_system_known_as_los_developed_by_joe_templeman_in_leicester_office_of_systemation_programming_was_done_in_octal_notation_machine_code_in_longhand_using_coding_sheets_the_17_bit_word_allowed_the_limited_storage_of_3_bytes_per_word_using_metacode	Architecture similar to the Data General "NOVA" in that the memory and I/O highways were separate whereas DEC used a common highway for memory and I/O with the peripherals occupying memory addresses. The NOVA was a 16 bit design but the Molecular was 18 bit allowing signed arithmetic and a memory parity check bit. The instruction set was in some ways similar to the Nova. The M18 was designed by a small team headed by Colin Chapman & Ian Miller.\nOperating system known as LOS developed by Joe Templeman in Leicester office of Systemation. Programming was done in octal notation machine code in longhand using coding sheets. The 17 bit word allowed the limited storage of 3 bytes per word using "Metacode"	architecture|similar|to|the|data|general|``|nova|''|in|that|the|memory|and|i\\/o|highways|were|separate|whereas|dec|used|a|common|highway|for|memory|and|i\\/o|with|the|peripherals|occupying|memory|addresses|.|the|nova|was|a|16|bit|design|but|the|molecular|was|18|bit|allowing|signed|arithmetic|and|a|memory|parity|check|bit|.|the|instruction|set|was|in|some|ways|similar|to|the|nova|.|the|m18|was|designed|by|a|small|team|headed|by|colin|chapman|&|ian|miller|.|operating|system|known|as|los|developed|by|joe|templeman|in|leicester|office|of|systemation|.|programming|was|done|in|octal|notation|machine|code|in|longhand|using|coding|sheets|.|the|17|bit|word|allowed|the|limited|storage|of|3|bytes|per|word|using|``|metacode|''	Architecture|similar|to|the|Data|General|``|NOVA|''|in|that|the|memory|and|i\\/o|highway|be|separate|whereas|DEC|use|a|common|highway|for|memory|and|i\\/o|with|the|peripheral|occupy|memory|address|.|the|NOVA|be|a|16|bit|design|but|the|molecular|be|18|bit|allow|sign|arithmetic|and|a|memory|parity|check|bit|.|the|instruction|set|be|in|some|way|similar|to|the|Nova|.|the|m18|be|design|by|a|small|team|head|by|Colin|Chapman|&|Ian|Miller|.|operate|system|know|as|LOS|develop|by|Joe|Templeman|in|Leicester|office|of|systemation|.|programming|be|do|in|octal|notation|machine|code|in|longhand|use|code|sheet|.|the|17|bit|word|allow|the|limited|storage|of|3|byte|per|word|use|``|Metacode|''	NNP|JJ|TO|DT|NNP|NNP|``|NNP|''|IN|IN|DT|NN|CC|NN|NNS|VBD-AUX|JJ|IN|NNP|VBD|DT|JJ|NN|IN|NN|CC|NN|IN|DT|NNS|VBG|NN|NNS|.|DT|NNP|VBD-AUX|DT|CD|NN|NN|CC|DT|JJ|VBD-AUX|CD|NN|VBG|VBN|NN|CC|DT|NN|NN|NN|NN|.|DT|NN|NN|VBD-AUX|IN|DT|NNS|JJ|TO|DT|NNP|.|DT|NN|VBD-AUX|VBN|IN|DT|JJ|NN|VBN|IN|NNP|NNP|CC|NNP|NNP|.|VBG|NN|VBN|IN|NNP|VBN|IN|NNP|NNP|IN|NNP|NN|IN|NN|.|NN|VBD-AUX|VBN|IN|JJ|NN|NN|NN|IN|NN|VBG|VBG|NNS|.|DT|CD|NN|NN|VBD|DT|JJ|NN|IN|CD|NNS|IN|NN|VBG|``|NNP|''	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|NUMBER|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|PERSON|O|PERSON|PERSON|O|O|O|O|O|O|O|O|PERSON|PERSON|O|LOCATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||16.0|||||||18.0||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||17.0||||||||3.0|||||||				Architecture similar to the Data General "NOVA" in that the memory and I|O highways were separate whereas DEC used a common highway for memory and I|O with the peripherals occupying memory addresses. The NOVA was a 16 bit design but the Molecular was 18 bit allowing signed arithmetic and a memory parity check bit. The instruction set was in some ways similar to the Nova. The M18 was designed by a small team headed by Colin Chapman & Ian Miller.|Operating system known as LOS developed by Joe Templeman in Leicester office of Systemation. Programming was done in octal notation machine code in longhand using coding sheets. The 17 bit word allowed the limited storage of 3 bytes per word using "Metacode"	fb:part.architecture_similar_to_the_data_general_nova_in_that_the_memory_and_i|fb:part.o_highways_were_separate_whereas_dec_used_a_common_highway_for_memory_and_i|fb:part.o_with_the_peripherals_occupying_memory_addresses_the_nova_was_a_16_bit_design_but_the_molecular_was_18_bit_allowing_signed_arithmetic_and_a_memory_parity_check_bit_the_instruction_set_was_in_some_ways_similar_to_the_nova_the_m18_was_designed_by_a_small_team_headed_by_colin_chapman_ian_miller|fb:part.operating_system_known_as_los_developed_by_joe_templeman_in_leicester_office_of_systemation_programming_was_done_in_octal_notation_machine_code_in_longhand_using_coding_sheets_the_17_bit_word_allowed_the_limited_storage_of_3_bytes_per_word_using_metacode
3	4	fb:cell.ferrite_core_memory_from_plessey_or_fabritek_and_later_emm_front_loading_disk_drive_optional_paper_tape_reader_d400_front_loading_cartridge_875_kbit_d800_1_75_mbit	Ferrite core memory. from Plessey or Fabritek and later EMM.\nFront-loading disk drive\nOptional paper tape reader D400 front-loading cartridge 875 kbit D800 1.75 Mbit	ferrite|core|memory|.|from|plessey|or|fabritek|and|later|emm|.|front|loading|disk|drive|optional|paper|tape|reader|d400|front|loading|cartridge|875|kbit|d800|1.75|mbit	ferrite|core|memory|.|from|Plessey|or|Fabritek|and|later|emm|.|front|loading|disk|drive|optional|paper|tape|reader|d400|front|load|cartridge|875|kbit|d800|1.75|mbit	JJ|NN|NN|.|IN|NNP|CC|NNP|CC|JJ|NN|.|NN|NN|NN|NN|JJ|NN|NN|NN|NN|NN|VBG|NN|CD|NN|NN|CD|NN	O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|NUMBER|O	||||||||||||||||||||||||875.0|||1.75|				Ferrite core memory. from Plessey or Fabritek and later EMM.|Front-loading disk drive|Optional paper tape reader D400 front-loading cartridge 875 kbit D800 1.75 Mbit	fb:part.ferrite_core_memory_from_plessey_or_fabritek_and_later_emm|fb:part.front_loading_disk_drive|fb:part.optional_paper_tape_reader_d400_front_loading_cartridge_875_kbit_d800_1_75_mbit
4	0	fb:cell.mark_2	Mark 2	mark|2	Mark|2	NNP|CD	O|NUMBER	|2.0	2.0			Mark 2	fb:part.mark_2
4	1	fb:cell.1973	1973	1973	1973	CD	DATE	1973	1973.0	1973-xx-xx			
4	2	fb:cell.sales_order_processing	Sales Order processing	sales|order|processing	sale|order|processing	NNS|NN|NN	O|O|O	||				Sales Order processing	fb:part.sales_order_processing
4	3	fb:cell.totally_new_processor_with_hardware_interrupt_stacking_a_variable_cycle_time_and_new_disc_controller_same_basic_architecture_as_the_mk1_used_a_totally_new_os_water_cooled_cabinets_with_remote_chiller_unit_initially_later_normal_fan_cooling	Totally new processor with hardware interrupt stacking, a variable cycle time and new disc controller. Same basic architecture as the MK1. Used a totally new OS.\nWater-cooled cabinets with remote chiller unit initially, later normal fan cooling.	totally|new|processor|with|hardware|interrupt|stacking|,|a|variable|cycle|time|and|new|disc|controller|.|same|basic|architecture|as|the|mk1|.|used|a|totally|new|os|.|water|cooled|cabinets|with|remote|chiller|unit|initially|,|later|normal|fan|cooling|.	totally|new|processor|with|hardware|interrupt|stack|,|a|variable|cycle|time|and|new|disc|controller|.|same|basic|architecture|as|the|mk1|.|use|a|totally|new|OS|.|Water|cool|cabinet|with|remote|chiller|unit|initially|,|later|normal|fan|cooling|.	RB|JJ|NN|IN|NN|NN|VBG|,|DT|JJ|NN|NN|CC|JJ|NN|NN|.|JJ|JJ|NN|IN|DT|NN|.|VBN|DT|RB|JJ|NNP|.|NNP|VBD|NNS|IN|JJ|NN|NN|RB|,|RB|JJ|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||				Totally new processor with hardware interrupt stacking|a variable cycle time and new disc controller. Same basic architecture as the MK1. Used a totally new OS.|Water-cooled cabinets with remote chiller unit initially|later normal fan cooling.	fb:part.totally_new_processor_with_hardware_interrupt_stacking|fb:part.a_variable_cycle_time_and_new_disc_controller_same_basic_architecture_as_the_mk1_used_a_totally_new_os|fb:part.water_cooled_cabinets_with_remote_chiller_unit_initially|fb:part.later_normal_fan_cooling
4	4	fb:cell.fabritek_or_emm_ferrite_core_memory_introduced_the_dd1600_cdc_9427_top_loading_disk_drive_one_fixed_one_removable_and_or_a_large_multi_platter_cdc_removable_disk_drive	Fabritek or EMM ferrite core memory. Introduced the DD1600 CDC 9427 top-loading disk drive one fixed one removable and/or a large multi-platter CDC removable disk drive.	fabritek|or|emm|ferrite|core|memory|.|introduced|the|dd1600|cdc|9427|top|loading|disk|drive|one|fixed|one|removable|and\\/or|a|large|multi|platter|cdc|removable|disk|drive|.	fabritek|or|emm|ferrite|core|memory|.|introduce|the|dd1600|CDC|9427|top|load|disk|drive|one|fix|one|removable|and\\/or|a|large|multus|platter|CDC|removable|disk|drive|.	NN|CC|NN|JJ|NN|NN|.|VBN|DT|NN|NNP|CD|JJ|VBG|NN|NN|CD|VBN|CD|JJ|CC|DT|JJ|NNS|VBP|NNP|JJ|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|NUMBER|O|NUMBER|O|O|O|O|O|O|ORGANIZATION|O|O|O|O	|||||||||||9427.0|||||1.0||1.0|||||||||||				Fabritek or EMM ferrite core memory. Introduced the DD1600 CDC 9427 top-loading disk drive one fixed one removable and|or a large multi-platter CDC removable disk drive.	fb:part.fabritek_or_emm_ferrite_core_memory_introduced_the_dd1600_cdc_9427_top_loading_disk_drive_one_fixed_one_removable_and|fb:part.or_a_large_multi_platter_cdc_removable_disk_drive
5	0	fb:cell.mark_3	Mark 3	mark|3	Mark|3	NNP|CD	O|NUMBER	|3.0	3.0			Mark 3	fb:part.mark_3
5	1	fb:cell.null											fb:part.null
5	2	fb:cell.null											fb:part.null
5	3	fb:cell.designation_not_used_but_was_effectively_the_3me_6me_which_was_a_mk2_processor_modified_to_run_the_mk1_os_and_application_software_hardware_interrupt_stacking_disabled_used_a_low_capacity_version_of_the_cdc_9427	Designation not used but was effectively the 3ME/6ME which was a MK2 processor modified to run the MK1 OS and application software. Hardware interrupt stacking disabled. Used a low capacity version of the CDC 9427	designation|not|used|but|was|effectively|the|3me\\/6me|which|was|a|mk2|processor|modified|to|run|the|mk1|os|and|application|software|.|hardware|interrupt|stacking|disabled|.|used|a|low|capacity|version|of|the|cdc|9427	designation|not|use|but|be|effectively|the|3me\\/6me|which|be|a|mk2|processor|modify|to|run|the|mk1|os|and|application|software|.|hardware|interrupt|stack|disabled|.|use|a|low|capacity|version|of|the|CDC|9427	NN|RB|VBN|CC|VBD-AUX|RB|DT|NN|WDT|VBD-AUX|DT|NN|NN|VBN|TO|VB|DT|NN|NN|CC|NN|NN|.|NN|NN|VBG|JJ|.|VBN|DT|JJ|NN|NN|IN|DT|NNP|CD	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER	||||||||||||||||||||||||||||||||||||9427.0	3.0		6.0	Designation not used but was effectively the 3ME|6ME which was a MK2 processor modified to run the MK1 OS and application software. Hardware interrupt stacking disabled. Used a low capacity version of the CDC 9427	fb:part.designation_not_used_but_was_effectively_the_3me|fb:part.6me_which_was_a_mk2_processor_modified_to_run_the_mk1_os_and_application_software_hardware_interrupt_stacking_disabled_used_a_low_capacity_version_of_the_cdc_9427
5	4	fb:cell.null											fb:part.null
6	0	fb:cell.mark_4	Mark 4	mark|4	Mark|4	NNP|CD	O|NUMBER	|4.0	4.0			Mark 4	fb:part.mark_4
6	1	fb:cell.null											fb:part.null
6	2	fb:cell.sop_livestock_markets_paper_merchants_plumbers_merchants	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	sop|,|livestock|markets|,|paper|merchants|,|plumbers|merchants|.	sop|,|Livestock|Markets|,|Paper|merchant|,|Plumbers|merchant|.	NN|,|NNP|NNP|,|NNP|NNS|,|NNPS|NNS|.	O|O|O|O|O|O|O|O|O|O|O	||||||||||				SOP|Livestock Markets|Paper merchants|Plumbers merchants.	fb:part.sop|fb:part.livestock_markets|fb:part.paper_merchants|fb:part.plumbers_merchants
6	3	fb:cell.developed_after_the_demise_of_bcl_by_abs_computers_basically_a_6me_in_a_new_cabinet_with_lead_acid_battery_backed_static_ram_instead_of_ferrite_core_programming_still_done_in_octal_notation_machine_code_in_longhand_using_coding_sheets	Developed after the demise of BCL by ABS computers. Basically a 6ME in a new cabinet with lead acid battery backed static RAM instead of ferrite core.\nProgramming still done in octal notation machine code in longhand using coding sheets.	developed|after|the|demise|of|bcl|by|abs|computers|.|basically|a|6me|in|a|new|cabinet|with|lead|acid|battery|backed|static|ram|instead|of|ferrite|core|.|programming|still|done|in|octal|notation|machine|code|in|longhand|using|coding|sheets|.	develop|after|the|demise|of|bcl|by|ab|computer|.|basically|a|6me|in|a|new|cabinet|with|lead|acid|battery|back|static|RAM|instead|of|ferrite|core|.|programming|still|do|in|octal|notation|machine|code|in|longhand|use|code|sheet|.	VBN|IN|DT|NN|IN|NN|IN|NN|NNS|.|RB|DT|NN|IN|DT|JJ|NN|IN|JJ|NN|NN|VBD|JJ|NNP|RB|IN|JJ|NN|.|NN|RB|VBN|IN|JJ|NN|NN|NN|IN|NN|VBG|VBG|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||				Developed after the demise of BCL by ABS computers. Basically a 6ME in a new cabinet with lead acid battery backed static RAM instead of ferrite core.|Programming still done in octal notation machine code in longhand using coding sheets.	fb:part.developed_after_the_demise_of_bcl_by_abs_computers_basically_a_6me_in_a_new_cabinet_with_lead_acid_battery_backed_static_ram_instead_of_ferrite_core|fb:part.programming_still_done_in_octal_notation_machine_code_in_longhand_using_coding_sheets
6	4	fb:cell.core_memory_or_static_ram_introduced_the_cdc_hawk_9427h_disk_drive_up_to_4_supported_per_controller_max_2_controllers_and_or_a_large_multi_platter_cdc_d8000_removable_disk_drive	Core Memory or static RAM, Introduced the CDC Hawk 9427H disk drive, up to 4 supported per controller, max 2 controllers and/or a large multi-platter CDC D8000 removable disk drive.	core|memory|or|static|ram|,|introduced|the|cdc|hawk|9427h|disk|drive|,|up|to|4|supported|per|controller|,|max|2|controllers|and\\/or|a|large|multi|platter|cdc|d8000|removable|disk|drive|.	core|memory|or|static|RAM|,|introduce|the|CDC|hawk|9427h|disk|drive|,|up|to|4|support|per|controller|,|max|2|controller|and\\/or|a|large|multus|platter|CDC|d8000|removable|disk|drive|.	NN|NN|CC|JJ|NNP|,|VBD|DT|NNP|NN|NN|NN|NN|,|RB|TO|CD|VBD|IN|NN|,|NN|CD|NNS|CC|DT|JJ|NNS|VBP|NNP|NN|JJ|NN|NN|.	O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|NUMBER|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O	||||||||||||||||<=4.0||||||2.0||||||||||||	9427.0		4.0	Core Memory or static RAM|Introduced the CDC Hawk 9427H disk drive|up to 4 supported per controller|max 2 controllers and|or a large multi-platter CDC D8000 removable disk drive.	fb:part.core_memory_or_static_ram|fb:part.introduced_the_cdc_hawk_9427h_disk_drive|fb:part.up_to_4_supported_per_controller|fb:part.max_2_controllers_and|fb:part.or_a_large_multi_platter_cdc_d8000_removable_disk_drive
7	0	fb:cell.mark_5	Mark 5	mark|5	Mark|5	NNP|CD	O|NUMBER	|5.0	5.0			Mark 5	fb:part.mark_5
7	1	fb:cell.1984	1984	1984	1984	CD	DATE	1984	1984.0	1984-xx-xx			
7	2	fb:cell.sop_livestock_markets_paper_merchants_plumbers_merchants	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	sop|,|livestock|markets|,|paper|merchants|,|plumbers|merchants|.	sop|,|Livestock|Markets|,|Paper|merchant|,|Plumbers|merchant|.	NN|,|NNP|NNP|,|NNP|NNS|,|NNPS|NNS|.	O|O|O|O|O|O|O|O|O|O|O	||||||||||				SOP|Livestock Markets|Paper merchants|Plumbers merchants.	fb:part.sop|fb:part.livestock_markets|fb:part.paper_merchants|fb:part.plumbers_merchants
7	3	fb:cell.basically_a_re_engineering_of_the_6me_processor_to_remove_redundant_stack_logic_and_reduced_to_3_boards_with_a_printed_circuit_backplane_instead_of_the_costly_wire_wrapped_backplane_used_for_earlier_models_hardware_re_design_by_systemation_developments_k_a_howlett_keith_alec_b1943_son_of_w_a_howlett_with_assistance_in_the_final_test_stages_by_g_boote_cabinet_design_by_business_computers_systems_ltd_hardware_designed_and_manufactured_by_systemation_developments_for_business_computers_systems	Basically a re engineering of the 6ME processor to remove redundant stack logic and reduced to 3 boards with a printed circuit backplane instead of the costly wire wrapped backplane used for earlier models.\nHardware re-design by Systemation Developments K. A. Howlett (Keith Alec b1943 son of W A Howlett)with assistance in the final test stages by G. Boote. Cabinet design by Business Computers Systems Ltd. Hardware designed and manufactured by Systemation Developments for Business computers Systems.	basically|a|re|engineering|of|the|6me|processor|to|remove|redundant|stack|logic|and|reduced|to|3|boards|with|a|printed|circuit|backplane|instead|of|the|costly|wire|wrapped|backplane|used|for|earlier|models|.|hardware|re|design|by|systemation|developments|k.|a.|howlett|-lrb-|keith|alec|b1943|son|of|w|a|howlett|-rrb-|with|assistance|in|the|final|test|stages|by|g.|boote|.|cabinet|design|by|business|computers|systems|ltd.|.|hardware|designed|and|manufactured|by|systemation|developments|for|business|computers|systems|.	basically|a|re|engineering|of|the|6me|processor|to|remove|redundant|stack|logic|and|reduce|to|3|board|with|a|print|circuit|backplane|instead|of|the|costly|wire|wrap|backplane|use|for|earlier|model|.|hardware|re|design|by|Systemation|Developments|K.|A.|Howlett|-lrb-|Keith|Alec|b1943|son|of|w|a|howlett|-rrb-|with|assistance|in|the|final|test|stage|by|G.|Boote|.|Cabinet|design|by|Business|Computers|Systems|Ltd.|.|hardware|design|and|manufacture|by|Systemation|Developments|for|Business|computer|Systems|.	RB|DT|NN|NN|IN|DT|NN|NN|TO|VB|JJ|VBP|NN|CC|VBN|TO|CD|NNS|IN|DT|VBN|NN|NN|RB|IN|DT|JJ|NN|VBD|NN|VBN|IN|JJR|NNS|.|NN|NN|NN|IN|NNP|NNP|NNP|NNP|NNP|-LRB-|NNP|NNP|NN|NN|IN|NN|NN|NN|-RRB-|IN|NN|IN|DT|JJ|NN|NNS|IN|NNP|NNP|.|NNP|NN|IN|NNP|NNPS|NNPS|NNP|.|NN|VBN|CC|VBN|IN|NNP|NNP|IN|NNP|NNS|NNPS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|PERSON|PERSON|O|PERSON|PERSON|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|PERSON|PERSON|O|O|O|O|ORGANIZATION|ORGANIZATION|ORGANIZATION|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||3.0||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	6.0		3.0	Basically a re engineering of the 6ME processor to remove redundant stack logic and reduced to 3 boards with a printed circuit backplane instead of the costly wire wrapped backplane used for earlier models.|Hardware re-design by Systemation Developments K. A. Howlett (Keith Alec b1943 son of W A Howlett)with assistance in the final test stages by G. Boote. Cabinet design by Business Computers Systems Ltd. Hardware designed and manufactured by Systemation Developments for Business computers Systems.	fb:part.basically_a_re_engineering_of_the_6me_processor_to_remove_redundant_stack_logic_and_reduced_to_3_boards_with_a_printed_circuit_backplane_instead_of_the_costly_wire_wrapped_backplane_used_for_earlier_models|fb:part.hardware_re_design_by_systemation_developments_k_a_howlett_keith_alec_b1943_son_of_w_a_howlett_with_assistance_in_the_final_test_stages_by_g_boote_cabinet_design_by_business_computers_systems_ltd_hardware_designed_and_manufactured_by_systemation_developments_for_business_computers_systems
7	4	fb:cell.initially_large_format_ampex_ferrite_core_memory_then_static_ram_both_introduced_by_systemation_developments_later_bank_switching_memory_introduced_32k_18_bit_words_base_memory_plus_up_to_8_x_32k_banks_first_sold_with_cdc_hawk_9427h_drives_later_cdc_lark_2_disk_drives_memory_and_new_rs232_4_port_i_o_card_quad_i_o_by_k_a_howlett_lark_2_disc_controller_by_j_adams_up_to_four_cdc_lark_or_amcodyne_drives_per_controller_max_2_controllers	Initially large-format Ampex ferrite core memory then static RAM, both introduced by Systemation Developments.\nLater Bank switching memory introduced (32K 18 bit words base memory plus up to 8 X 32K banks).\nFirst sold with CDC Hawk 9427H drives later CDC Lark 2 disk drives. Memory and new RS232 4 port I/O card (Quad I/O) by K. A. Howlett, Lark 2 disc controller by J. Adams. Up to four CDC Lark or Amcodyne drives per controller, max 2 controllers.	initially|large|format|ampex|ferrite|core|memory|then|static|ram|,|both|introduced|by|systemation|developments|.|later|bank|switching|memory|introduced|-lrb-|32k|18|bit|words|base|memory|plus|up|to|8|x|32k|banks|-rrb-|.|first|sold|with|cdc|hawk|9427h|drives|later|cdc|lark|2|disk|drives|.|memory|and|new|rs232|4|port|i\\/o|card|-lrb-|quad|i\\/o|-rrb-|by|k.|a.|howlett|,|lark|2|disc|controller|by|j.|adams|.|up|to|four|cdc|lark|or|amcodyne|drives|per|controller|,|max|2|controllers|.	initially|large|format|Ampex|ferrite|core|memory|then|static|RAM|,|both|introduce|by|Systemation|Developments|.|later|Bank|switching|memory|introduce|-lrb-|32k|18|bit|word|base|memory|plus|up|to|8|x|32k|bank|-rrb-|.|first|sell|with|CDC|Hawk|9427h|drive|later|CDC|Lark|2|disk|drive|.|memory|and|new|rs232|4|port|i\\/o|card|-lrb-|quad|i\\/o|-rrb-|by|K.|A.|Howlett|,|Lark|2|disc|controller|by|J.|Adams|.|up|to|four|CDC|Lark|or|Amcodyne|drive|per|controller|,|max|2|controller|.	RB|JJ|NN|NNP|JJ|NN|NN|RB|JJ|NNP|,|DT|VBN|IN|NNP|NNP|.|RB|NNP|NN|NN|VBN|-LRB-|CD|CD|NN|NNS|NN|NN|CC|IN|TO|CD|NN|CD|NNS|-RRB-|.|RB|VBN|IN|NNP|NNP|NN|NNS|RB|NNP|NNP|CD|NN|NNS|.|NN|CC|JJ|NN|CD|NN|NN|NN|-LRB-|NN|NN|-RRB-|IN|NNP|NNP|NNP|,|NNP|CD|NN|NN|IN|NNP|NNP|.|IN|TO|CD|NNP|NNP|CC|NNP|NNS|IN|NN|,|NN|CD|NNS|.	O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|NUMBER|O|O|O|O|O|O|O|NUMBER|O|NUMBER|O|O|O|ORDINAL|O|O|ORGANIZATION|O|O|O|O|ORGANIZATION|O|NUMBER|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|PERSON|PERSON|PERSON|O|O|NUMBER|O|O|O|PERSON|PERSON|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|NUMBER|O|O	|||||||||||||||||||||||18.0|18.0||||||||<=8.0||32.0||||1.0||||||||||2.0||||||||4.0||||||||||||||2.0|||||||||<=4.0||||||||||2.0||				Initially large-format Ampex ferrite core memory then static RAM|both introduced by Systemation Developments.|Later Bank switching memory introduced (32K 18 bit words base memory plus up to 8 X 32K banks).|First sold with CDC Hawk 9427H drives later CDC Lark 2 disk drives. Memory and new RS232 4 port I|O card (Quad I|O) by K. A. Howlett|Lark 2 disc controller by J. Adams. Up to four CDC Lark or Amcodyne drives per controller|max 2 controllers.	fb:part.initially_large_format_ampex_ferrite_core_memory_then_static_ram|fb:part.both_introduced_by_systemation_developments|fb:part.later_bank_switching_memory_introduced_32k_18_bit_words_base_memory_plus_up_to_8_x_32k_banks|fb:part.first_sold_with_cdc_hawk_9427h_drives_later_cdc_lark_2_disk_drives_memory_and_new_rs232_4_port_i|fb:part.o_card_quad_i|fb:part.o_by_k_a_howlett|fb:part.lark_2_disc_controller_by_j_adams_up_to_four_cdc_lark_or_amcodyne_drives_per_controller|fb:part.max_2_controllers
8	0	fb:cell.distributor	Distributor	distributor	distributor	NN	O					Distributor	fb:part.distributor
8	1	fb:cell.1986	1986	1986	1986	CD	DATE	1986	1986.0	1986-xx-xx			
8	2	fb:cell.sop_livestock_markets_paper_merchants_plumbers_merchants	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	sop|,|livestock|markets|,|paper|merchants|,|plumbers|merchants|.	sop|,|Livestock|Markets|,|Paper|merchant|,|Plumbers|merchant|.	NN|,|NNP|NNP|,|NNP|NNS|,|NNPS|NNS|.	O|O|O|O|O|O|O|O|O|O|O	||||||||||				SOP|Livestock Markets|Paper merchants|Plumbers merchants.	fb:part.sop|fb:part.livestock_markets|fb:part.paper_merchants|fb:part.plumbers_merchants
8	3	fb:cell.smaller_version_of_the_mark_5_in_an_oversized_pc_style_vertical_cabinet_with_a_cdc_lark_2_drive_built_in_designed_and_manufactured_by_systemation_developments_k_a_howlett_for_business_computers_systems_a_single_board_processor_was_developed_to_replace_the_3_card_processor_and_was_working_but_never_came_to_market_at_the_same_time_a_transputer_based_maths_co_processor_had_also_been_developed_assembler_programming_introduced_by_systemation_developments_with_the_aid_of_a_third_party_running_on_pc_s_with_the_program_then_downloaded_to_the_m18	Smaller version of the Mark 5 in an oversized PC style vertical cabinet with a CDC Lark 2 drive built in. Designed and manufactured by Systemation Developments K. A. Howlett for Business Computers Systems. A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party, running on PC's with the program then downloaded to the M18.	smaller|version|of|the|mark|5|in|an|oversized|pc|style|vertical|cabinet|with|a|cdc|lark|2|drive|built|in|.|designed|and|manufactured|by|systemation|developments|k.|a.|howlett|for|business|computers|systems|.|a|single|board|processor|was|developed|to|replace|the|3|card|processor|and|was|working|but|never|came|to|market|.|at|the|same|time|a|transputer|based|maths|co|processor|had|also|been|developed|.|assembler|programming|introduced|by|systemation|developments|with|the|aid|of|a|third|party|,|running|on|pc|'s|with|the|program|then|downloaded|to|the|m18|.	smaller|version|of|the|Mark|5|in|a|oversized|pc|style|vertical|cabinet|with|a|CDC|lark|2|drive|build|in|.|design|and|manufacture|by|Systemation|Developments|K.|A.|Howlett|for|Business|Computers|Systems|.|a|single|board|processor|be|develop|to|replace|the|3|card|processor|and|be|work|but|never|come|to|market|.|at|the|same|time|a|transputer|base|math|co|processor|have|also|be|develop|.|Assembler|programming|introduce|by|Systemation|Developments|with|the|aid|of|a|third|party|,|run|on|pc|'s|with|the|program|then|download|to|the|m18|.	JJR|NN|IN|DT|NNP|CD|IN|DT|JJ|NN|NN|JJ|NN|IN|DT|NNP|NN|CD|NN|VBN|IN|.|VBN|CC|VBN|IN|NNP|NNP|NNP|NNP|NNP|IN|NNP|NNPS|NNPS|.|DT|JJ|NN|NN|VBD-AUX|VBN|TO|VB|DT|CD|NN|NN|CC|VBD-AUX|VBG|CC|RB|VBD|TO|NN|.|IN|DT|JJ|NN|DT|NN|VBN|NNS|VBP|NN|VBD-AUX|RB|VBD-AUX|VBN|.|NNP|NN|VBN|IN|NNP|NNP|IN|DT|NN|IN|DT|JJ|NN|,|VBG|IN|NN|POS|IN|DT|NN|RB|VBD|TO|DT|NN|.	O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|ORGANIZATION|ORGANIZATION|NUMBER|O|O|O|O|O|O|O|O|O|O|PERSON|PERSON|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORDINAL|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||5.0||||||||||||2.0||||||||||||||||||||||||||||3.0||||||||||||||||||||||||||||||||||||||3.0|||||||||||||||	5.0		2.0	Smaller version of the Mark 5 in an oversized PC style vertical cabinet with a CDC Lark 2 drive built in. Designed and manufactured by Systemation Developments K. A. Howlett for Business Computers Systems. A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party|running on PC's with the program then downloaded to the M18.	fb:part.smaller_version_of_the_mark_5_in_an_oversized_pc_style_vertical_cabinet_with_a_cdc_lark_2_drive_built_in_designed_and_manufactured_by_systemation_developments_k_a_howlett_for_business_computers_systems_a_single_board_processor_was_developed_to_replace_the_3_card_processor_and_was_working_but_never_came_to_market_at_the_same_time_a_transputer_based_maths_co_processor_had_also_been_developed_assembler_programming_introduced_by_systemation_developments_with_the_aid_of_a_third_party|fb:part.running_on_pc_s_with_the_program_then_downloaded_to_the_m18
8	4	fb:cell.support_for_seven_additional_external_lark_2_or_amcodyne_drives_4_per_controller_including_the_integral_drive_32k_18_bit_words_of_base_memory_plus_up_to_8_32k_banks	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.	support|for|seven|additional|external|lark|2|or|amcodyne|drives|4|per|controller|including|the|integral|drive|.|32k|18|bit|words|of|base|memory|plus|up|to|8|32k|banks|.	support|for|seven|additional|external|lark|2|or|amcodyne|drive|4|per|controller|include|the|integral|drive|.|32k|18|bit|word|of|base|memory|plus|up|to|8|32k|bank|.	NN|IN|CD|JJ|JJ|NN|CD|CC|NN|NNS|CD|IN|NN|VBG|DT|JJ|NN|.|CD|CD|NN|NNS|IN|NN|NN|CC|IN|TO|CD|CD|NNS|.	O|O|NUMBER|O|O|O|NUMBER|O|O|O|NUMBER|O|O|O|O|O|O|O|NUMBER|NUMBER|O|O|O|O|O|O|O|O|NUMBER|NUMBER|O|O	||7.0||||2.0||||4.0||||||||18.0|18.0|||||||||<=8.0|<=8.0||	2.0		4.0	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.	fb:part.support_for_seven_additional_external_lark_2_or_amcodyne_drives_4_per_controller_including_the_integral_drive_32k_18_bit_words_of_base_memory_plus_up_to_8_32k_banks
9	0	fb:cell.null											fb:part.null
9	1	fb:cell.1990	1990(?)	1990|-lrb-|?|-rrb-	1990|-lrb-|?|-rrb-	CD|-LRB-|.|-RRB-	DATE|O|O|O	1990|||	1990.0				
9	2	fb:cell.sop_livestock_markets_paper_merchants_plumbers_merchants	SOP, Livestock Markets, Paper merchants, Plumbers merchants.	sop|,|livestock|markets|,|paper|merchants|,|plumbers|merchants|.	sop|,|Livestock|Markets|,|Paper|merchant|,|Plumbers|merchant|.	NN|,|NNP|NNP|,|NNP|NNS|,|NNPS|NNS|.	O|O|O|O|O|O|O|O|O|O|O	||||||||||				SOP|Livestock Markets|Paper merchants|Plumbers merchants.	fb:part.sop|fb:part.livestock_markets|fb:part.paper_merchants|fb:part.plumbers_merchants
9	3	fb:cell.a_single_board_processor_was_developed_to_replace_the_3_card_processor_and_was_working_but_never_came_to_market_at_the_same_time_a_transputer_based_maths_co_processor_had_also_been_developed_assembler_programming_introduced_by_systemation_developments_with_the_aid_of_a_third_party_running_on_pc_s_with_the_program_then_downloaded_to_the_m18	A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party, running on PC's with the program then downloaded to the M18.	a|single|board|processor|was|developed|to|replace|the|3|card|processor|and|was|working|but|never|came|to|market|.|at|the|same|time|a|transputer|based|maths|co|processor|had|also|been|developed|.|assembler|programming|introduced|by|systemation|developments|with|the|aid|of|a|third|party|,|running|on|pc|'s|with|the|program|then|downloaded|to|the|m18|.	a|single|board|processor|be|develop|to|replace|the|3|card|processor|and|be|work|but|never|come|to|market|.|at|the|same|time|a|transputer|base|math|co|processor|have|also|be|develop|.|Assembler|programming|introduce|by|Systemation|Developments|with|the|aid|of|a|third|party|,|run|on|pc|'s|with|the|program|then|download|to|the|m18|.	DT|JJ|NN|NN|VBD-AUX|VBN|TO|VB|DT|CD|NN|NN|CC|VBD-AUX|VBG|CC|RB|VBD|TO|NN|.|IN|DT|JJ|NN|DT|NN|VBN|NNS|VBP|NN|VBD-AUX|RB|VBD-AUX|VBN|.|NNP|NN|VBN|IN|NNP|NNP|IN|DT|NN|IN|DT|JJ|NN|,|VBG|IN|NN|POS|IN|DT|NN|RB|VBD|TO|DT|NN|.	O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORDINAL|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||3.0||||||||||||||||||||||||||||||||||||||3.0|||||||||||||||	3.0			A single board processor was developed to replace the 3 card processor and was working but never came to market. At the same time a Transputer based maths co-processor had also been developed. Assembler programming introduced by Systemation Developments with the aid of a third party|running on PC's with the program then downloaded to the M18.	fb:part.a_single_board_processor_was_developed_to_replace_the_3_card_processor_and_was_working_but_never_came_to_market_at_the_same_time_a_transputer_based_maths_co_processor_had_also_been_developed_assembler_programming_introduced_by_systemation_developments_with_the_aid_of_a_third_party|fb:part.running_on_pc_s_with_the_program_then_downloaded_to_the_m18
9	4	fb:cell.support_for_seven_additional_external_lark_2_or_amcodyne_drives_4_per_controller_including_the_integral_drive_32k_18_bit_words_of_base_memory_plus_up_to_8_32k_banks	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.	support|for|seven|additional|external|lark|2|or|amcodyne|drives|4|per|controller|including|the|integral|drive|.|32k|18|bit|words|of|base|memory|plus|up|to|8|32k|banks|.	support|for|seven|additional|external|lark|2|or|amcodyne|drive|4|per|controller|include|the|integral|drive|.|32k|18|bit|word|of|base|memory|plus|up|to|8|32k|bank|.	NN|IN|CD|JJ|JJ|NN|CD|CC|NN|NNS|CD|IN|NN|VBG|DT|JJ|NN|.|CD|CD|NN|NNS|IN|NN|NN|CC|IN|TO|CD|CD|NNS|.	O|O|NUMBER|O|O|O|NUMBER|O|O|O|NUMBER|O|O|O|O|O|O|O|NUMBER|NUMBER|O|O|O|O|O|O|O|O|NUMBER|NUMBER|O|O	||7.0||||2.0||||4.0||||||||18.0|18.0|||||||||<=8.0|<=8.0||	2.0		4.0	Support for seven additional external Lark 2 or Amcodyne drives 4 per controller including the integral drive. 32K 18 bit words of base memory plus up to 8 32K banks.	fb:part.support_for_seven_additional_external_lark_2_or_amcodyne_drives_4_per_controller_including_the_integral_drive_32k_18_bit_words_of_base_memory_plus_up_to_8_32k_banks
10	0	fb:cell.distributor_ep	Distributor EP	distributor|ep	distributor|ep	NN|NN	O|O	|				Distributor EP	fb:part.distributor_ep
10	1	fb:cell.1989	1989	1989	1989	CD	DATE	1989	1989.0	1989-xx-xx			
10	2	fb:cell.sop_livestock_markets	SOP, Livestock Markets	sop|,|livestock|markets	sop|,|Livestock|Markets	NN|,|NNP|NNPS	O|O|O|O	|||				SOP|Livestock Markets	fb:part.sop|fb:part.livestock_markets
10	3	fb:cell.enhanced_version_of_the_distributor	Enhanced version of the Distributor	enhanced|version|of|the|distributor	enhance|version|of|the|distributor	VBN|NN|IN|DT|NN	O|O|O|O|O	||||				Enhanced version of the Distributor	fb:part.enhanced_version_of_the_distributor
10	4	fb:cell.scsi_disk_and_tape_support	SCSI disk and tape support	scsi|disk|and|tape|support	SCSI|disk|and|tape|support	NNP|NN|CC|NN|NN	O|O|O|O|O	||||				SCSI disk and tape support	fb:part.scsi_disk_and_tape_support
