row	col	id	content	tokens	lemmaTokens	posTags	nerTags	nerValues	number	date	num2	list	listId
-1	0	fb:row.row.devanagari	Devanagari	devanagari	devanagari	JJ	LOCATION						
-1	1	fb:row.row.iso_15919	ISO 15919	iso|15919	iso|15919	NN|CD	O|NUMBER	|15919.0					
-1	2	fb:row.row.unrsgn	UNRSGN	unrsgn	UNRSGN	NNP	O						
-1	3	fb:row.row.iast	IAST	iast	iast	NN	O						
-1	4	fb:row.row.comment	Comment	comment	comment	NN	O						
0	0	fb:cell.null	ए /  े	ए|\\/|े	ए|\\/|े	NN|:|CD	O|O|NUMBER	||				ए|े	fb:part.null|fb:part.null_2
0	1	fb:cell.e	ē	ē	ē	NN	O						
0	2	fb:cell.e	ē	ē	ē	NN	O						
0	3	fb:cell.e	ē	ē	ē	NN	O						
0	4	fb:cell.to_distinguish_between_long_and_short_e_in_dravidian_languages_e_now_represents_short_note_that_the_use_of_e_is_considered_optional_in_iso_15919_and_using_e_for_long_is_acceptable_for_languages_that_do_not_distinguish_long_and_short_e	To distinguish between long and short 'e' in Dravidian languages, 'e' now represents ऎ /  ॆ (short). Note that the use of ē is considered optional in ISO 15919, and using e for ए (long) is acceptable for languages that do not distinguish long and short e.	to|distinguish|between|long|and|short|`|e|'|in|dravidian|languages|,|`|e|'|now|represents|ऎ|\\/|ॆ|-lrb-|short|-rrb-|.|note|that|the|use|of|ē|is|considered|optional|in|iso|15919|,|and|using|e|for|ए|-lrb-|long|-rrb-|is|acceptable|for|languages|that|do|not|distinguish|long|and|short|e.	to|distinguish|between|long|and|short|`|e|'|in|dravidian|language|,|`|e|'|now|represent|ऎ|\\/|ॆ|-lrb-|short|-rrb-|.|note|that|the|use|of|ē|be|consider|optional|in|iso|15919|,|and|use|e|for|ए|-lrb-|long|-rrb-|be|acceptable|for|language|that|do|not|distinguish|long|and|short|e.	TO|VB|IN|JJ|CC|JJ|``|LS|''|IN|JJ|NNS|,|``|LS|''|RB|VBZ|CD|:|CD|-LRB-|JJ|-RRB-|.|VB|IN|DT|NN|IN|NN|VBD-AUX|VBN|JJ|IN|NN|CD|,|CC|VBG|LS|IN|NN|-LRB-|JJ|-RRB-|VBD-AUX|JJ|IN|NNS|WDT|VBD-AUX|RB|VB|JJ|CC|JJ|NN	O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|DATE|O|NUMBER|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||PRESENT_REF||||||||||||||||||||15919.0|||||||||||||||||||||				To distinguish between long and short 'e' in Dravidian languages|'e' now represents ऎ|ॆ (short). Note that the use of ē is considered optional in ISO 15919|and using e for ए (long) is acceptable for languages that do not distinguish long and short e.	fb:part.to_distinguish_between_long_and_short_e_in_dravidian_languages|fb:part._e_now_represents|fb:part._short_note_that_the_use_of_e_is_considered_optional_in_iso_15919|fb:part.and_using_e_for_long_is_acceptable_for_languages_that_do_not_distinguish_long_and_short_e
1	0	fb:cell.null_2	ओ /  ो	ओ|\\/|ो	ओ|\\/|ो	NN|:|CD	O|O|NUMBER	||				ओ|ो	fb:part.null_3|fb:part.null_4
1	1	fb:cell.o	ō	ō	ō	NN	O						
1	2	fb:cell.o	ō	ō	ō	NN	O						
1	3	fb:cell.o	ō	ō	ō	NN	O						
1	4	fb:cell.to_distinguish_between_long_and_short_o_in_dravidian_languages_o_now_represents_short_note_that_the_use_of_o_is_considered_optional_in_iso_15919_and_using_o_for_long_is_acceptable_for_languages_that_do_not_distinguish_long_and_short_o	To distinguish between long and short 'o' in Dravidian languages, 'o' now represents ऒ /  ॊ (short). Note that the use of ō is considered optional in ISO 15919, and using o for ओ (long) is acceptable for languages that do not distinguish long and short o.	to|distinguish|between|long|and|short|`|o|'|in|dravidian|languages|,|`|o|'|now|represents|ऒ|\\/|ॊ|-lrb-|short|-rrb-|.|note|that|the|use|of|ō|is|considered|optional|in|iso|15919|,|and|using|o|for|ओ|-lrb-|long|-rrb-|is|acceptable|for|languages|that|do|not|distinguish|long|and|short|o.	to|distinguish|between|long|and|short|`|o|'|in|dravidian|language|,|`|o|'|now|represent|ऒ|\\/|ॊ|-lrb-|short|-rrb-|.|note|that|the|use|of|ō|be|consider|optional|in|iso|15919|,|and|use|o|for|ओ|-lrb-|long|-rrb-|be|acceptable|for|language|that|do|not|distinguish|long|and|short|o.	TO|VB|IN|JJ|CC|JJ|``|NN|''|IN|JJ|NNS|,|``|NN|''|RB|VBZ|CD|:|CD|-LRB-|JJ|-RRB-|.|VB|IN|DT|NN|IN|NN|VBD-AUX|VBN|JJ|IN|NN|CD|,|CC|VBG|NN|IN|NN|-LRB-|JJ|-RRB-|VBD-AUX|JJ|IN|NNS|WDT|VBD-AUX|RB|VB|JJ|CC|JJ|NN	O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|DATE|O|NUMBER|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||PRESENT_REF||||||||||||||||||||15919.0|||||||||||||||||||||				To distinguish between long and short 'o' in Dravidian languages|'o' now represents ऒ|ॊ (short). Note that the use of ō is considered optional in ISO 15919|and using o for ओ (long) is acceptable for languages that do not distinguish long and short o.	fb:part.to_distinguish_between_long_and_short_o_in_dravidian_languages|fb:part._o_now_represents|fb:part._short_note_that_the_use_of_o_is_considered_optional_in_iso_15919|fb:part.and_using_o_for_long_is_acceptable_for_languages_that_do_not_distinguish_long_and_short_o
2	0	fb:cell.null_3	ऋ /  ृ	ऋ|\\/|ृ	ऋ|\\/|ृ	NN|:|CD	O|O|NUMBER	||				ऋ|ृ	fb:part.null_5|fb:part.null_6
2	1	fb:cell.r	r̥	r̥	r̥	NN	O						
2	2	fb:cell.r	r̥	r̥	r̥	NN	O						
2	3	fb:cell.r	r̥	r̥	r̥	NN	O						
2	4	fb:cell.in_iso_15919_r_is_used_to_represent	In ISO 15919, ṛ is used to represent ड़.	in|iso|15919|,|ṛ|is|used|to|represent|ड़|.	in|iso|15919|,|ṛ|be|use|to|represent|ड़|.	IN|NN|CD|,|NN|VBD-AUX|VBN|TO|VB|NN|.	O|O|NUMBER|O|O|O|O|O|O|O|O	||15919.0||||||||	15919.0			In ISO 15919|ṛ is used to represent ड़.	fb:part.in_iso_15919|fb:part.r_is_used_to_represent
3	0	fb:cell.null_4	ॠ /  ॄ	ॠ|\\/|ॄ	ॠ|\\/|ॄ	NN|:|CD	O|O|NUMBER	||				ॠ|ॄ	fb:part.null_7|fb:part.null_8
3	1	fb:cell.r	r̥	r̥	r̥	NN	O						
3	2	fb:cell.r	r̥	r̥	r̥	NN	O						
3	3	fb:cell.r	r̥	r̥	r̥	NN	O						
3	4	fb:cell.for_consistency_with_r	For consistency with r̥	for|consistency|with|r̥	for|consistency|with|r̥	IN|NN|IN|NN	O|O|O|O	|||				For consistency with r̥	fb:part.for_consistency_with_r
4	0	fb:cell.null_5	ऌ /  ॢ	ऌ|\\/|ॢ	ऌ|\\/|ॢ	NN|:|CD	O|O|NUMBER	||				ऌ|ॢ	fb:part.null_9|fb:part.null_10
4	1	fb:cell.l	l̥	l̥	l̥	NN	O						
4	2	fb:cell.l	l̥	l̥	l̥	NN	O						
4	3	fb:cell.l	l̥	l̥	l̥	NN	O						
4	4	fb:cell.in_iso_15919_l_is_used_to_represent	In ISO 15919, ḷ is used to represent ळ.	in|iso|15919|,|ḷ|is|used|to|represent|ळ|.	in|iso|15919|,|ḷ|be|use|to|represent|ळ|.	IN|NN|CD|,|NN|VBD-AUX|VBN|TO|VB|NN|.	O|O|NUMBER|O|O|O|O|O|O|O|O	||15919.0||||||||	15919.0			In ISO 15919|ḷ is used to represent ळ.	fb:part.in_iso_15919|fb:part.l_is_used_to_represent
5	0	fb:cell.null_6	ॡ /  ॣ	ॡ|\\/|ॣ	ॡ|\\/|ॣ	NN|:|CD	O|O|NUMBER	||				ॡ|ॣ	fb:part.null_11|fb:part.null_12
5	1	fb:cell.l	l̥	l̥	l̥	NN	O						
5	2	fb:cell.l	l̥	l̥	l̥	NN	O						
5	3	fb:cell.l	l̥	l̥	l̥	NN	O						
5	4	fb:cell.for_consistency_with_l	For consistency with l̥	for|consistency|with|l̥	for|consistency|with|l̥	IN|NN|IN|NN	O|O|O|O	|||				For consistency with l̥	fb:part.for_consistency_with_l
6	0	fb:cell.null_7	◌ं	◌|ं	◌|ं	NN|NN	O|O	|				◌ं	fb:part.null_13
6	1	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
6	2	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
6	3	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
6	4	fb:cell.iso_15919_has_two_options_about_anusvara_1_in_the_simplified_nasalization_option_an_anusvara_is_always_transliterated_as_m_2_in_the_strict_nasalization_option_anusvara_before_a_class_consonant_is_transliterated_as_the_class_nasal_n_before_k_kh_g_gh_n_n_before_c_ch_j_jh_n_n_before_t_th_d_dh_n_n_before_t_th_d_dh_n_m_before_p_ph_b_bh_m_m_is_sometimes_used_to_specifically_represent_gurmukhi_tippi	ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option, an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option, anusvāra before a class consonant is transliterated as the class nasal—ṅ before k, kh, g, gh, ṅ; ñ before c, ch, j, jh, ñ; ṇ before ṭ, ṭh, ḍ, ḍh, ṇ; n before t, th, d, dh, n; m before p, ph, b, bh, m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ.	iso|15919|has|two|options|about|anusvāra|.|-lrb-|1|-rrb-|in|the|simplified|nasalization|option|,|an|anusvāra|is|always|transliterated|as|ṁ|.|-lrb-|2|-rrb-|in|the|strict|nasalization|option|,|anusvāra|before|a|class|consonant|is|transliterated|as|the|class|nasal|--|ṅ|before|k|,|kh|,|g|,|gh|,|ṅ|;|ñ|before|c|,|ch|,|j|,|jh|,|ñ|;|ṇ|before|ṭ|,|ṭh|,|ḍ|,|ḍh|,|ṇ|;|n|before|t|,|th|,|d|,|dh|,|n|;|m|before|p|,|ph|,|b|,|bh|,|m.|ṃ|is|sometimes|used|to|specifically|represent|gurmukhi|tippi|.	iso|15919|have|two|option|about|anusvāra|.|-lrb-|1|-rrb-|in|the|simplify|nasalization|option|,|a|anusvāra|be|always|transliterate|as|ṁ|.|-lrb-|2|-rrb-|in|the|strict|nasalization|option|,|anusvāra|before|a|class|consonant|be|transliterate|as|the|class|nasal|--|ṅ|before|k|,|kh|,|g|,|gh|,|ṅ|;|ñ|before|c|,|ch|,|j|,|jh|,|ñ|;|ṇ|before|ṭ|,|ṭh|,|ḍ|,|ḍh|,|ṇ|;|n|before|t|,|th|,|d|,|dh|,|n|;|m|before|p|,|ph|,|b|,|bh|,|m.|ṃ|be|sometimes|use|to|specifically|represent|Gurmukhi|Tippi|.	NN|CD|VBD-AUX|CD|NNS|IN|NN|.|-LRB-|LS|-RRB-|IN|DT|VBN|NN|NN|,|DT|NN|VBD-AUX|RB|VBN|IN|NN|.|-LRB-|LS|-RRB-|IN|DT|JJ|NN|NN|,|NN|IN|DT|NN|JJ|VBD-AUX|VBN|IN|DT|NN|JJ|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|DT|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|NN|VBD-AUX|RB|VBN|TO|RB|VB|NNP|NNP|.	O|NUMBER|O|NUMBER|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|15919.0||2.0||||||1.0|||||||||||||||||2.0||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	15919.0			ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option|an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option|anusvāra before a class consonant is transliterated as the class nasal—ṅ before k|kh|g|gh|ṅ; ñ before c|ch|j|jh|ñ; ṇ before ṭ|ṭh|ḍ|ḍh|ṇ; n before t|th|d|dh|n; m before p|ph|b|bh|m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ.	fb:part.iso_15919_has_two_options_about_anusvara_1_in_the_simplified_nasalization_option|fb:part.an_anusvara_is_always_transliterated_as_m_2_in_the_strict_nasalization_option|fb:part.anusvara_before_a_class_consonant_is_transliterated_as_the_class_nasal_n_before_k|fb:part.kh|fb:part.g|fb:part.gh|fb:part.n_n_before_c|fb:part.ch|fb:part.j|fb:part.jh|fb:part.n_n_before_t|fb:part.th|fb:part.d|fb:part.dh|fb:part.n_n_before_t|fb:part.th|fb:part.d|fb:part.dh|fb:part.n_m_before_p|fb:part.ph|fb:part.b|fb:part.bh|fb:part.m_m_is_sometimes_used_to_specifically_represent_gurmukhi_tippi
7	0	fb:cell.null_7	◌ं	◌|ं	◌|ं	NN|NN	O|O	|				◌ं	fb:part.null_13
7	1	fb:cell.n_n_n_n_m	ṅ ñ ṇ n m	ṅ|ñ|ṇ|n|m	ṅ|ñ|ṇ|n|m	NN|NN|NN|NN|NN	O|O|O|O|O	||||					
7	2	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
7	3	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
7	4	fb:cell.iso_15919_has_two_options_about_anusvara_1_in_the_simplified_nasalization_option_an_anusvara_is_always_transliterated_as_m_2_in_the_strict_nasalization_option_anusvara_before_a_class_consonant_is_transliterated_as_the_class_nasal_n_before_k_kh_g_gh_n_n_before_c_ch_j_jh_n_n_before_t_th_d_dh_n_n_before_t_th_d_dh_n_m_before_p_ph_b_bh_m_m_is_sometimes_used_to_specifically_represent_gurmukhi_tippi	ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option, an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option, anusvāra before a class consonant is transliterated as the class nasal—ṅ before k, kh, g, gh, ṅ; ñ before c, ch, j, jh, ñ; ṇ before ṭ, ṭh, ḍ, ḍh, ṇ; n before t, th, d, dh, n; m before p, ph, b, bh, m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ.	iso|15919|has|two|options|about|anusvāra|.|-lrb-|1|-rrb-|in|the|simplified|nasalization|option|,|an|anusvāra|is|always|transliterated|as|ṁ|.|-lrb-|2|-rrb-|in|the|strict|nasalization|option|,|anusvāra|before|a|class|consonant|is|transliterated|as|the|class|nasal|--|ṅ|before|k|,|kh|,|g|,|gh|,|ṅ|;|ñ|before|c|,|ch|,|j|,|jh|,|ñ|;|ṇ|before|ṭ|,|ṭh|,|ḍ|,|ḍh|,|ṇ|;|n|before|t|,|th|,|d|,|dh|,|n|;|m|before|p|,|ph|,|b|,|bh|,|m.|ṃ|is|sometimes|used|to|specifically|represent|gurmukhi|tippi|.	iso|15919|have|two|option|about|anusvāra|.|-lrb-|1|-rrb-|in|the|simplify|nasalization|option|,|a|anusvāra|be|always|transliterate|as|ṁ|.|-lrb-|2|-rrb-|in|the|strict|nasalization|option|,|anusvāra|before|a|class|consonant|be|transliterate|as|the|class|nasal|--|ṅ|before|k|,|kh|,|g|,|gh|,|ṅ|;|ñ|before|c|,|ch|,|j|,|jh|,|ñ|;|ṇ|before|ṭ|,|ṭh|,|ḍ|,|ḍh|,|ṇ|;|n|before|t|,|th|,|d|,|dh|,|n|;|m|before|p|,|ph|,|b|,|bh|,|m.|ṃ|be|sometimes|use|to|specifically|represent|Gurmukhi|Tippi|.	NN|CD|VBD-AUX|CD|NNS|IN|NN|.|-LRB-|LS|-RRB-|IN|DT|VBN|NN|NN|,|DT|NN|VBD-AUX|RB|VBN|IN|NN|.|-LRB-|LS|-RRB-|IN|DT|JJ|NN|NN|,|NN|IN|DT|NN|JJ|VBD-AUX|VBN|IN|DT|NN|JJ|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|:|NN|IN|NN|,|DT|,|NN|,|NN|,|NN|:|NN|IN|NN|,|NN|,|NN|,|NN|,|NN|NN|VBD-AUX|RB|VBN|TO|RB|VB|NNP|NNP|.	O|NUMBER|O|NUMBER|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|15919.0||2.0||||||1.0|||||||||||||||||2.0||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	15919.0			ISO 15919 has two options about anusvāra. (1) In the simplified nasalization option|an anusvāra is always transliterated as ṁ. (2) In the strict nasalization option|anusvāra before a class consonant is transliterated as the class nasal—ṅ before k|kh|g|gh|ṅ; ñ before c|ch|j|jh|ñ; ṇ before ṭ|ṭh|ḍ|ḍh|ṇ; n before t|th|d|dh|n; m before p|ph|b|bh|m. ṃ is sometimes used to specifically represent Gurmukhi Tippi  ੰ.	fb:part.iso_15919_has_two_options_about_anusvara_1_in_the_simplified_nasalization_option|fb:part.an_anusvara_is_always_transliterated_as_m_2_in_the_strict_nasalization_option|fb:part.anusvara_before_a_class_consonant_is_transliterated_as_the_class_nasal_n_before_k|fb:part.kh|fb:part.g|fb:part.gh|fb:part.n_n_before_c|fb:part.ch|fb:part.j|fb:part.jh|fb:part.n_n_before_t|fb:part.th|fb:part.d|fb:part.dh|fb:part.n_n_before_t|fb:part.th|fb:part.d|fb:part.dh|fb:part.n_m_before_p|fb:part.ph|fb:part.b|fb:part.bh|fb:part.m_m_is_sometimes_used_to_specifically_represent_gurmukhi_tippi
8	0	fb:cell.null_8	◌ँ	◌|ँ	◌|ँ	NN|NN	O|O	|				◌ँ	fb:part.null_14
8	1	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
8	2	fb:cell.null_9											
8	3	fb:cell.m	ṁ	ṁ	ṁ	NN	O						
8	4	fb:cell.vowel_nasalization_is_transliterated_as_a_tilde_above_the_transliterated_vowel_over_the_second_vowel_in_the_case_of_a_digraph_such_as_ai_au_except_in_sanskrit	Vowel nasalization is transliterated as a tilde above the transliterated vowel (over the second vowel in the case of a digraph such as aĩ, aũ), except in Sanskrit.	vowel|nasalization|is|transliterated|as|a|tilde|above|the|transliterated|vowel|-lrb-|over|the|second|vowel|in|the|case|of|a|digraph|such|as|aĩ|,|aũ|-rrb-|,|except|in|sanskrit|.	vowel|nasalization|be|transliterate|as|a|tilde|above|the|transliterate|vowel|-lrb-|over|the|second|vowel|in|the|case|of|a|digraph|such|as|aĩ|,|aũ|-rrb-|,|except|in|Sanskrit|.	NN|NN|VBD-AUX|VBN|IN|DT|NN|IN|DT|VBN|NN|-LRB-|IN|DT|JJ|NN|IN|DT|NN|IN|DT|NN|JJ|IN|NN|,|NN|-RRB-|,|IN|IN|NNP|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORDINAL|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||2.0||||||||||||||||||				Vowel nasalization is transliterated as a tilde above the transliterated vowel (over the second vowel in the case of a digraph such as aĩ|aũ)|except in Sanskrit.	fb:part.vowel_nasalization_is_transliterated_as_a_tilde_above_the_transliterated_vowel_over_the_second_vowel_in_the_case_of_a_digraph_such_as_ai|fb:part.au|fb:part.except_in_sanskrit
