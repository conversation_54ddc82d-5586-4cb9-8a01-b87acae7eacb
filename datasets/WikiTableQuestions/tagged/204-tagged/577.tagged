row	col	id	content	tokens	lemmaTokens	posTags	nerTags	nerValues	number	date	num2	list	listId
-1	0	fb:row.row.name	Name	name	name	VB	O						
-1	1	fb:row.row.first_operational	First operational	first|operational	First|operational	NNP|JJ	ORDINAL|O	1.0|					
-1	2	fb:row.row.numeral_system	Numeral system	numeral|system	numeral|system	NN|NN	O|O	|					
-1	3	fb:row.row.computing_mechanism	Computing mechanism	computing|mechanism	Computing|mechanism	NNP|NN	O|O	|					
-1	4	fb:row.row.programming	Programming	programming	program	VBG	O						
-1	5	fb:row.row.turing_complete	Turing complete	turing|complete	ture|complete	VBG|JJ	O|O	|					
0	0	fb:cell.zuse_z3_germany	Zuse Z3 (Germany)	zuse|z3|-lrb-|germany|-rrb-	Zuse|z3|-lrb-|Germany|-rrb-	NNP|NN|-LRB-|NNP|-RRB-	PERSON|O|O|LOCATION|O	||||	3.0				
0	1	fb:cell.may_1941	May 1941	may|1941	May|1941	VBD-AUX|CD	DATE|DATE	1941-05|1941-05	1941.0	1941-05-xx			
0	2	fb:cell.binary_floating_point	Binary floating point	binary|floating|point	binary|floating|point	JJ|JJ|NN	O|O|O	||					
0	3	fb:cell.electro_mechanical	Electro-mechanical	electro|mechanical	Electro|mechanical	NNP|JJ	O|O	|					
0	4	fb:cell.program_controlled_by_punched_35_mm_film_stock_but_no_conditional_branch	Program-controlled by punched 35 mm film stock (but no conditional branch)	program|controlled|by|punched|35|mm|film|stock|-lrb-|but|no|conditional|branch|-rrb-	program|control|by|punch|35|mm|film|stock|-lrb-|but|no|conditional|branch|-rrb-	NN|VBN|IN|VBN|CD|NN|NN|NN|-LRB-|CC|DT|JJ|NN|-RRB-	O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O	||||35.0|||||||||	35.0				
0	5	fb:cell.in_theory_1998	In theory (1998)	in|theory|-lrb-|1998|-rrb-	in|theory|-lrb-|1998|-rrb-	IN|NN|-LRB-|CD|-RRB-	O|O|O|DATE|O	|||1998|	1998.0				
1	0	fb:cell.atanasoff_berry_computer_us	Atanasoff–Berry Computer (US)	atanasoff|--|berry|computer|-lrb-|us|-rrb-	Atanasoff|--|Berry|Computer|-lrb-|US|-rrb-	NNP|:|NNP|NNP|-LRB-|NNP|-RRB-	PERSON|O|O|O|O|LOCATION|O	||||||					
1	1	fb:cell.1942	1942	1942	1942	CD	DATE	1942	1942.0	1942-xx-xx			
1	2	fb:cell.binary	Binary	binary	binary	JJ	O						
1	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
1	4	fb:cell.not_programmable_single_purpose	Not programmable—single purpose	not|programmable|--|single|purpose	not|programmable|--|single|purpose	RB|JJ|:|JJ|NN	O|O|O|O|O	||||					
1	5	fb:cell.no	No	no	no	DT	O						
2	0	fb:cell.colossus_mark_1_uk	Colossus Mark 1 (UK)	colossus|mark|1|-lrb-|uk|-rrb-	Colossus|Mark|1|-lrb-|UK|-rrb-	NNP|NNP|CD|-LRB-|NNP|-RRB-	O|O|NUMBER|O|LOCATION|O	||1.0|||	1.0				
2	1	fb:cell.february_1944	February 1944	february|1944	February|1944	NNP|CD	DATE|DATE	1944-02|1944-02	1944.0	1944-02-xx			
2	2	fb:cell.binary	Binary	binary	binary	JJ	O						
2	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
2	4	fb:cell.program_controlled_by_patch_cables_and_switches	Program-controlled by patch cables and switches	program|controlled|by|patch|cables|and|switches	program|control|by|patch|cable|and|switch	NN|VBN|IN|NN|NNS|CC|NNS	O|O|O|O|O|O|O	||||||					
2	5	fb:cell.no	No	no	no	DT	O						
3	0	fb:cell.harvard_mark_i_ibm_ascc_us	Harvard Mark I – IBM ASCC (US)	harvard|mark|i|--|ibm|ascc|-lrb-|us|-rrb-	Harvard|Mark|I|--|IBM|ASCC|-lrb-|US|-rrb-	NNP|NNP|PRP|:|NNP|NNP|-LRB-|NNP|-RRB-	O|O|O|O|O|O|O|LOCATION|O	||||||||					
3	1	fb:cell.may_1944	May 1944	may|1944	May|1944	VBD-AUX|CD	DATE|DATE	1944-05|1944-05	1944.0	1944-05-xx			
3	2	fb:cell.decimal	Decimal	decimal	decimal	JJ	O						
3	3	fb:cell.electro_mechanical	Electro-mechanical	electro|mechanical	Electro|mechanical	NNP|JJ	O|O	|					
3	4	fb:cell.program_controlled_by_24_channel_punched_paper_tape_but_no_conditional_branch	Program-controlled by 24-channel punched paper tape (but no conditional branch)	program|controlled|by|24|channel|punched|paper|tape|-lrb-|but|no|conditional|branch|-rrb-	program|control|by|24|channel|punch|paper|tape|-lrb-|but|no|conditional|branch|-rrb-	NN|VBN|IN|CD|NN|VBD|NN|NN|-LRB-|CC|DT|JJ|NN|-RRB-	O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O	|||24.0||||||||||	24.0				
3	5	fb:cell.debatable	Debatable	debatable	debatable	JJ	O						
4	0	fb:cell.colossus_mark_2_uk	Colossus Mark 2 (UK)	colossus|mark|2|-lrb-|uk|-rrb-	Colossus|Mark|2|-lrb-|UK|-rrb-	NNP|NNP|CD|-LRB-|NNP|-RRB-	O|O|NUMBER|O|LOCATION|O	||2.0|||	2.0				
4	1	fb:cell.june_1944	June 1944	june|1944	June|1944	NNP|CD	DATE|DATE	1944-06|1944-06	1944.0	1944-06-xx			
4	2	fb:cell.binary	Binary	binary	binary	JJ	O						
4	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
4	4	fb:cell.program_controlled_by_patch_cables_and_switches	Program-controlled by patch cables and switches	program|controlled|by|patch|cables|and|switches	program|control|by|patch|cable|and|switch	NN|VBN|IN|NN|NNS|CC|NNS	O|O|O|O|O|O|O	||||||					
4	5	fb:cell.in_theory_2011	In theory (2011)	in|theory|-lrb-|2011|-rrb-	in|theory|-lrb-|2011|-rrb-	IN|NN|-LRB-|CD|-RRB-	O|O|O|DATE|O	|||2011|	2011.0				
5	0	fb:cell.zuse_z4_germany	Zuse Z4 (Germany)	zuse|z4|-lrb-|germany|-rrb-	Zuse|z4|-lrb-|Germany|-rrb-	NNP|NN|-LRB-|NNP|-RRB-	PERSON|O|O|LOCATION|O	||||	4.0				
5	1	fb:cell.march_1945	March 1945	march|1945	March|1945	NNP|CD	DATE|DATE	1945-03|1945-03	1945.0	1945-03-xx			
5	2	fb:cell.binary_floating_point	Binary floating point	binary|floating|point	binary|floating|point	JJ|JJ|NN	O|O|O	||					
5	3	fb:cell.electro_mechanical	Electro-mechanical	electro|mechanical	Electro|mechanical	NNP|JJ	O|O	|					
5	4	fb:cell.program_controlled_by_punched_35_mm_film_stock	Program-controlled by punched 35 mm film stock	program|controlled|by|punched|35|mm|film|stock	program|control|by|punch|35|mm|film|stock	NN|VBN|IN|VBN|CD|NN|NN|NN	O|O|O|O|NUMBER|O|O|O	||||35.0|||	35.0				
5	5	fb:cell.yes	Yes	yes	yes	UH	O						
6	0	fb:cell.eniac_us	ENIAC (US)	eniac|-lrb-|us|-rrb-	ENIAC|-lrb-|US|-rrb-	NNP|-LRB-|NNP|-RRB-	O|O|LOCATION|O	|||					
6	1	fb:cell.july_1946	July 1946	july|1946	July|1946	NNP|CD	DATE|DATE	1946-07|1946-07	1946.0	1946-07-xx			
6	2	fb:cell.decimal	Decimal	decimal	decimal	JJ	O						
6	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
6	4	fb:cell.program_controlled_by_patch_cables_and_switches	Program-controlled by patch cables and switches	program|controlled|by|patch|cables|and|switches	program|control|by|patch|cable|and|switch	NN|VBN|IN|NN|NNS|CC|NNS	O|O|O|O|O|O|O	||||||					
6	5	fb:cell.yes	Yes	yes	yes	UH	O						
7	0	fb:cell.manchester_small_scale_experimental_machine_baby_uk	Manchester Small-Scale Experimental Machine (Baby) (UK)	manchester|small|scale|experimental|machine|-lrb-|baby|-rrb-|-lrb-|uk|-rrb-	Manchester|Small|Scale|experimental|machine|-lrb-|Baby|-rrb-|-lrb-|UK|-rrb-	NNP|NNP|NNP|JJ|NN|-LRB-|NNP|-RRB-|-LRB-|NNP|-RRB-	O|O|O|O|O|O|O|O|O|LOCATION|O	||||||||||					
7	1	fb:cell.june_1948	June 1948	june|1948	June|1948	NNP|CD	DATE|DATE	1948-06|1948-06	1948.0	1948-06-xx			
7	2	fb:cell.binary	Binary	binary	binary	JJ	O						
7	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
7	4	fb:cell.stored_program_in_williams_cathode_ray_tube_memory	Stored-program in Williams cathode ray tube memory	stored|program|in|williams|cathode|ray|tube|memory	store|program|in|Williams|cathode|ray|tube|memory	VBN|NN|IN|NNP|NN|NN|NN|NN	O|O|O|O|O|O|O|O	|||||||					
7	5	fb:cell.yes	Yes	yes	yes	UH	O						
8	0	fb:cell.modified_eniac_us	Modified ENIAC (US)	modified|eniac|-lrb-|us|-rrb-	modify|eniac|-lrb-|US|-rrb-	VBN|NN|-LRB-|NNP|-RRB-	O|O|O|LOCATION|O	||||					
8	1	fb:cell.september_1948	September 1948	september|1948	September|1948	NNP|CD	DATE|DATE	1948-09|1948-09	1948.0	1948-09-xx			
8	2	fb:cell.decimal	Decimal	decimal	decimal	JJ	O						
8	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
8	4	fb:cell.read_only_stored_programming_mechanism_using_the_function_tables_as_program_rom	Read-only stored programming mechanism using the Function Tables as program ROM	read|only|stored|programming|mechanism|using|the|function|tables|as|program|rom	read|only|store|programming|mechanism|use|the|function|table|as|program|rom	VB|RB|VBN|NN|NN|VBG|DT|NN|VBZ|IN|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||					
8	5	fb:cell.yes	Yes	yes	yes	UH	O						
9	0	fb:cell.manchester_mark_1_uk	Manchester Mark 1 (UK)	manchester|mark|1|-lrb-|uk|-rrb-	Manchester|Mark|1|-lrb-|UK|-rrb-	NNP|NNP|CD|-LRB-|NNP|-RRB-	O|O|NUMBER|O|LOCATION|O	||1.0|||	1.0				
9	1	fb:cell.april_1949	April 1949	april|1949	April|1949	NNP|CD	DATE|DATE	1949-04|1949-04	1949.0	1949-04-xx			
9	2	fb:cell.binary	Binary	binary	binary	JJ	O						
9	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
9	4	fb:cell.stored_program_in_williams_cathode_ray_tube_memory_and_magnetic_drum_memory	Stored-program in Williams cathode ray tube memory and magnetic drum memory	stored|program|in|williams|cathode|ray|tube|memory|and|magnetic|drum|memory	store|program|in|Williams|cathode|ray|tube|memory|and|magnetic|drum|memory	VBN|NN|IN|NNP|NN|NN|NN|NN|CC|JJ|VBP|NN	O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||					
9	5	fb:cell.yes	Yes	yes	yes	UH	O						
10	0	fb:cell.edsac_uk	EDSAC (UK)	edsac|-lrb-|uk|-rrb-	edsac|-lrb-|UK|-rrb-	NN|-LRB-|NNP|-RRB-	O|O|LOCATION|O	|||					
10	1	fb:cell.may_1949	May 1949	may|1949	May|1949	VBD-AUX|CD	DATE|DATE	1949-05|1949-05	1949.0	1949-05-xx			
10	2	fb:cell.binary	Binary	binary	binary	JJ	O						
10	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
10	4	fb:cell.stored_program_in_mercury_delay_line_memory	Stored-program in mercury delay line memory	stored|program|in|mercury|delay|line|memory	store|program|in|mercury|delay|line|memory	VBN|NN|IN|NN|NN|NN|NN	O|O|O|O|O|O|O	||||||					
10	5	fb:cell.yes	Yes	yes	yes	UH	O						
11	0	fb:cell.csirac_australia	CSIRAC (Australia)	csirac|-lrb-|australia|-rrb-	csirac|-lrb-|Australia|-rrb-	NN|-LRB-|NNP|-RRB-	O|O|LOCATION|O	|||					
11	1	fb:cell.november_1949	November 1949	november|1949	November|1949	NNP|CD	DATE|DATE	1949-11|1949-11	1949.0	1949-11-xx			
11	2	fb:cell.binary	Binary	binary	binary	JJ	O						
11	3	fb:cell.electronic	Electronic	electronic	electronic	JJ	O						
11	4	fb:cell.stored_program_in_mercury_delay_line_memory	Stored-program in mercury delay line memory	stored|program|in|mercury|delay|line|memory	store|program|in|mercury|delay|line|memory	VBN|NN|IN|NN|NN|NN|NN	O|O|O|O|O|O|O	||||||					
11	5	fb:cell.yes	Yes	yes	yes	UH	O						
