row	col	id	content	tokens	lemmaTokens	posTags	nerTags	nerValues	number	date	num2	list	listId
-1	0	fb:row.row.product	Product	product	product	NN	O						
-1	1	fb:row.row.main_functionality	Main Functionality	main|functionality	Main|Functionality	NNP|NNP	O|O	|					
-1	2	fb:row.row.input_format	Input Format	input|format	input|format	NN|NN	O|O	|					
-1	3	fb:row.row.output_format	Output Format	output|format	output|format	NN|NN	O|O	|					
-1	4	fb:row.row.platform	Platform	platform	platform	NN	O						
-1	5	fb:row.row.license_and_cost	License and cost	license|and|cost	license|and|cost	NN|CC|NN	O|O|O	||					
-1	6	fb:row.row.notes	Notes	notes	note	NNS	O						
0	0	fb:cell.allegrograph	AllegroGraph	allegrograph	AllegroGraph	NNP	O					AllegroGraph	fb:part.allegrograph
0	1	fb:cell.graph_database_rdf_with_gruff_visualization_tool	Graph Database. RDF with Gruff visualization tool	graph|database|.|rdf|with|gruff|visualization|tool	graph|database|.|rdf|with|gruff|visualization|tool	NN|NN|.|NN|IN|JJ|NN|NN	O|O|O|ORGANIZATION|O|O|O|O	|||||||				Graph Database. RDF with Gruff visualization tool	fb:part.graph_database_rdf_with_gruff_visualization_tool
0	2	fb:cell.rdf	RDF	rdf	rdf	NN	O					RDF	fb:part.rdf
0	3	fb:cell.rdf	RDF	rdf	rdf	NN	O					RDF	fb:part.rdf
0	4	fb:cell.linux_mac_windows	Linux, Mac, Windows	linux|,|mac|,|windows	Linux|,|Mac|,|Windows	NNP|,|NNP|,|NNP	O|O|O|O|MISC	||||				Linux|Mac|Windows	fb:part.linux|fb:part.mac|fb:part.windows
0	5	fb:cell.free_and_commercial	Free and Commercial	free|and|commercial	Free|and|Commercial	NNP|CC|NNP	O|O|O	||				Free and Commercial	fb:part.free_and_commercial
0	6	fb:cell.allegrograph_is_a_graph_database_it_is_disk_based_fully_transactional_oltp_database_that_stores_data_structured_in_graphs_rather_than_in_tables_allegrograph_includes_a_social_networking_analytics_library	AllegroGraph is a graph database. It is disk-based, fully transactional OLTP database that stores data structured in graphs rather than in tables. AllegroGraph includes a Social Networking Analytics library.	allegrograph|is|a|graph|database|.|it|is|disk|based|,|fully|transactional|oltp|database|that|stores|data|structured|in|graphs|rather|than|in|tables|.|allegrograph|includes|a|social|networking|analytics|library|.	AllegroGraph|be|a|graph|database|.|it|be|disk|base|,|fully|transactional|oltp|database|that|store|datum|structure|in|graph|rather|than|in|table|.|AllegroGraph|include|a|Social|Networking|Analytics|library|.	NNP|VBD-AUX|DT|NN|NN|.|PRP|VBD-AUX|NN|VBN|,|RB|JJ|NN|NN|WDT|NNS|NNS|VBN|IN|NNS|RB|IN|IN|NNS|.|NNP|VBZ|DT|NNP|NNP|NNP|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|MISC|MISC|O|O	|||||||||||||||||||||||||||||||||				AllegroGraph is a graph database. It is disk-based|fully transactional OLTP database that stores data structured in graphs rather than in tables. AllegroGraph includes a Social Networking Analytics library.	fb:part.allegrograph_is_a_graph_database_it_is_disk_based|fb:part.fully_transactional_oltp_database_that_stores_data_structured_in_graphs_rather_than_in_tables_allegrograph_includes_a_social_networking_analytics_library
1	0	fb:cell.egonet	EgoNet	egonet	EgoNet	NNP	O					EgoNet	fb:part.egonet
1	1	fb:cell.ego_centric_network_analysis	Ego-centric network analysis	ego|centric|network|analysis	ego|centric|network|analysis	NN|JJ|NN|NN	O|O|O|O	|||				Ego-centric network analysis	fb:part.ego_centric_network_analysis
1	2	fb:cell.conducts_interviews_or_takes_any_valid_xml_file	Conducts interviews or takes any valid XML file	conducts|interviews|or|takes|any|valid|xml|file	conduct|interview|or|take|any|valid|xml|file	VBZ|NNS|CC|VBZ|DT|JJ|NN|NN	O|O|O|O|O|O|O|O	|||||||				Conducts interviews or takes any valid XML file	fb:part.conducts_interviews_or_takes_any_valid_xml_file
1	3	fb:cell.output_to_csv_and_convertible_to_almost_any_other_format	Output to CSV and convertible to almost any other format	output|to|csv|and|convertible|to|almost|any|other|format	output|to|CSV|and|convertible|to|almost|any|other|format	NN|TO|NNP|CC|JJ|TO|RB|DT|JJ|NN	O|O|ORGANIZATION|O|O|O|O|O|O|O	|||||||||				Output to CSV and convertible to almost any other format	fb:part.output_to_csv_and_convertible_to_almost_any_other_format
1	4	fb:cell.any_system_supporting_java	Any system supporting Java	any|system|supporting|java	any|system|support|Java	DT|NN|VBG|NNP	O|O|O|MISC	|||				Any system supporting Java	fb:part.any_system_supporting_java
1	5	fb:cell.open_source_seeking_contributors	Open Source, seeking contributors	open|source|,|seeking|contributors	Open|Source|,|seek|contributor	NNP|NNP|,|VBG|NNS	MISC|MISC|O|O|O	||||				Open Source|seeking contributors	fb:part.open_source|fb:part.seeking_contributors
1	6	fb:cell.egonet_is_a_program_for_the_collection_and_analysis_of_egocentric_network_data_egonet_contains_facilities_to_assist_in_creating_the_questionnaire_collecting_the_data_and_providing_general_global_network_measures_and_data_matrixes_that_can_be_used_in_further_analysis_by_other_software_programs	Egonet is a program for the collection and analysis of egocentric network data. Egonet contains facilities to assist in creating the questionnaire, collecting the data and providing general global network measures and data matrixes that can be used in further analysis by other software programs.	egonet|is|a|program|for|the|collection|and|analysis|of|egocentric|network|data|.|egonet|contains|facilities|to|assist|in|creating|the|questionnaire|,|collecting|the|data|and|providing|general|global|network|measures|and|data|matrixes|that|can|be|used|in|further|analysis|by|other|software|programs|.	Egonet|be|a|program|for|the|collection|and|analysis|of|egocentric|network|datum|.|egonet|contain|facility|to|assist|in|create|the|questionnaire|,|collect|the|datum|and|provide|general|global|network|measure|and|datum|matrix|that|can|be|use|in|further|analysis|by|other|software|program|.	NNP|VBD-AUX|DT|NN|IN|DT|NN|CC|NN|IN|JJ|NN|NNS|.|NN|VBZ|NNS|TO|VB|IN|VBG|DT|NN|,|VBG|DT|NNS|CC|VBG|JJ|JJ|NN|NNS|CC|NNS|NNS|WDT|VBD-AUX|VBD-AUX|VBN|IN|JJ|NN|IN|JJ|NN|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||||||				Egonet is a program for the collection and analysis of egocentric network data. Egonet contains facilities to assist in creating the questionnaire|collecting the data and providing general global network measures and data matrixes that can be used in further analysis by other software programs.	fb:part.egonet_is_a_program_for_the_collection_and_analysis_of_egocentric_network_data_egonet_contains_facilities_to_assist_in_creating_the_questionnaire|fb:part.collecting_the_data_and_providing_general_global_network_measures_and_data_matrixes_that_can_be_used_in_further_analysis_by_other_software_programs
2	0	fb:cell.gephi	Gephi	gephi	Gephi	NNP	PERSON					Gephi	fb:part.gephi
2	1	fb:cell.graph_exploration_and_manipulation_software	Graph exploration and manipulation software	graph|exploration|and|manipulation|software	graph|exploration|and|manipulation|software	NN|NN|CC|NN|NN	O|O|O|O|O	||||				Graph exploration and manipulation software	fb:part.graph_exploration_and_manipulation_software
2	2	fb:cell.graphviz_dot_graphlet_gml_guess_gdf_leda_gml_networkx_graphml_net_nodexl_graphml_net_pajek_net_gml_sonivis_graphml_tulip_tlp_dot_ucinet_dl_yed_gml_gephi_gexf_edge_list_csv_databases	GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), Pajek(.net, .gml), Sonivis(.graphml), Tulip(.tlp, .dot), UCINET(.dl), yEd(.gml), Gephi (.gexf), Edge list(.csv), databases	graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|guess|-lrb-|.|gdf|-rrb-|,|leda|-lrb-|.|gml|-rrb-|,|networkx|-lrb-|.|graphml|,|.|net|-rrb-|,|nodexl|-lrb-|.|graphml|,|.|net|-rrb-|,|pajek|-lrb-|.|net|,|.|gml|-rrb-|,|sonivis|-lrb-|.|graphml|-rrb-|,|tulip|-lrb-|.|tlp|,|.|dot|-rrb-|,|ucinet|-lrb-|.|dl|-rrb-|,|yed|-lrb-|.|gml|-rrb-|,|gephi|-lrb-|.|gexf|-rrb-|,|edge|list|-lrb-|.|csv|-rrb-|,|databases	graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|guess|-lrb-|.|gdf|-rrb-|,|leda|-lrb-|.|gml|-rrb-|,|networkx|-lrb-|.|graphml|,|.|net|-rrb-|,|nodexl|-lrb-|.|graphml|,|.|net|-rrb-|,|pajek|-lrb-|.|net|,|.|gml|-rrb-|,|Sonivis|-lrb-|.|graphml|-rrb-|,|Tulip|-lrb-|.|tlp|,|.|dot|-rrb-|,|UCINET|-lrb-|.|dl|-rrb-|,|yed|-lrb-|.|gml|-rrb-|,|Gephi|-lrb-|.|gexf|-rrb-|,|Edge|list|-lrb-|.|csv|-rrb-|,|database	NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|,|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NNP|NN|-LRB-|.|NN|-RRB-|,|NNS	O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				GraphViz(.dot)|Graphlet(.gml)|GUESS(.gdf)|LEDA(.gml)|NetworkX(.graphml|.net)|NodeXL(.graphml|.net)|Pajek(.net|.gml)|Sonivis(.graphml)|Tulip(.tlp|.dot)|UCINET(.dl)|yEd(.gml)|Gephi (.gexf)|Edge list(.csv)|databases	fb:part.graphviz_dot|fb:part.graphlet_gml|fb:part.guess_gdf|fb:part.leda_gml|fb:part.networkx_graphml|fb:part._net|fb:part.nodexl_graphml|fb:part._net|fb:part.pajek_net|fb:part._gml|fb:part.sonivis_graphml|fb:part.tulip_tlp|fb:part._dot|fb:part.ucinet_dl|fb:part.yed_gml|fb:part.gephi_gexf|fb:part.edge_list_csv|fb:part.databases
2	3	fb:cell.guess_gdf_gephi_gexf_svg_png	GUESS(.gdf), Gephi(.gexf), .svg, .png	guess|-lrb-|.|gdf|-rrb-|,|gephi|-lrb-|.|gexf|-rrb-|,|.|svg|,|.|png	guess|-lrb-|.|gdf|-rrb-|,|Gephi|-lrb-|.|gexf|-rrb-|,|.|svg|,|.|png	NN|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|.|NN|,|.|NN	O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O	||||||||||||||||				GUESS(.gdf)|Gephi(.gexf)|.svg|.png	fb:part.guess_gdf|fb:part.gephi_gexf_2|fb:part._svg|fb:part._png
2	4	fb:cell.any_system_supporting_java_1_6_and_opengl	Any system supporting Java 1.6 and OpenGL	any|system|supporting|java|1.6|and|opengl	any|system|support|Java|1.6|and|opengl	DT|NN|VBG|NNP|CD|CC|NN	O|O|O|MISC|NUMBER|O|O	||||1.6||	1.6			Any system supporting Java 1.6 and OpenGL	fb:part.any_system_supporting_java_1_6_and_opengl
2	5	fb:cell.open_source_gpl3_seeking_contributors	Open Source (GPL3), seeking contributors	open|source|-lrb-|gpl3|-rrb-|,|seeking|contributors	Open|Source|-lrb-|gpl3|-rrb-|,|seek|contributor	NNP|NNP|-LRB-|NN|-RRB-|,|VBG|NNS	MISC|MISC|O|O|O|O|O|O	|||||||	3.0			Open Source (GPL3)|seeking contributors	fb:part.open_source_gpl3|fb:part.seeking_contributors
2	6	fb:cell.gephiis_an_interactive_visualization_and_exploration_platform_for_all_kinds_of_networks_and_complex_systems_dynamic_and_hierarchical_graphs_it_is_a_tool_for_people_that_have_to_explore_and_understand_graphs_the_user_interacts_with_the_representation_manipulate_the_structures_shapes_and_colors_to_reveal_hidden_properties_it_uses_a_3d_render_engine_to_display_large_networks_in_real_time_and_to_speed_up_the_exploration_a_flexible_and_multi_task_architecture_brings_new_possibilities_to_work_with_complex_data_sets_and_produce_valuable_visual_results	Gephiis an interactive visualization and exploration platform for all kinds of networks and complex systems, dynamic and hierarchical graphs. It is a tool for people that have to explore and understand graphs. The user interacts with the representation, manipulate the structures, shapes and colors to reveal hidden properties. It uses a 3D render engine to display large networks in real-time and to speed up the exploration. A flexible and multi-task architecture brings new possibilities to work with complex data sets and produce valuable visual results.	gephiis|an|interactive|visualization|and|exploration|platform|for|all|kinds|of|networks|and|complex|systems|,|dynamic|and|hierarchical|graphs|.|it|is|a|tool|for|people|that|have|to|explore|and|understand|graphs|.|the|user|interacts|with|the|representation|,|manipulate|the|structures|,|shapes|and|colors|to|reveal|hidden|properties|.|it|uses|a|3d|render|engine|to|display|large|networks|in|real|time|and|to|speed|up|the|exploration|.|a|flexible|and|multi|task|architecture|brings|new|possibilities|to|work|with|complex|data|sets|and|produce|valuable|visual|results|.	Gephiis|a|interactive|visualization|and|exploration|platform|for|all|kind|of|network|and|complex|system|,|dynamic|and|hierarchical|graph|.|it|be|a|tool|for|people|that|have|to|explore|and|understand|graph|.|the|user|interact|with|the|representation|,|manipulate|the|structure|,|shape|and|color|to|reveal|hidden|property|.|it|use|a|3d|render|engine|to|display|large|network|in|real|time|and|to|speed|up|the|exploration|.|a|flexible|and|multi|task|architecture|bring|new|possibility|to|work|with|complex|datum|set|and|produce|valuable|visual|result|.	NNP|DT|JJ|NN|CC|NN|NN|IN|DT|NNS|IN|NNS|CC|JJ|NNS|,|JJ|CC|JJ|NNS|.|PRP|VBD-AUX|DT|NN|IN|NNS|WDT|VBD-AUX|TO|VB|CC|VB|NNS|.|DT|NN|VBZ|IN|DT|NN|,|VB|DT|NNS|,|NNS|CC|NNS|TO|VB|JJ|NNS|.|PRP|VBZ|DT|NN|VBP|NN|TO|VB|JJ|NNS|IN|JJ|NN|CC|TO|VB|RP|DT|NN|.|DT|JJ|CC|JJ|NN|NN|VBZ|JJ|NNS|TO|VB|IN|JJ|NNS|NNS|CC|VBP|JJ|JJ|NNS|.	MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				Gephiis an interactive visualization and exploration platform for all kinds of networks and complex systems|dynamic and hierarchical graphs. It is a tool for people that have to explore and understand graphs. The user interacts with the representation|manipulate the structures|shapes and colors to reveal hidden properties. It uses a 3D render engine to display large networks in real-time and to speed up the exploration. A flexible and multi-task architecture brings new possibilities to work with complex data sets and produce valuable visual results.	fb:part.gephiis_an_interactive_visualization_and_exploration_platform_for_all_kinds_of_networks_and_complex_systems|fb:part.dynamic_and_hierarchical_graphs_it_is_a_tool_for_people_that_have_to_explore_and_understand_graphs_the_user_interacts_with_the_representation|fb:part.manipulate_the_structures|fb:part.shapes_and_colors_to_reveal_hidden_properties_it_uses_a_3d_render_engine_to_display_large_networks_in_real_time_and_to_speed_up_the_exploration_a_flexible_and_multi_task_architecture_brings_new_possibilities_to_work_with_complex_data_sets_and_produce_valuable_visual_results
3	0	fb:cell.graphstream	GraphStream	graphstream	graphstream	NN	O					GraphStream	fb:part.graphstream
3	1	fb:cell.dynamic_graph_library	Dynamic Graph Library	dynamic|graph|library	Dynamic|Graph|Library	NNP|NNP|NNP	O|O|O	||				Dynamic Graph Library	fb:part.dynamic_graph_library
3	2	fb:cell.graphstream_dgs_graphviz_dot_graphlet_gml_edge_list	GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), edge list	graphstream|-lrb-|.|dgs|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|edge|list	graphstream|-lrb-|.|dg|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|edge|list	NN|-LRB-|.|NNS|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O	|||||||||||||||||||				GraphStream(.dgs)|GraphViz(.dot)|Graphlet(.gml)|edge list	fb:part.graphstream_dgs|fb:part.graphviz_dot|fb:part.graphlet_gml|fb:part.edge_list
3	3	fb:cell.graphstream_dgs_graphviz_dot_graphlet_gml_image_sequence	GraphStream(.dgs), GraphViz(.dot), Graphlet(.gml), image sequence	graphstream|-lrb-|.|dgs|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|image|sequence	graphstream|-lrb-|.|dg|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|image|sequence	NN|-LRB-|.|NNS|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O	|||||||||||||||||||				GraphStream(.dgs)|GraphViz(.dot)|Graphlet(.gml)|image sequence	fb:part.graphstream_dgs|fb:part.graphviz_dot|fb:part.graphlet_gml|fb:part.image_sequence
3	4	fb:cell.any_system_supporting_java	Any system supporting Java	any|system|supporting|java	any|system|support|Java	DT|NN|VBG|NNP	O|O|O|MISC	|||				Any system supporting Java	fb:part.any_system_supporting_java
3	5	fb:cell.open_source	Open Source	open|source	Open|Source	NNP|NNP	MISC|MISC	|				Open Source	fb:part.open_source
3	6	fb:cell.with_graphstream_you_deal_with_graphs_static_and_dynamic_you_create_them_from_scratch_from_a_file_or_any_source_you_display_and_render_them	With GraphStream you deal with graphs. Static and Dynamic.\nYou create them from scratch, from a file or any source. You display and render them.	with|graphstream|you|deal|with|graphs|.|static|and|dynamic|.|you|create|them|from|scratch|,|from|a|file|or|any|source|.|you|display|and|render|them|.	with|graphstream|you|deal|with|graph|.|static|and|Dynamic|.|you|create|they|from|scratch|,|from|a|file|or|any|source|.|you|display|and|render|they|.	IN|NN|PRP|VBP|IN|NNS|.|JJ|CC|NNP|.|PRP|VBP|PRP|IN|NN|,|IN|DT|NN|CC|DT|NN|.|PRP|VBP|CC|VBP|PRP|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||				With GraphStream you deal with graphs. Static and Dynamic.|You create them from scratch|from a file or any source. You display and render them.	fb:part.with_graphstream_you_deal_with_graphs_static_and_dynamic|fb:part.you_create_them_from_scratch|fb:part.from_a_file_or_any_source_you_display_and_render_them
4	0	fb:cell.graph_tool	Graph-tool	graph|tool	graph|tool	NN|NN	O|O	|				Graph-tool	fb:part.graph_tool
4	1	fb:cell.python_module_for_efficient_analysis_and_visualization_of_graphs	Python module for efficient analysis and visualization of graphs.	python|module|for|efficient|analysis|and|visualization|of|graphs|.	Python|module|for|efficient|analysis|and|visualization|of|graph|.	NNP|NN|IN|JJ|NN|CC|NN|IN|NNS|.	O|O|O|O|O|O|O|O|O|O	|||||||||				Python module for efficient analysis and visualization of graphs.	fb:part.python_module_for_efficient_analysis_and_visualization_of_graphs
4	2	fb:cell.graphviz_dot_graphml	GraphViz(.dot), GraphML	graphviz|-lrb-|.|dot|-rrb-|,|graphml	graphviz|-lrb-|.|dot|-rrb-|,|graphml	NN|-LRB-|.|NN|-RRB-|,|NN	O|O|O|O|O|O|O	||||||				GraphViz(.dot)|GraphML	fb:part.graphviz_dot|fb:part.graphml
4	3	fb:cell.graphviz_dot_graphml_bmp_canon_cmap_eps_fig_gd_gd2_gif_gtk_ico_imap_cmapx_ismap_jpeg_pdf_plain_png_ps_ps2_svg_svgz_tif_vml_vmlz_vrml_wbmp_xlib	GraphViz(.dot), GraphML, .bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib	graphviz|-lrb-|.|dot|-rrb-|,|graphml|,|.|bmp|,|.|canon|,|.|cmap|,|.|eps|,|.|fig|,|.|gd|,|.|gd2|,|.|gif|,|.|gtk|,|.|ico|,|.|imap|,|.|cmapx|,|.|ismap|,|.|jpeg|,|.|pdf|,|.|plain|,|.|png|,|.|ps|,|.|ps2|,|.|svg|,|.|svgz|,|.|tif|,|.|vml|,|.|vmlz|,|.|vrml|,|.|wbmp|,|.|xlib	graphviz|-lrb-|.|dot|-rrb-|,|graphml|,|.|bmp|,|.|canon|,|.|cmap|,|.|ep|,|.|fig|,|.|gd|,|.|gd2|,|.|gif|,|.|gtk|,|.|ico|,|.|imap|,|.|cmapx|,|.|ismap|,|.|jpeg|,|.|pdf|,|.|plain|,|.|png|,|.|p|,|.|ps2|,|.|svg|,|.|svgz|,|.|tif|,|.|vml|,|.|vmlz|,|.|vrml|,|.|wbmp|,|.|xlib	NN|-LRB-|.|NN|-RRB-|,|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NNS|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|RB|,|.|NN|,|.|NNS|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				GraphViz(.dot)|GraphML|.bmp|.canon|.cmap|.eps|.fig|.gd|.gd2|.gif|.gtk|.ico|.imap|.cmapx|.ismap|.jpeg|.pdf|.plain|.png|.ps|.ps2|.svg|.svgz|.tif|.vml|.vmlz|.vrml|.wbmp|.xlib	fb:part.graphviz_dot|fb:part.graphml|fb:part._bmp|fb:part._canon|fb:part._cmap|fb:part._eps|fb:part._fig|fb:part._gd|fb:part._gd2|fb:part._gif|fb:part._gtk|fb:part._ico|fb:part._imap|fb:part._cmapx|fb:part._ismap|fb:part._jpeg|fb:part._pdf|fb:part._plain|fb:part._png|fb:part._ps|fb:part._ps2|fb:part._svg|fb:part._svgz|fb:part._tif|fb:part._vml|fb:part._vmlz|fb:part._vrml|fb:part._wbmp|fb:part._xlib
4	4	fb:cell.gnu_linux_mac	GNU/Linux, Mac	gnu\\/linux|,|mac	GNU\\/Linux|,|Mac	NNP|,|NNP	O|O|O	||				GNU|Linux|Mac	fb:part.gnu|fb:part.linux|fb:part.mac
4	5	fb:cell.free_software_gpl3	Free Software (GPL3)	free|software|-lrb-|gpl3|-rrb-	Free|Software|-lrb-|gpl3|-rrb-	NNP|NNP|-LRB-|NN|-RRB-	O|O|O|O|O	||||	3.0			Free Software (GPL3)	fb:part.free_software_gpl3
4	6	fb:cell.graph_tool_is_a_python_module_for_efficient_analysis_of_graphs_its_core_data_structures_and_algorithms_are_implemented_in_c_with_heavy_use_of_template_metaprogramming_based_on_the_boost_graph_library_it_contains_a_comprehensive_list_of_algorithms	Graph-tool is a python module for efficient analysis of graphs. Its core data structures and algorithms are implemented in C++, with heavy use of Template metaprogramming, based on the Boost Graph Library. It contains a comprehensive list of algorithms.	graph|tool|is|a|python|module|for|efficient|analysis|of|graphs|.|its|core|data|structures|and|algorithms|are|implemented|in|c|+|+|,|with|heavy|use|of|template|metaprogramming|,|based|on|the|boost|graph|library|.|it|contains|a|comprehensive|list|of|algorithms|.	graph|tool|be|a|python|module|for|efficient|analysis|of|graph|.|its|core|datum|structure|and|algorithm|be|implement|in|c|+|+|,|with|heavy|use|of|template|metaprogramming|,|base|on|the|Boost|Graph|Library|.|it|contain|a|comprehensive|list|of|algorithm|.	NN|NN|VBD-AUX|DT|NN|NN|IN|JJ|NN|IN|NNS|.|PRP$|NN|NNS|NNS|CC|NNS|VBD-AUX|VBN|IN|NN|CC|CC|,|IN|JJ|NN|IN|NN|NN|,|VBN|IN|DT|NNP|NNP|NNP|.|PRP|VBZ|DT|JJ|NN|IN|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||				Graph-tool is a python module for efficient analysis of graphs. Its core data structures and algorithms are implemented in C++|with heavy use of Template metaprogramming|based on the Boost Graph Library. It contains a comprehensive list of algorithms.	fb:part.graph_tool_is_a_python_module_for_efficient_analysis_of_graphs_its_core_data_structures_and_algorithms_are_implemented_in_c|fb:part.with_heavy_use_of_template_metaprogramming|fb:part.based_on_the_boost_graph_library_it_contains_a_comprehensive_list_of_algorithms
5	0	fb:cell.graphviz	Graphviz	graphviz	graphviz	NN	O					Graphviz	fb:part.graphviz
5	1	fb:cell.graph_vizualisation_software	Graph vizualisation software	graph|vizualisation|software	graph|vizualisation|software	NN|NN|NN	O|O|O	||				Graph vizualisation software	fb:part.graph_vizualisation_software
5	2	fb:cell.graphviz_dot	GraphViz(.dot)	graphviz|-lrb-|.|dot|-rrb-	graphviz|-lrb-|.|dot|-rrb-	NN|-LRB-|.|NN|-RRB-	O|O|O|O|O	||||				GraphViz(.dot)	fb:part.graphviz_dot
5	3	fb:cell._bmp_canon_cmap_eps_fig_gd_gd2_gif_gtk_ico_imap_cmapx_ismap_jpeg_pdf_plain_png_ps_ps2_svg_svgz_tif_vml_vmlz_vrml_wbmp_xlib	.bmp, .canon, .cmap, .eps, .fig, .gd, .gd2, .gif, .gtk, .ico, .imap, .cmapx, .ismap, .jpeg, .pdf, .plain, .png, .ps, .ps2, .svg, .svgz, .tif, .vml, .vmlz, .vrml, .wbmp, .xlib	.|bmp|,|.|canon|,|.|cmap|,|.|eps|,|.|fig|,|.|gd|,|.|gd2|,|.|gif|,|.|gtk|,|.|ico|,|.|imap|,|.|cmapx|,|.|ismap|,|.|jpeg|,|.|pdf|,|.|plain|,|.|png|,|.|ps|,|.|ps2|,|.|svg|,|.|svgz|,|.|tif|,|.|vml|,|.|vmlz|,|.|vrml|,|.|wbmp|,|.|xlib	.|bmp|,|.|canon|,|.|cmap|,|.|ep|,|.|fig|,|.|gd|,|.|gd2|,|.|gif|,|.|gtk|,|.|ico|,|.|imap|,|.|cmapx|,|.|ismap|,|.|jpeg|,|.|pdf|,|.|plain|,|.|png|,|.|p|,|.|ps2|,|.|svg|,|.|svgz|,|.|tif|,|.|vml|,|.|vmlz|,|.|vrml|,|.|wbmp|,|.|xlib	.|NN|,|.|NN|,|.|NN|,|.|NNS|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|RB|,|.|NN|,|.|NNS|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN|,|.|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				.bmp|.canon|.cmap|.eps|.fig|.gd|.gd2|.gif|.gtk|.ico|.imap|.cmapx|.ismap|.jpeg|.pdf|.plain|.png|.ps|.ps2|.svg|.svgz|.tif|.vml|.vmlz|.vrml|.wbmp|.xlib	fb:part._bmp|fb:part._canon|fb:part._cmap|fb:part._eps|fb:part._fig|fb:part._gd|fb:part._gd2|fb:part._gif|fb:part._gtk|fb:part._ico|fb:part._imap|fb:part._cmapx|fb:part._ismap|fb:part._jpeg|fb:part._pdf|fb:part._plain|fb:part._png|fb:part._ps|fb:part._ps2|fb:part._svg|fb:part._svgz|fb:part._tif|fb:part._vml|fb:part._vmlz|fb:part._vrml|fb:part._wbmp|fb:part._xlib
5	4	fb:cell.linux_mac_windows	Linux, Mac, Windows	linux|,|mac|,|windows	Linux|,|Mac|,|Windows	NNP|,|NNP|,|NNP	O|O|O|O|MISC	||||				Linux|Mac|Windows	fb:part.linux|fb:part.mac|fb:part.windows
5	5	fb:cell.open_source_cpl	Open Source (CPL)	open|source|-lrb-|cpl|-rrb-	Open|Source|-lrb-|CPL|-rrb-	NNP|NNP|-LRB-|NNP|-RRB-	O|O|O|O|O	||||				Open Source (CPL)	fb:part.open_source_cpl
5	6	fb:cell.graphviz_is_open_source_graph_visualization_framework_it_has_several_main_graph_layout_programs_suitable_for_social_network_visualization	Graphviz is open source graph visualization framework. It has several main graph layout programs suitable for social network visualization.	graphviz|is|open|source|graph|visualization|framework|.|it|has|several|main|graph|layout|programs|suitable|for|social|network|visualization|.	graphviz|be|open|source|graph|visualization|framework|.|it|have|several|main|graph|layout|program|suitable|for|social|network|visualization|.	NN|VBD-AUX|JJ|NN|NN|NN|NN|.|PRP|VBD-AUX|JJ|JJ|NN|NN|NNS|JJ|IN|JJ|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||				Graphviz is open source graph visualization framework. It has several main graph layout programs suitable for social network visualization.	fb:part.graphviz_is_open_source_graph_visualization_framework_it_has_several_main_graph_layout_programs_suitable_for_social_network_visualization
6	0	fb:cell.java_universal_network_graph_jung_framework	Java Universal Network/Graph (JUNG) Framework	java|universal|network\\/graph|-lrb-|jung|-rrb-|framework	Java|Universal|Network\\/Graph|-lrb-|JUNG|-rrb-|framework	NNP|NNP|NNP|-LRB-|NNP|-RRB-|NN	MISC|MISC|MISC|O|O|O|O	||||||				Java Universal Network|Graph (JUNG) Framework	fb:part.java_universal_network|fb:part.graph_jung_framework
6	1	fb:cell.network_and_graph_manipulation_analysis_and_visualization	network and graph manipulation, analysis, and visualization	network|and|graph|manipulation|,|analysis|,|and|visualization	network|and|graph|manipulation|,|analysis|,|and|visualization	NN|CC|NN|NN|,|NN|,|CC|NN	O|O|O|O|O|O|O|O|O	||||||||				network and graph manipulation|analysis|and visualization	fb:part.network_and_graph_manipulation|fb:part.analysis|fb:part.and_visualization
6	2	fb:cell.built_in_support_for_graphml_pajek_and_some_text_formats_user_can_create_parsers_for_any_desired_format	built-in support for GraphML, Pajek, and some text formats; user can create parsers for any desired format	built|in|support|for|graphml|,|pajek|,|and|some|text|formats|;|user|can|create|parsers|for|any|desired|format	build|in|support|for|graphml|,|pajek|,|and|some|text|format|;|user|can|create|parser|for|any|desire|format	VBN|IN|NN|IN|NN|,|NN|,|CC|DT|NN|NNS|:|NN|VBD-AUX|VB|NNS|IN|DT|VBN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||				built-in support for GraphML|Pajek|and some text formats; user can create parsers for any desired format	fb:part.built_in_support_for_graphml|fb:part.pajek|fb:part.and_some_text_formats_user_can_create_parsers_for_any_desired_format
6	3	fb:cell.built_in_support_for_graphml_pajek_and_some_text_formats_user_can_create_exporters_for_any_desired_format	built-in support for GraphML, Pajek, and some text formats; user can create exporters for any desired format	built|in|support|for|graphml|,|pajek|,|and|some|text|formats|;|user|can|create|exporters|for|any|desired|format	build|in|support|for|graphml|,|pajek|,|and|some|text|format|;|user|can|create|exporter|for|any|desire|format	VBN|IN|NN|IN|NN|,|NN|,|CC|DT|NN|NNS|:|NN|VBD-AUX|VB|NNS|IN|DT|VBN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||				built-in support for GraphML|Pajek|and some text formats; user can create exporters for any desired format	fb:part.built_in_support_for_graphml|fb:part.pajek|fb:part.and_some_text_formats_user_can_create_exporters_for_any_desired_format
6	4	fb:cell.any_platform_supporting_java	Any platform supporting Java	any|platform|supporting|java	any|platform|support|Java	DT|NN|VBG|NNP	O|O|O|MISC	|||				Any platform supporting Java	fb:part.any_platform_supporting_java
6	5	fb:cell.open_source_bsd_license	Open source (BSD license)	open|source|-lrb-|bsd|license|-rrb-	open|source|-lrb-|bsd|license|-rrb-	VB|NN|-LRB-|NN|NN|-RRB-	O|O|O|O|O|O	|||||				Open source (BSD license)	fb:part.open_source_bsd_license
6	6	fb:cell.jung_is_a_java_api_and_library_that_provides_a_common_and_extensible_language_for_the_modeling_analysis_and_visualization_of_relational_data_it_supports_a_variety_of_graph_types_including_hypergraphs_supports_graph_elements_of_any_type_and_with_any_properties_enables_customizable_visualizations_and_includes_algorithms_from_graph_theory_data_mining_and_social_network_analysis_e_g_clustering_decomposition_optimization_random_graph_generation_statistical_analysis_distances_flows_and_centrality_pagerank_hits_etc_it_is_limited_only_by_the_amount_of_memory_allocated_to_java	JUNG is a Java API and library that provides a common and extensible language for the modeling, analysis, and visualization of relational data. It supports a variety of graph types (including hypergraphs), supports graph elements of any type and with any properties, enables customizable visualizations, and includes algorithms from graph theory, data mining, and social network analysis (e.g., clustering, decomposition, optimization, random graph generation, statistical analysis, distances, flows, and centrality (PageRank, HITS, etc.)). It is limited only by the amount of memory allocated to Java.	jung|is|a|java|api|and|library|that|provides|a|common|and|extensible|language|for|the|modeling|,|analysis|,|and|visualization|of|relational|data|.|it|supports|a|variety|of|graph|types|-lrb-|including|hypergraphs|-rrb-|,|supports|graph|elements|of|any|type|and|with|any|properties|,|enables|customizable|visualizations|,|and|includes|algorithms|from|graph|theory|,|data|mining|,|and|social|network|analysis|-lrb-|e.g.|,|clustering|,|decomposition|,|optimization|,|random|graph|generation|,|statistical|analysis|,|distances|,|flows|,|and|centrality|-lrb-|pagerank|,|hits|,|etc.|-rrb-|-rrb-|.|it|is|limited|only|by|the|amount|of|memory|allocated|to|java|.	JUNG|be|a|Java|API|and|library|that|provide|a|common|and|extensible|language|for|the|modeling|,|analysis|,|and|visualization|of|relational|datum|.|it|support|a|variety|of|graph|type|-lrb-|include|hypergraph|-rrb-|,|support|graph|element|of|any|type|and|with|any|property|,|enable|customizable|visualization|,|and|include|algorithm|from|graph|theory|,|datum|mining|,|and|social|network|analysis|-lrb-|e.g.|,|clustering|,|decomposition|,|optimization|,|random|graph|generation|,|statistical|analysis|,|distance|,|flow|,|and|centrality|-lrb-|pagerank|,|hit|,|etc.|-rrb-|-rrb-|.|it|be|limit|only|by|the|amount|of|memory|allocate|to|Java|.	NNP|VBD-AUX|DT|NNP|NNP|CC|NN|WDT|VBZ|DT|JJ|CC|JJ|NN|IN|DT|NN|,|NN|,|CC|NN|IN|JJ|NNS|.|PRP|VBZ|DT|NN|IN|NN|NNS|-LRB-|VBG|NNS|-RRB-|,|VBZ|NN|NNS|IN|DT|NN|CC|IN|DT|NNS|,|VBZ|JJ|NNS|,|CC|VBZ|NNS|IN|NN|NN|,|NNS|NN|,|CC|JJ|NN|NN|-LRB-|FW|,|NN|,|NN|,|NN|,|JJ|NN|NN|,|JJ|NN|,|NNS|,|VBZ|,|CC|NN|-LRB-|NN|,|VBZ|,|FW|-RRB-|-RRB-|.|PRP|VBD-AUX|VBN|RB|IN|DT|NN|IN|NN|VBN|TO|NNP|.	ORGANIZATION|O|O|MISC|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				JUNG is a Java API and library that provides a common and extensible language for the modeling|analysis|and visualization of relational data. It supports a variety of graph types (including hypergraphs)|supports graph elements of any type and with any properties|enables customizable visualizations|and includes algorithms from graph theory|data mining|and social network analysis (e.g.|clustering|decomposition|optimization|random graph generation|statistical analysis|distances|flows|and centrality (PageRank|HITS|etc.)). It is limited only by the amount of memory allocated to Java.	fb:part.jung_is_a_java_api_and_library_that_provides_a_common_and_extensible_language_for_the_modeling|fb:part.analysis|fb:part.and_visualization_of_relational_data_it_supports_a_variety_of_graph_types_including_hypergraphs|fb:part.supports_graph_elements_of_any_type_and_with_any_properties|fb:part.enables_customizable_visualizations|fb:part.and_includes_algorithms_from_graph_theory|fb:part.data_mining|fb:part.and_social_network_analysis_e_g|fb:part.clustering|fb:part.decomposition|fb:part.optimization|fb:part.random_graph_generation|fb:part.statistical_analysis|fb:part.distances|fb:part.flows|fb:part.and_centrality_pagerank|fb:part.hits|fb:part.etc_it_is_limited_only_by_the_amount_of_memory_allocated_to_java
7	0	fb:cell.mathematica	Mathematica	mathematica	Mathematica	NNP	PERSON					Mathematica	fb:part.mathematica
7	1	fb:cell.graph_analysis_statistics_data_visualization_optimization_image_recognition	Graph analysis, statistics, data visualization, optimization, image recognition.	graph|analysis|,|statistics|,|data|visualization|,|optimization|,|image|recognition|.	graph|analysis|,|statistics|,|datum|visualization|,|optimization|,|image|recognition|.	NN|NN|,|NNS|,|NNS|NN|,|NN|,|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||				Graph analysis|statistics|data visualization|optimization|image recognition.	fb:part.graph_analysis|fb:part.statistics|fb:part.data_visualization|fb:part.optimization|fb:part.image_recognition
7	2	fb:cell.3ds_aco_affymetrix_aiff_apachelog_arcgrid_au_avi_base64_bdf_binary_bit_bmp_byte_byu_bzip2_cded_cdf_character16_character8_cif_complex128_complex256_complex64_csv_cur_dbf_dicom_dif_dimacs_directory_dot_dxf_edf_eps_expressionml_fasta_fits_flac_genbank_geotiff_gif_gpx_graph6_graphlet_graphml_grib_gtopo30_gxl_gzip_harwellboeing_hdf_hdf5_html_ico_ics_integer128_integer16_integer24_integer32_integer64_integer8_jpeg_jpeg2000_json_jvx_kml_latex_leda_list_lwo_mat_mathml_mbox_mdb_mgf_mmcif_mol_mol2_mps_mtp_mtx_mx_nasacdf_nb_ndk_netcdf_nexus_noff_obj_ods_off_package_pajek_pbm_pcx_pdb_pdf_pgm_ply_png_pnm_ppm_pxr_quicktime_rawbitmap_real128_real32_real64_rib_rss_rtf_sct_sdf_sdts_sdtsdem_shp_smiles_snd_sp3_sparse6_stl_string_surfergrid_sxc_table_tar_terminatedstring_text_tga_tgf_tiff_tiger_tle_tsv_usgsdem_uue_vcf_vcs_vtk_wav_wave64_wdx_xbm_xhtml_xhtmlmathml_xls_xlsx_xml_xport_xyz_zip	3DS, ACO, Affymetrix, AIFF, ApacheLog, ArcGRID, AU, AVI, Base64, BDF, Binary, Bit, BMP, Byte, BYU, BZIP2, CDED, CDF, Character16, Character8, CIF, Complex128, Complex256, Complex64, CSV, CUR, DBF, DICOM, DIF, DIMACS, Directory, DOT, DXF, EDF, EPS, ExpressionML, FASTA, FITS, FLAC, GenBank, GeoTIFF, GIF, GPX, Graph6, Graphlet, GraphML, GRIB, GTOPO30, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, ICO, ICS, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LaTeX, LEDA, List, LWO, MAT, MathML, MBOX, MDB, MGF, MMCIF, MOL, MOL2, MPS, MTP, MTX, MX, NASACDF, NB, NDK, NetCDF, NEXUS, NOFF, OBJ, ODS, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RSS, RTF, SCT, SDF, SDTS, SDTSDEM, SHP, SMILES, SND, SP3, Sparse6, STL, String, SurferGrid, SXC, Table, TAR, TerminatedString, Text, TGA, TGF, TIFF, TIGER, TLE, TSV, USGSDEM, UUE, VCF, VCS, VTK, WAV, Wave64, WDX, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XPORT, XYZ, ZIP	3ds|,|aco|,|affymetrix|,|aiff|,|apachelog|,|arcgrid|,|au|,|avi|,|base64|,|bdf|,|binary|,|bit|,|bmp|,|byte|,|byu|,|bzip2|,|cded|,|cdf|,|character16|,|character8|,|cif|,|complex128|,|complex256|,|complex64|,|csv|,|cur|,|dbf|,|dicom|,|dif|,|dimacs|,|directory|,|dot|,|dxf|,|edf|,|eps|,|expressionml|,|fasta|,|fits|,|flac|,|genbank|,|geotiff|,|gif|,|gpx|,|graph6|,|graphlet|,|graphml|,|grib|,|gtopo30|,|gxl|,|gzip|,|harwellboeing|,|hdf|,|hdf5|,|html|,|ico|,|ics|,|integer128|,|integer16|,|integer24|,|integer32|,|integer64|,|integer8|,|jpeg|,|jpeg2000|,|json|,|jvx|,|kml|,|latex|,|leda|,|list|,|lwo|,|mat|,|mathml|,|mbox|,|mdb|,|mgf|,|mmcif|,|mol|,|mol2|,|mps|,|mtp|,|mtx|,|mx|,|nasacdf|,|nb|,|ndk|,|netcdf|,|nexus|,|noff|,|obj|,|ods|,|off|,|package|,|pajek|,|pbm|,|pcx|,|pdb|,|pdf|,|pgm|,|ply|,|png|,|pnm|,|ppm|,|pxr|,|quicktime|,|rawbitmap|,|real128|,|real32|,|real64|,|rib|,|rss|,|rtf|,|sct|,|sdf|,|sdts|,|sdtsdem|,|shp|,|smiles|,|snd|,|sp3|,|sparse6|,|stl|,|string|,|surfergrid|,|sxc|,|table|,|tar|,|terminatedstring|,|text|,|tga|,|tgf|,|tiff|,|tiger|,|tle|,|tsv|,|usgsdem|,|uue|,|vcf|,|vcs|,|vtk|,|wav|,|wave64|,|wdx|,|xbm|,|xhtml|,|xhtmlmathml|,|xls|,|xlsx|,|xml|,|xport|,|xyz|,|zip	3d|,|ACO|,|Affymetrix|,|AIFF|,|ApacheLog|,|ArcGRID|,|au|,|AVI|,|base64|,|bdf|,|Binary|,|bit|,|bmp|,|Byte|,|BYU|,|bzip2|,|cded|,|cdf|,|character16|,|character8|,|cif|,|complex128|,|complex256|,|complex64|,|CSV|,|CUR|,|DBF|,|DICOM|,|DIF|,|DIMACS|,|Directory|,|DOT|,|DXF|,|EDF|,|EPS|,|expressionml|,|fasta|,|fit|,|FLAC|,|genbank|,|geotiff|,|gif|,|gpx|,|graph6|,|graphlet|,|graphml|,|GRIB|,|gtopo30|,|gxl|,|gzip|,|harwellboeing|,|hdf|,|hdf5|,|HTML|,|ICO|,|ic|,|integer128|,|integer16|,|integer24|,|integer32|,|integer64|,|integer8|,|jpeg|,|jpeg2000|,|json|,|jvx|,|kml|,|latex|,|LEDA|,|list|,|lwo|,|MAT|,|MathML|,|MBOX|,|MDB|,|MGF|,|MMCIF|,|MOL|,|mol2|,|MPS|,|MTP|,|MTX|,|MX|,|NASACDF|,|nb|,|ndk|,|netcdf|,|nexus|,|noff|,|obj|,|od|,|off|,|package|,|pajek|,|pbm|,|PCX|,|PDB|,|pdf|,|PGM|,|PLY|,|PNG|,|PNM|,|PPM|,|PXR|,|QuickTime|,|RawBitmap|,|real128|,|real32|,|real64|,|rib|,|rss|,|rtf|,|SCT|,|SDF|,|SDTS|,|SDTSDEM|,|SHP|,|SMILES|,|SND|,|sp3|,|sparse6|,|stl|,|String|,|SurferGrid|,|SXC|,|Table|,|tar|,|terminatedstring|,|text|,|TGA|,|tgf|,|tiff|,|TIGER|,|tle|,|tsv|,|USGSDEM|,|UUE|,|VCF|,|VCS|,|VTK|,|WAV|,|wave64|,|wdx|,|xbm|,|xhtml|,|xhtmlmathml|,|XLS|,|XLSX|,|xml|,|xport|,|XYZ|,|zip	NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|VBZ|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NN|,|NNP|,|NN	O|O|ORGANIZATION|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|ORGANIZATION|O|O|O|ORGANIZATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|ORGANIZATION|O|LOCATION|O|O|O|LOCATION|O|O|O|ORGANIZATION|O|O|O|ORGANIZATION|O|O|O|O|O|LOCATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|LOCATION|O|ORGANIZATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|ORGANIZATION|O|O|O|LOCATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|LOCATION|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O|O|O|LOCATION|O|ORGANIZATION|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	3.0		64.0	3DS|ACO|Affymetrix|AIFF|ApacheLog|ArcGRID|AU|AVI|Base64|BDF|Binary|Bit|BMP|Byte|BYU|BZIP2|CDED|CDF|Character16|Character8|CIF|Complex128|Complex256|Complex64|CSV|CUR|DBF|DICOM|DIF|DIMACS|Directory|DOT|DXF|EDF|EPS|ExpressionML|FASTA|FITS|FLAC|GenBank|GeoTIFF|GIF|GPX|Graph6|Graphlet|GraphML|GRIB|GTOPO30|GXL|GZIP|HarwellBoeing|HDF|HDF5|HTML|ICO|ICS|Integer128|Integer16|Integer24|Integer32|Integer64|Integer8|JPEG|JPEG2000|JSON|JVX|KML|LaTeX|LEDA|List|LWO|MAT|MathML|MBOX|MDB|MGF|MMCIF|MOL|MOL2|MPS|MTP|MTX|MX|NASACDF|NB|NDK|NetCDF|NEXUS|NOFF|OBJ|ODS|OFF|Package|Pajek|PBM|PCX|PDB|PDF|PGM|PLY|PNG|PNM|PPM|PXR|QuickTime|RawBitmap|Real128|Real32|Real64|RIB|RSS|RTF|SCT|SDF|SDTS|SDTSDEM|SHP|SMILES|SND|SP3|Sparse6|STL|String|SurferGrid|SXC|Table|TAR|TerminatedString|Text|TGA|TGF|TIFF|TIGER|TLE|TSV|USGSDEM|UUE|VCF|VCS|VTK|WAV|Wave64|WDX|XBM|XHTML|XHTMLMathML|XLS|XLSX|XML|XPORT|XYZ|ZIP	fb:part.3ds|fb:part.aco|fb:part.affymetrix|fb:part.aiff|fb:part.apachelog|fb:part.arcgrid|fb:part.au|fb:part.avi|fb:part.base64|fb:part.bdf|fb:part.binary|fb:part.bit|fb:part.bmp|fb:part.byte|fb:part.byu|fb:part.bzip2|fb:part.cded|fb:part.cdf|fb:part.character16|fb:part.character8|fb:part.cif|fb:part.complex128|fb:part.complex256|fb:part.complex64|fb:part.csv|fb:part.cur|fb:part.dbf|fb:part.dicom|fb:part.dif|fb:part.dimacs|fb:part.directory|fb:part.dot|fb:part.dxf|fb:part.edf|fb:part.eps|fb:part.expressionml|fb:part.fasta|fb:part.fits|fb:part.flac|fb:part.genbank|fb:part.geotiff|fb:part.gif|fb:part.gpx|fb:part.graph6|fb:part.graphlet|fb:part.graphml|fb:part.grib|fb:part.gtopo30|fb:part.gxl|fb:part.gzip|fb:part.harwellboeing|fb:part.hdf|fb:part.hdf5|fb:part.html|fb:part.ico|fb:part.ics|fb:part.integer128|fb:part.integer16|fb:part.integer24|fb:part.integer32|fb:part.integer64|fb:part.integer8|fb:part.jpeg|fb:part.jpeg2000|fb:part.json|fb:part.jvx|fb:part.kml|fb:part.latex|fb:part.leda|fb:part.list|fb:part.lwo|fb:part.mat|fb:part.mathml|fb:part.mbox|fb:part.mdb|fb:part.mgf|fb:part.mmcif|fb:part.mol|fb:part.mol2|fb:part.mps|fb:part.mtp|fb:part.mtx|fb:part.mx|fb:part.nasacdf|fb:part.nb|fb:part.ndk|fb:part.netcdf|fb:part.nexus|fb:part.noff|fb:part.obj|fb:part.ods|fb:part.off|fb:part.package|fb:part.pajek|fb:part.pbm|fb:part.pcx|fb:part.pdb|fb:part.pdf|fb:part.pgm|fb:part.ply|fb:part.png|fb:part.pnm|fb:part.ppm|fb:part.pxr|fb:part.quicktime|fb:part.rawbitmap|fb:part.real128|fb:part.real32|fb:part.real64|fb:part.rib|fb:part.rss|fb:part.rtf|fb:part.sct|fb:part.sdf|fb:part.sdts|fb:part.sdtsdem|fb:part.shp|fb:part.smiles|fb:part.snd|fb:part.sp3|fb:part.sparse6|fb:part.stl|fb:part.string|fb:part.surfergrid|fb:part.sxc|fb:part.table|fb:part.tar|fb:part.terminatedstring|fb:part.text|fb:part.tga|fb:part.tgf|fb:part.tiff|fb:part.tiger|fb:part.tle|fb:part.tsv|fb:part.usgsdem|fb:part.uue|fb:part.vcf|fb:part.vcs|fb:part.vtk|fb:part.wav|fb:part.wave64|fb:part.wdx|fb:part.xbm|fb:part.xhtml|fb:part.xhtmlmathml|fb:part.xls|fb:part.xlsx|fb:part.xml|fb:part.xport|fb:part.xyz|fb:part.zip
7	3	fb:cell.3ds_aco_aiff_au_avi_base64_binary_bit_bmp_byte_byu_bzip2_c_cdf_character16_character8_complex128_complex256_complex64_csv_dicom_dif_dimacs_dot_dxf_emf_eps_expressionml_fasta_fits_flac_flv_gif_graph6_graphlet_graphml_gxl_gzip_harwellboeing_hdf_hdf5_html_integer128_integer16_integer24_integer32_integer64_integer8_jpeg_jpeg2000_json_jvx_kml_leda_list_lwo_mat_mathml_maya_mgf_midi_mol_mol2_mtx_mx_nasacdf_nb_netcdf_nexus_noff_obj_off_package_pajek_pbm_pcx_pdb_pdf_pgm_ply_png_pnm_pov_ppm_pxr_quicktime_rawbitmap_real128_real32_real64_rib_rtf_sct_sdf_snd_sparse6_stl_string_surfergrid_svg_swf_table_tar_terminatedstring_tex_text_tga_tgf_tiff_tsv_uue_videoframes_vrml_vtk_wav_wave64_wdx_wmf_x3d_xbm_xhtml_xhtmlmathml_xls_xlsx_xml_xyz_zip_zpr	3DS, ACO, AIFF, AU, AVI, Base64, Binary, Bit, BMP, Byte, BYU, BZIP2, C, CDF, Character16, Character8, Complex128, Complex256, Complex64, CSV, DICOM, DIF, DIMACS, DOT, DXF, EMF, EPS, ExpressionML, FASTA, FITS, FLAC, FLV, GIF, Graph6, Graphlet, GraphML, GXL, GZIP, HarwellBoeing, HDF, HDF5, HTML, Integer128, Integer16, Integer24, Integer32, Integer64, Integer8, JPEG, JPEG2000, JSON, JVX, KML, LEDA, List, LWO, MAT, MathML, Maya, MGF, MIDI, MOL, MOL2, MTX, MX, NASACDF, NB, NetCDF, NEXUS, NOFF, OBJ, OFF, Package, Pajek, PBM, PCX, PDB, PDF, PGM, PLY, PNG, PNM, POV, PPM, PXR, QuickTime, RawBitmap, Real128, Real32, Real64, RIB, RTF, SCT, SDF, SND, Sparse6, STL, String, SurferGrid, SVG, SWF, Table, TAR, TerminatedString, TeX, Text, TGA, TGF, TIFF, TSV, UUE, VideoFrames, VRML, VTK, WAV, Wave64, WDX, WMF, X3D, XBM, XHTML, XHTMLMathML, XLS, XLSX, XML, XYZ, ZIP, ZPR	3ds|,|aco|,|aiff|,|au|,|avi|,|base64|,|binary|,|bit|,|bmp|,|byte|,|byu|,|bzip2|,|c|,|cdf|,|character16|,|character8|,|complex128|,|complex256|,|complex64|,|csv|,|dicom|,|dif|,|dimacs|,|dot|,|dxf|,|emf|,|eps|,|expressionml|,|fasta|,|fits|,|flac|,|flv|,|gif|,|graph6|,|graphlet|,|graphml|,|gxl|,|gzip|,|harwellboeing|,|hdf|,|hdf5|,|html|,|integer128|,|integer16|,|integer24|,|integer32|,|integer64|,|integer8|,|jpeg|,|jpeg2000|,|json|,|jvx|,|kml|,|leda|,|list|,|lwo|,|mat|,|mathml|,|maya|,|mgf|,|midi|,|mol|,|mol2|,|mtx|,|mx|,|nasacdf|,|nb|,|netcdf|,|nexus|,|noff|,|obj|,|off|,|package|,|pajek|,|pbm|,|pcx|,|pdb|,|pdf|,|pgm|,|ply|,|png|,|pnm|,|pov|,|ppm|,|pxr|,|quicktime|,|rawbitmap|,|real128|,|real32|,|real64|,|rib|,|rtf|,|sct|,|sdf|,|snd|,|sparse6|,|stl|,|string|,|surfergrid|,|svg|,|swf|,|table|,|tar|,|terminatedstring|,|tex|,|text|,|tga|,|tgf|,|tiff|,|tsv|,|uue|,|videoframes|,|vrml|,|vtk|,|wav|,|wave64|,|wdx|,|wmf|,|x3d|,|xbm|,|xhtml|,|xhtmlmathml|,|xls|,|xlsx|,|xml|,|xyz|,|zip|,|zpr	3d|,|ACO|,|AIFF|,|au|,|AVI|,|base64|,|Binary|,|bit|,|bmp|,|Byte|,|BYU|,|bzip2|,|c|,|cdf|,|character16|,|character8|,|complex128|,|complex256|,|complex64|,|CSV|,|DICOM|,|DIF|,|DIMACS|,|DOT|,|dxf|,|emf|,|ep|,|expressionml|,|fasta|,|fit|,|FLAC|,|flv|,|gif|,|graph6|,|graphlet|,|graphml|,|gxl|,|gzip|,|harwellboeing|,|hdf|,|hdf5|,|HTML|,|integer128|,|integer16|,|integer24|,|integer32|,|integer64|,|integer8|,|jpeg|,|jpeg2000|,|json|,|jvx|,|kml|,|LEDA|,|list|,|lwo|,|mat|,|mathml|,|maya|,|mgf|,|midi|,|MOL|,|mol2|,|MTX|,|MX|,|NASACDF|,|nb|,|netcdf|,|nexus|,|noff|,|obj|,|off|,|package|,|pajek|,|pbm|,|PCX|,|PDB|,|pdf|,|PGM|,|PLY|,|PNG|,|PNM|,|POV|,|PPM|,|PXR|,|QuickTime|,|RawBitmap|,|real128|,|real32|,|real64|,|rib|,|rtf|,|sct|,|sdf|,|snd|,|sparse6|,|stl|,|String|,|SurferGrid|,|SVG|,|SWF|,|Table|,|tar|,|terminatedstring|,|tex|,|text|,|TGA|,|tgf|,|tiff|,|tsv|,|uue|,|VideoFrames|,|VRML|,|VTK|,|WAV|,|wave64|,|wdx|,|WMF|,|x3d|,|xbm|,|xhtml|,|xhtmlmathml|,|XLS|,|XLSX|,|xml|,|XYZ|,|ZIP|,|ZPR	NN|,|NNP|,|NNP|,|NN|,|NNP|,|NN|,|NNP|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|VBZ|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NN|,|NN|,|JJ|,|NN|,|NN|,|NNP|,|NN|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NN|,|NN|,|NNP|,|NN|,|NN|,|NN|,|NN|,|NNP|,|NNP|,|NN|,|NNP|,|NNP|,|NNP	O|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|ORGANIZATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|LOCATION|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	3.0		64.0	3DS|ACO|AIFF|AU|AVI|Base64|Binary|Bit|BMP|Byte|BYU|BZIP2|C|CDF|Character16|Character8|Complex128|Complex256|Complex64|CSV|DICOM|DIF|DIMACS|DOT|DXF|EMF|EPS|ExpressionML|FASTA|FITS|FLAC|FLV|GIF|Graph6|Graphlet|GraphML|GXL|GZIP|HarwellBoeing|HDF|HDF5|HTML|Integer128|Integer16|Integer24|Integer32|Integer64|Integer8|JPEG|JPEG2000|JSON|JVX|KML|LEDA|List|LWO|MAT|MathML|Maya|MGF|MIDI|MOL|MOL2|MTX|MX|NASACDF|NB|NetCDF|NEXUS|NOFF|OBJ|OFF|Package|Pajek|PBM|PCX|PDB|PDF|PGM|PLY|PNG|PNM|POV|PPM|PXR|QuickTime|RawBitmap|Real128|Real32|Real64|RIB|RTF|SCT|SDF|SND|Sparse6|STL|String|SurferGrid|SVG|SWF|Table|TAR|TerminatedString|TeX|Text|TGA|TGF|TIFF|TSV|UUE|VideoFrames|VRML|VTK|WAV|Wave64|WDX|WMF|X3D|XBM|XHTML|XHTMLMathML|XLS|XLSX|XML|XYZ|ZIP|ZPR	fb:part.3ds|fb:part.aco|fb:part.aiff|fb:part.au|fb:part.avi|fb:part.base64|fb:part.binary|fb:part.bit|fb:part.bmp|fb:part.byte|fb:part.byu|fb:part.bzip2|fb:part.c|fb:part.cdf|fb:part.character16|fb:part.character8|fb:part.complex128|fb:part.complex256|fb:part.complex64|fb:part.csv|fb:part.dicom|fb:part.dif|fb:part.dimacs|fb:part.dot|fb:part.dxf|fb:part.emf|fb:part.eps|fb:part.expressionml|fb:part.fasta|fb:part.fits|fb:part.flac|fb:part.flv|fb:part.gif|fb:part.graph6|fb:part.graphlet|fb:part.graphml|fb:part.gxl|fb:part.gzip|fb:part.harwellboeing|fb:part.hdf|fb:part.hdf5|fb:part.html|fb:part.integer128|fb:part.integer16|fb:part.integer24|fb:part.integer32|fb:part.integer64|fb:part.integer8|fb:part.jpeg|fb:part.jpeg2000|fb:part.json|fb:part.jvx|fb:part.kml|fb:part.leda|fb:part.list|fb:part.lwo|fb:part.mat|fb:part.mathml|fb:part.maya|fb:part.mgf|fb:part.midi|fb:part.mol|fb:part.mol2|fb:part.mtx|fb:part.mx|fb:part.nasacdf|fb:part.nb|fb:part.netcdf|fb:part.nexus|fb:part.noff|fb:part.obj|fb:part.off|fb:part.package|fb:part.pajek|fb:part.pbm|fb:part.pcx|fb:part.pdb|fb:part.pdf|fb:part.pgm|fb:part.ply|fb:part.png|fb:part.pnm|fb:part.pov|fb:part.ppm|fb:part.pxr|fb:part.quicktime|fb:part.rawbitmap|fb:part.real128|fb:part.real32|fb:part.real64|fb:part.rib|fb:part.rtf|fb:part.sct|fb:part.sdf|fb:part.snd|fb:part.sparse6|fb:part.stl|fb:part.string|fb:part.surfergrid|fb:part.svg|fb:part.swf|fb:part.table|fb:part.tar|fb:part.terminatedstring|fb:part.tex|fb:part.text|fb:part.tga|fb:part.tgf|fb:part.tiff|fb:part.tsv|fb:part.uue|fb:part.videoframes|fb:part.vrml|fb:part.vtk|fb:part.wav|fb:part.wave64|fb:part.wdx|fb:part.wmf|fb:part.x3d|fb:part.xbm|fb:part.xhtml|fb:part.xhtmlmathml|fb:part.xls|fb:part.xlsx|fb:part.xml|fb:part.xyz|fb:part.zip|fb:part.zpr
7	4	fb:cell.windows_macintosh_linux	Windows, Macintosh, Linux	windows|,|macintosh|,|linux	Windows|,|Macintosh|,|Linux	NNP|,|NNP|,|NNP	MISC|O|O|O|O	||||				Windows|Macintosh|Linux	fb:part.windows|fb:part.macintosh|fb:part.linux
7	5	fb:cell.commercial	Commercial	commercial	commercial	NN	O					Commercial	fb:part.commercial
7	6	fb:cell.mathematica_is_a_general_purpose_computation_and_analysis_environment	Mathematica is a general purpose computation and analysis environment.	mathematica|is|a|general|purpose|computation|and|analysis|environment|.	Mathematica|be|a|general|purpose|computation|and|analysis|environment|.	NNP|VBD-AUX|DT|JJ|NN|NN|CC|NN|NN|.	PERSON|O|O|O|O|O|O|O|O|O	|||||||||				Mathematica is a general purpose computation and analysis environment.	fb:part.mathematica_is_a_general_purpose_computation_and_analysis_environment
8	0	fb:cell.netlytic	Netlytic	netlytic	netlytic	JJ	LOCATION					Netlytic	fb:part.netlytic
8	1	fb:cell.cloud_based_text_social_network_analyzer	Cloud based text & social network analyzer	cloud|based|text|&|social|network|analyzer	cloud|base|text|&|social|network|analyzer	NN|VBN|NN|CC|JJ|NN|NN	O|O|O|O|O|O|O	||||||				Cloud based text & social network analyzer	fb:part.cloud_based_text_social_network_analyzer
8	2	fb:cell.rss_google_drive_twitter_youtube_comments_csv_txt	RSS, Google Drive, Twitter, YouTube comments, .csv, .txt	rss|,|google|drive|,|twitter|,|youtube|comments|,|.|csv|,|.|txt	rss|,|Google|Drive|,|Twitter|,|YouTube|comment|,|.|csv|,|.|txt	NN|,|NNP|NNP|,|NNP|,|NNP|NNS|,|.|NN|,|.|NN	O|O|ORGANIZATION|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O	||||||||||||||				RSS|Google Drive|Twitter|YouTube comments|.csv|.txt	fb:part.rss|fb:part.google_drive|fb:part.twitter|fb:part.youtube_comments|fb:part._csv|fb:part._txt
8	3	fb:cell._csv_mds_dl_ucinet_net_pajek	.csv, .mds, .dl (UCINET), .net (Pajek)	.|csv|,|.|mds|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-	.|csv|,|.|md|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-	.|NN|,|.|NNS|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||				.csv|.mds|.dl (UCINET)|.net (Pajek)	fb:part._csv|fb:part._mds|fb:part._dl_ucinet|fb:part._net_pajek
8	4	fb:cell.windows_linux_mac	Windows, Linux, Mac	windows|,|linux|,|mac	Windows|,|Linux|,|Mac	NNP|,|NNP|,|NNP	MISC|O|O|O|O	||||				Windows|Linux|Mac	fb:part.windows|fb:part.linux|fb:part.mac
8	5	fb:cell.freemium	Freemium	freemium	freemium	NN	O					Freemium	fb:part.freemium
8	6	fb:cell.netlytic_allows_users_to_automatically_summarize_large_volumes_of_text_discover_social_networks_from_conversations_on_social_media_such_as_twitter_youtube_blogs_online_forums_chats_netlytic_can_automatically_build_chain_networks_personal_name_networks_based_on_who_replies_to_whom_who_mentioned_whom	Netlytic allows users to automatically summarize large volumes of text & discover social networks from conversations on social media such as Twitter, YouTube, blogs, online forums & chats. Netlytic can automatically build chain networks & personal name networks, based on who replies to whom & who mentioned whom.	netlytic|allows|users|to|automatically|summarize|large|volumes|of|text|&|discover|social|networks|from|conversations|on|social|media|such|as|twitter|,|youtube|,|blogs|,|online|forums|&|chats|.|netlytic|can|automatically|build|chain|networks|&|personal|name|networks|,|based|on|who|replies|to|whom|&|who|mentioned|whom|.	netlytic|allow|user|to|automatically|summarize|large|volume|of|text|&|discover|social|network|from|conversation|on|social|media|such|as|Twitter|,|YouTube|,|blog|,|online|forum|&|chat|.|Netlytic|can|automatically|build|chain|network|&|personal|name|network|,|base|on|who|reply|to|whom|&|who|mention|whom|.	JJ|VBZ|NNS|TO|RB|VB|JJ|NNS|IN|NN|CC|VBP|JJ|NNS|IN|NNS|IN|JJ|NNS|JJ|IN|NNP|,|NNP|,|NNS|,|JJ|NNS|CC|NNS|.|NNP|VBD-AUX|RB|VB|NN|NNS|CC|JJ|NN|NNS|,|VBN|IN|WP|VBZ|TO|WP|CC|WP|VBD|WP|.	PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||||||||||||||||				Netlytic allows users to automatically summarize large volumes of text & discover social networks from conversations on social media such as Twitter|YouTube|blogs|online forums & chats. Netlytic can automatically build chain networks & personal name networks|based on who replies to whom & who mentioned whom.	fb:part.netlytic_allows_users_to_automatically_summarize_large_volumes_of_text_discover_social_networks_from_conversations_on_social_media_such_as_twitter|fb:part.youtube|fb:part.blogs|fb:part.online_forums_chats_netlytic_can_automatically_build_chain_networks_personal_name_networks|fb:part.based_on_who_replies_to_whom_who_mentioned_whom
9	0	fb:cell.network_overview_discovery_exploration_for_excel_nodexl	Network Overview Discovery Exploration for Excel (NodeXL)	network|overview|discovery|exploration|for|excel|-lrb-|nodexl|-rrb-	Network|Overview|Discovery|Exploration|for|Excel|-lrb-|NodeXL|-rrb-	NNP|NNP|NNP|NNP|IN|NNP|-LRB-|NNP|-RRB-	O|O|O|O|O|MISC|O|O|O	||||||||				Network Overview Discovery Exploration for Excel (NodeXL)	fb:part.network_overview_discovery_exploration_for_excel_nodexl
9	1	fb:cell.network_overview_discovery_and_exploration	Network overview, discovery and exploration	network|overview|,|discovery|and|exploration	Network|overview|,|discovery|and|exploration	NNP|NN|,|NN|CC|NN	O|O|O|O|O|O	|||||				Network overview|discovery and exploration	fb:part.network_overview|fb:part.discovery_and_exploration
9	2	fb:cell.email_csv_text_txt_xls_excel_xslt_excel_2007_2010_2013_net_pajek_dl_ucinet_graphml	email, .csv (text), .txt, .xls (Excel), .xslt (Excel 2007, 2010, 2013), .net (Pajek), .dl (UCINet), GraphML	email|,|.|csv|-lrb-|text|-rrb-|,|.|txt|,|.|xls|-lrb-|excel|-rrb-|,|.|xslt|-lrb-|excel|2007|,|2010|,|2013|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|graphml	email|,|.|csv|-lrb-|text|-rrb-|,|.|txt|,|.|xl|-lrb-|Excel|-rrb-|,|.|xslt|-lrb-|Excel|2007|,|2010|,|2013|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|graphml	NN|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|,|.|NNS|-LRB-|NNP|-RRB-|,|.|NN|-LRB-|NNP|CD|,|CD|,|CD|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|MISC|DATE|DATE|DATE|DATE|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||2007|2007|2007|2007|2007|||||||||||||||				email|.csv (text)|.txt|.xls (Excel)|.xslt (Excel 2007|2010|2013)|.net (Pajek)|.dl (UCINet)|GraphML	fb:part.email|fb:part._csv_text|fb:part._txt|fb:part._xls_excel|fb:part._xslt_excel_2007|fb:part.2010|fb:part.2013|fb:part._net_pajek|fb:part._dl_ucinet|fb:part.graphml
9	3	fb:cell._csv_text_txt_xls_excel_xslt_excel_2007_dl_ucinet_graphml	.csv (text), .txt, .xls (Excel), .xslt (Excel 2007), .dl (UCINet), GraphML	.|csv|-lrb-|text|-rrb-|,|.|txt|,|.|xls|-lrb-|excel|-rrb-|,|.|xslt|-lrb-|excel|2007|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|graphml	.|csv|-lrb-|text|-rrb-|,|.|txt|,|.|xl|-lrb-|Excel|-rrb-|,|.|xslt|-lrb-|Excel|2007|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|graphml	.|NN|-LRB-|NN|-RRB-|,|.|NN|,|.|NNS|-LRB-|NNP|-RRB-|,|.|NN|-LRB-|NNP|CD|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|NN	O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|MISC|DATE|O|O|O|O|O|O|O|O|O	|||||||||||||||||||2007|||||||||				.csv (text)|.txt|.xls (Excel)|.xslt (Excel 2007)|.dl (UCINet)|GraphML	fb:part._csv_text|fb:part._txt|fb:part._xls_excel|fb:part._xslt_excel_2007_2|fb:part._dl_ucinet|fb:part.graphml
9	4	fb:cell.windows_xp_vista_7	Windows XP/Vista/7	windows|xp\\/vista\\/7	Windows|xp\\/vista\\/7	NNP|NN	MISC|MISC	|	7.0			Windows XP|Vista|7	fb:part.windows_xp|fb:part.vista|fb:part.7
9	5	fb:cell.free_ms_pl	Free (Ms-PL)	free|-lrb-|ms|pl|-rrb-	Free|-lrb-|Ms|pl|-rrb-	NNP|-LRB-|NNP|NN|-RRB-	O|O|O|O|O	||||				Free (Ms-PL)	fb:part.free_ms_pl
9	6	fb:cell.nodexl_is_a_free_and_open_excel_2007_2010_2013_add_in_and_c_net_library_for_network_analysis_and_visualization_it_integrates_into_excel_2007_2010_2013_and_adds_directed_graph_as_a_chart_type_to_the_spreadsheet_and_calculates_a_core_set_of_network_metrics_and_scores_supports_extracting_email_twitter_youtube_facebook_www_wiki_and_flickr_social_networks_accepts_edge_lists_and_matrix_representations_of_graphs_allows_for_easy_and_automated_manipulation_and_filtering_of_underlying_data_in_spreadsheet_format_multiple_network_visualization_layouts_reads_and_writes_pajek_ucinet_and_graphml_files	NodeXL is a free and open Excel 2007, 2010, 2013 Add-in and C#/.Net library for network analysis and visualization. It integrates into Excel 2007, 2010, 2013 and adds directed graph as a chart type to the spreadsheet and calculates a core set of network metrics and scores. Supports extracting email, Twitter, YouTube, Facebook, WWW, Wiki and flickr social networks. Accepts edge lists and matrix representations of graphs. Allows for easy and automated manipulation and filtering of underlying data in spreadsheet format. Multiple network visualization layouts. Reads and writes Pajek, UCINet and GraphML files.	nodexl|is|a|free|and|open|excel|2007|,|2010|,|2013|add|in|and|c|#|\\/|.|net|library|for|network|analysis|and|visualization|.|it|integrates|into|excel|2007|,|2010|,|2013|and|adds|directed|graph|as|a|chart|type|to|the|spreadsheet|and|calculates|a|core|set|of|network|metrics|and|scores|.|supports|extracting|email|,|twitter|,|youtube|,|facebook|,|www|,|wiki|and|flickr|social|networks|.|accepts|edge|lists|and|matrix|representations|of|graphs|.|allows|for|easy|and|automated|manipulation|and|filtering|of|underlying|data|in|spreadsheet|format|.|multiple|network|visualization|layouts|.|reads|and|writes|pajek|,|ucinet|and|graphml|files|.	nodexl|be|a|free|and|open|Excel|2007|,|2010|,|2013|add|in|and|c|#|\\/|.|net|library|for|network|analysis|and|visualization|.|it|integrate|into|Excel|2007|,|2010|,|2013|and|add|direct|graph|as|a|chart|type|to|the|spreadsheet|and|calculate|a|core|set|of|network|metric|and|score|.|support|extract|email|,|Twitter|,|YouTube|,|Facebook|,|WWW|,|Wiki|and|flickr|social|network|.|accept|edge|list|and|matrix|representation|of|graph|.|allow|for|easy|and|automated|manipulation|and|filter|of|underlie|datum|in|spreadsheet|format|.|multiple|network|visualization|layout|.|read|and|write|Pajek|,|UCINet|and|GraphML|file|.	NN|VBD-AUX|DT|JJ|CC|JJ|NNP|CD|,|CD|,|CD|VB|IN|CC|NN|#|:|.|JJ|NN|IN|NN|NN|CC|NN|.|PRP|VBZ|IN|NNP|CD|,|CD|,|CD|CC|VBZ|VBN|NN|IN|DT|NN|NN|TO|DT|NN|CC|VBZ|DT|NN|NN|IN|NN|NNS|CC|NNS|.|VBZ|VBG|NN|,|NNP|,|NNP|,|NNP|,|NNP|,|NNP|CC|NN|JJ|NNS|.|VBZ|NN|NNS|CC|NN|NNS|IN|NNS|.|VBZ|IN|JJ|CC|JJ|NN|CC|VBG|IN|VBG|NNS|IN|NN|NN|.|JJ|NN|NN|NNS|.|NNS|CC|VBZ|NNP|,|NNP|CC|NNP|NNS|.	O|O|O|O|O|O|MISC|DATE|DATE|DATE|DATE|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|DATE|DATE|DATE|DATE|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O	|||||||2007|2007|2007|2007|2007||||||||||||||||||||2007|2007|2007|2007|2007|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||	2.00720102013E11			NodeXL is a free and open Excel 2007|2010|2013 Add-in and C#|.Net library for network analysis and visualization. It integrates into Excel 2007|2010|2013 and adds directed graph as a chart type to the spreadsheet and calculates a core set of network metrics and scores. Supports extracting email|Twitter|YouTube|Facebook|WWW|Wiki and flickr social networks. Accepts edge lists and matrix representations of graphs. Allows for easy and automated manipulation and filtering of underlying data in spreadsheet format. Multiple network visualization layouts. Reads and writes Pajek|UCINet and GraphML files.	fb:part.nodexl_is_a_free_and_open_excel_2007|fb:part.2010|fb:part.2013_add_in_and_c|fb:part._net_library_for_network_analysis_and_visualization_it_integrates_into_excel_2007|fb:part.2010|fb:part.2013_and_adds_directed_graph_as_a_chart_type_to_the_spreadsheet_and_calculates_a_core_set_of_network_metrics_and_scores_supports_extracting_email|fb:part.twitter|fb:part.youtube|fb:part.facebook|fb:part.www|fb:part.wiki_and_flickr_social_networks_accepts_edge_lists_and_matrix_representations_of_graphs_allows_for_easy_and_automated_manipulation_and_filtering_of_underlying_data_in_spreadsheet_format_multiple_network_visualization_layouts_reads_and_writes_pajek|fb:part.ucinet_and_graphml_files
10	0	fb:cell.netminer_4	NetMiner 4	netminer|4	NetMiner|4	NNP|CD	O|NUMBER	|4.0	4.0			NetMiner 4	fb:part.netminer_4
10	1	fb:cell.all_in_one_software_for_network_analysis_and_visualization	All-in-one Software for Network Analysis and Visualization	all|in|one|software|for|network|analysis|and|visualization	all|in|one|Software|for|Network|analysis|and|visualization	DT|IN|CD|NNP|IN|NNP|NN|CC|NN	O|O|NUMBER|O|O|O|O|O|O	||1.0||||||				All-in-one Software for Network Analysis and Visualization	fb:part.all_in_one_software_for_network_analysis_and_visualization
10	2	fb:cell._xls_excel_xlsx_excel_2007_csv_text_dl_ucinet_net_pajek_dat_stocnet_gml_nmf_proprietary	.xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), .gml; NMF(proprietary)	.|xls|-lrb-|excel|-rrb-|,|.|xlsx|-lrb-|excel|2007|-rrb-|,|.|csv|-lrb-|text|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dat|-lrb-|stocnet|-rrb-|,|.|gml|;|nmf|-lrb-|proprietary|-rrb-	.|xl|-lrb-|Excel|-rrb-|,|.|xlsx|-lrb-|Excel|2007|-rrb-|,|.|csv|-lrb-|text|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dat|-lrb-|stocnet|-rrb-|,|.|gml|;|nmf|-lrb-|proprietary|-rrb-	.|NNS|-LRB-|NNP|-RRB-|,|.|NN|-LRB-|NNP|CD|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|:|NN|-LRB-|NN|-RRB-	O|O|O|MISC|O|O|O|O|O|MISC|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||2007|||||||||||||||||||||||||||||||||				.xls(Excel),.xlsx (Excel 2007)|.csv(text)|.dl(UCINET)|.net(Pajek)|.dat(StOCNET)|.gml; NMF(proprietary)	fb:part._xls_excel_xlsx_excel_2007|fb:part._csv_text_2|fb:part._dl_ucinet_2|fb:part._net_pajek_2|fb:part._dat_stocnet|fb:part._gml_nmf_proprietary
10	3	fb:cell._xls_excel_xlsx_excel_2007_csv_text_dl_ucinet_net_pajek_dat_stocnet_nmf_proprietary	.xls(Excel),.xlsx (Excel 2007), .csv(text), .dl(UCINET), .net(Pajek), .dat(StOCNET), NMF(proprietary)	.|xls|-lrb-|excel|-rrb-|,|.|xlsx|-lrb-|excel|2007|-rrb-|,|.|csv|-lrb-|text|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dat|-lrb-|stocnet|-rrb-|,|nmf|-lrb-|proprietary|-rrb-	.|xl|-lrb-|Excel|-rrb-|,|.|xlsx|-lrb-|Excel|2007|-rrb-|,|.|csv|-lrb-|text|-rrb-|,|.|dl|-lrb-|ucinet|-rrb-|,|.|net|-lrb-|pajek|-rrb-|,|.|dat|-lrb-|stocnet|-rrb-|,|nmf|-lrb-|proprietary|-rrb-	.|NNS|-LRB-|NNP|-RRB-|,|.|NN|-LRB-|NNP|CD|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|.|NN|-LRB-|NN|-RRB-|,|NN|-LRB-|NN|-RRB-	O|O|O|MISC|O|O|O|O|O|MISC|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||2007||||||||||||||||||||||||||||||				.xls(Excel),.xlsx (Excel 2007)|.csv(text)|.dl(UCINET)|.net(Pajek)|.dat(StOCNET)|NMF(proprietary)	fb:part._xls_excel_xlsx_excel_2007|fb:part._csv_text_2|fb:part._dl_ucinet_2|fb:part._net_pajek_2|fb:part._dat_stocnet|fb:part.nmf_proprietary
10	4	fb:cell.microsoft_windows	Microsoft Windows	microsoft|windows	Microsoft|Windows	NNP|NNP	ORGANIZATION|O	|				Microsoft Windows	fb:part.microsoft_windows
10	5	fb:cell.commercial_with_free_trial	Commercial with free trial	commercial|with|free|trial	commercial|with|free|trial	JJ|IN|JJ|NN	O|O|O|O	|||				Commercial with free trial	fb:part.commercial_with_free_trial
10	6	fb:cell.netminer_is_a_software_tool_for_exploratory_analysis_and_visualization_of_large_network_data_netminer_4_embed_internal_python_based_script_engine_which_equipped_with_the_automatic_script_generator_for_unskilled_users_then_the_users_can_operate_netminer_4_with_existing_gui_or_programmable_script_language_main_features_include_analysis_of_large_networks_10_000_000_nodes_comprehensive_network_measures_and_models_both_exploratory_confirmatory_analysis_interactive_visual_analytics_what_if_network_analysis_built_in_statistical_procedures_and_charts_full_documentation_1_000_pages_of_user_s_manual_expressive_network_data_model_facilities_for_data_workflow_management_python_based_script_workbench_and_user_friendliness	NetMiner is a software tool for exploratory analysis and visualization of large network data. NetMiner 4 embed internal Python-based script engine which equipped with the automatic Script Generator for unskilled users. Then the users can operate NetMiner 4 with existing GUI or programmable script language.\nMain features include : analysis of large networks(+10,000,000 nodes), comprehensive network measures and models, both exploratory & confirmatory analysis, interactive visual analytics, what-if network analysis, built-in statistical procedures and charts, full documentation(1,000+ pages of User's Manual), expressive network data model, facilities for data & workflow management, Python-based Script workbench and user-friendliness.	netminer|is|a|software|tool|for|exploratory|analysis|and|visualization|of|large|network|data|.|netminer|4|embed|internal|python|based|script|engine|which|equipped|with|the|automatic|script|generator|for|unskilled|users|.|then|the|users|can|operate|netminer|4|with|existing|gui|or|programmable|script|language|.|main|features|include|:|analysis|of|large|networks|-lrb-|+10,000,000|nodes|-rrb-|,|comprehensive|network|measures|and|models|,|both|exploratory|&|confirmatory|analysis|,|interactive|visual|analytics|,|what|if|network|analysis|,|built|in|statistical|procedures|and|charts|,|full|documentation|-lrb-|1,000|+|pages|of|user|'s|manual|-rrb-|,|expressive|network|data|model|,|facilities|for|data|&|workflow|management|,|python|based|script|workbench|and|user|friendliness|.	NetMiner|be|a|software|tool|for|exploratory|analysis|and|visualization|of|large|network|datum|.|NetMiner|4|embed|internal|Python|base|script|engine|which|equip|with|the|automatic|Script|Generator|for|unskilled|user|.|then|the|user|can|operate|NetMiner|4|with|exist|GUI|or|programmable|script|language|.|Main|feature|include|:|analysis|of|large|network|-lrb-|+10,000,000|node|-rrb-|,|comprehensive|network|measure|and|model|,|both|exploratory|&|confirmatory|analysis|,|interactive|visual|analytic|,|what|if|network|analysis|,|build|in|statistical|procedure|and|chart|,|full|documentation|-lrb-|1,000|+|page|of|user|'s|Manual|-rrb-|,|expressive|network|datum|model|,|facility|for|datum|&|workflow|management|,|Python|base|Script|workbench|and|user|friendliness|.	NNP|VBD-AUX|DT|NN|NN|IN|JJ|NN|CC|NN|IN|JJ|NN|NNS|.|NNP|CD|VBD|JJ|NNP|VBN|NN|NN|WDT|VBD|IN|DT|JJ|NNP|NNP|IN|JJ|NNS|.|RB|DT|NNS|VBD-AUX|VB|NNP|CD|IN|VBG|NNP|CC|JJ|NN|NN|.|NNP|NNS|VBP|:|NN|IN|JJ|NNS|-LRB-|CD|NNS|-RRB-|,|JJ|NN|NNS|CC|NNS|,|DT|NN|CC|JJ|NN|,|JJ|JJ|NNS|,|WP|IN|NN|NN|,|VBN|IN|JJ|NNS|CC|NNS|,|JJ|NN|-LRB-|CD|CC|NNS|IN|NN|POS|NNP|-RRB-|,|JJ|NN|NNS|NN|,|NNS|IN|NNS|CC|NN|NN|,|NNP|VBN|NNP|NN|CC|NN|NN|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||4.0||||||||||||||||||||||||4.0||||||||||||||||||1.0E7|||||||||||||||||||||||||||||||||||1000.0||||||||||||||||||||||||||||				NetMiner is a software tool for exploratory analysis and visualization of large network data. NetMiner 4 embed internal Python-based script engine which equipped with the automatic Script Generator for unskilled users. Then the users can operate NetMiner 4 with existing GUI or programmable script language.|Main features include : analysis of large networks(+10,000,000 nodes)|comprehensive network measures and models|both exploratory & confirmatory analysis|interactive visual analytics|what-if network analysis|built-in statistical procedures and charts|full documentation(1,000+ pages of User's Manual)|expressive network data model|facilities for data & workflow management|Python-based Script workbench and user-friendliness.	fb:part.netminer_is_a_software_tool_for_exploratory_analysis_and_visualization_of_large_network_data_netminer_4_embed_internal_python_based_script_engine_which_equipped_with_the_automatic_script_generator_for_unskilled_users_then_the_users_can_operate_netminer_4_with_existing_gui_or_programmable_script_language|fb:part.main_features_include_analysis_of_large_networks_10_000_000_nodes|fb:part.comprehensive_network_measures_and_models|fb:part.both_exploratory_confirmatory_analysis|fb:part.interactive_visual_analytics|fb:part.what_if_network_analysis|fb:part.built_in_statistical_procedures_and_charts|fb:part.full_documentation_1_000_pages_of_user_s_manual|fb:part.expressive_network_data_model|fb:part.facilities_for_data_workflow_management|fb:part.python_based_script_workbench_and_user_friendliness
11	0	fb:cell.networkx	NetworkX	networkx	networkx	NN	O					NetworkX	fb:part.networkx
11	1	fb:cell.python_package_for_the_creation_manipulation_and_study_of_the_structure_dynamics_and_functions_of_complex_networks	Python package for the creation, manipulation, and study of the structure, dynamics, and functions of complex networks.	python|package|for|the|creation|,|manipulation|,|and|study|of|the|structure|,|dynamics|,|and|functions|of|complex|networks|.	Python|package|for|the|creation|,|manipulation|,|and|study|of|the|structure|,|dynamics|,|and|function|of|complex|network|.	NNP|NN|IN|DT|NN|,|NN|,|CC|NN|IN|DT|NN|,|NNS|,|CC|NNS|IN|NN|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||				Python package for the creation|manipulation|and study of the structure|dynamics|and functions of complex networks.	fb:part.python_package_for_the_creation|fb:part.manipulation|fb:part.and_study_of_the_structure|fb:part.dynamics|fb:part.and_functions_of_complex_networks
11	2	fb:cell.gml_graph6_sparse6_graphml_graphviz_dot_networkx_yaml_adjacency_lists_and_edge_lists_pajek_net_leda	GML, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA	gml|,|graph6\\/sparse6|,|graphml|,|graphviz|-lrb-|.|dot|-rrb-|,|networkx|-lrb-|.|yaml|,|adjacency|lists|,|and|edge|lists|-rrb-|,|pajek|-lrb-|.|net|-rrb-|,|leda	gml|,|graph6\\/sparse6|,|graphml|,|graphviz|-lrb-|.|dot|-rrb-|,|networkx|-lrb-|.|yaml|,|adjacency|list|,|and|edge|list|-rrb-|,|pajek|-lrb-|.|net|-rrb-|,|leda	NN|,|NN|,|NN|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|NN|NNS|,|CC|NN|NNS|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|ORGANIZATION	|||||||||||||||||||||||||||||||	6.0		6.0	GML|Graph6|Sparse6|GraphML|GraphViz (.dot)|NetworkX (.yaml|adjacency lists|and edge lists)|Pajek (.net)|LEDA	fb:part.gml|fb:part.graph6|fb:part.sparse6|fb:part.graphml|fb:part.graphviz_dot_2|fb:part.networkx_yaml|fb:part.adjacency_lists|fb:part.and_edge_lists|fb:part.pajek_net_2|fb:part.leda
11	3	fb:cell.gml_gnome_dia_graph6_sparse6_graphml_graphviz_dot_networkx_yaml_adjacency_lists_and_edge_lists_pajek_net_leda_and_assorted_image_formats_jpg_png_ps_svg_et_al	GML, Gnome Dia, Graph6/Sparse6, GraphML, GraphViz (.dot), NetworkX (.yaml, adjacency lists, and edge lists), Pajek (.net), LEDA, and assorted image formats (.jpg, .png, .ps, .svg, et al.)	gml|,|gnome|dia|,|graph6\\/sparse6|,|graphml|,|graphviz|-lrb-|.|dot|-rrb-|,|networkx|-lrb-|.|yaml|,|adjacency|lists|,|and|edge|lists|-rrb-|,|pajek|-lrb-|.|net|-rrb-|,|leda|,|and|assorted|image|formats|-lrb-|.|jpg|,|.|png|,|.|ps|,|.|svg|,|et|al.|.|-rrb-	gml|,|Gnome|Dia|,|graph6\\/sparse6|,|graphml|,|graphviz|-lrb-|.|dot|-rrb-|,|networkx|-lrb-|.|yaml|,|adjacency|list|,|and|edge|list|-rrb-|,|pajek|-lrb-|.|net|-rrb-|,|LEDA|,|and|assorted|image|format|-lrb-|.|jpg|,|.|png|,|.|p|,|.|svg|,|et|al.|.|-rrb-	NN|,|NNP|NNP|,|NN|,|NN|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|NN|NNS|,|CC|NN|NNS|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NNP|,|CC|JJ|NN|NNS|-LRB-|.|NN|,|.|NN|,|.|NNS|,|.|NN|,|FW|FW|.|-RRB-	O|O|ORGANIZATION|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||	6.0		6.0	GML|Gnome Dia|Graph6|Sparse6|GraphML|GraphViz (.dot)|NetworkX (.yaml|adjacency lists|and edge lists)|Pajek (.net)|LEDA|and assorted image formats (.jpg|.png|.ps|.svg|et al.)	fb:part.gml|fb:part.gnome_dia|fb:part.graph6|fb:part.sparse6|fb:part.graphml|fb:part.graphviz_dot_2|fb:part.networkx_yaml|fb:part.adjacency_lists|fb:part.and_edge_lists|fb:part.pajek_net_2|fb:part.leda|fb:part.and_assorted_image_formats_jpg|fb:part._png|fb:part._ps|fb:part._svg|fb:part.et_al
11	4	fb:cell.open_source_gpl_and_similar	Open source (GPL and similar)	open|source|-lrb-|gpl|and|similar|-rrb-	open|source|-lrb-|gpl|and|similar|-rrb-	VB|NN|-LRB-|NN|CC|JJ|-RRB-	O|O|O|O|O|O|O	||||||				Open source (GPL and similar)	fb:part.open_source_gpl_and_similar
11	5	fb:cell.free	Free	free	Free	NNP	O					Free	fb:part.free
11	6	fb:cell.networkx_nx_is_a_toolset_for_graph_creation_manipulation_analysis_and_visualization_user_interface_is_through_scripting_command_line_provided_by_python_nx_includes_a_several_algorithms_metrics_and_graph_generators_visualization_is_provided_through_pylab_and_graphviz_nx_is_an_open_source_project_in_active_development_since_2004_with_an_open_bug_tracking_site_and_user_forums_development_is_sponsored_by_los_alamos_national_lab	NetworkX (NX) is a toolset for graph creation, manipulation, analysis, and visualization. User interface is through scripting/command-line provided by Python. NX includes a several algorithms, metrics and graph generators. Visualization is provided through pylab and graphviz.\nNX is an open-source project, in active development since 2004 with an open bug-tracking site, and user forums. Development is sponsored by Los Alamos National Lab.	networkx|-lrb-|nx|-rrb-|is|a|toolset|for|graph|creation|,|manipulation|,|analysis|,|and|visualization|.|user|interface|is|through|scripting\\/command|line|provided|by|python|.|nx|includes|a|several|algorithms|,|metrics|and|graph|generators|.|visualization|is|provided|through|pylab|and|graphviz|.|nx|is|an|open|source|project|,|in|active|development|since|2004|with|an|open|bug|tracking|site|,|and|user|forums|.|development|is|sponsored|by|los|alamos|national|lab|.	networkx|-lrb-|nx|-rrb-|be|a|toolset|for|graph|creation|,|manipulation|,|analysis|,|and|visualization|.|user|interface|be|through|scripting\\/command|line|provide|by|Python|.|nx|include|a|several|algorithm|,|metric|and|graph|generator|.|visualization|be|provide|through|pylab|and|graphviz|.|NX|be|a|open|source|project|,|in|active|development|since|2004|with|a|open|bug|tracking|site|,|and|user|forum|.|Development|be|sponsor|by|Los|Alamos|National|Lab|.	NN|-LRB-|NN|-RRB-|VBD-AUX|DT|NN|IN|NN|NN|,|NN|,|NN|,|CC|NN|.|NN|NN|VBD-AUX|IN|NN|NN|VBN|IN|NNP|.|NN|VBZ|DT|JJ|NNS|,|NNS|CC|NN|NNS|.|NN|VBD-AUX|VBN|IN|NN|CC|NN|.|NNP|VBD-AUX|DT|JJ|NN|NN|,|IN|JJ|NN|IN|CD|IN|DT|JJ|NN|NN|NN|,|CC|NN|NNS|.|NNP|VBD-AUX|VBN|IN|NNP|NNP|NNP|NNP|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|DATE|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|ORGANIZATION|ORGANIZATION|ORGANIZATION|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||2004||||||||||||||||||||				NetworkX (NX) is a toolset for graph creation|manipulation|analysis|and visualization. User interface is through scripting|command-line provided by Python. NX includes a several algorithms|metrics and graph generators. Visualization is provided through pylab and graphviz.|NX is an open-source project|in active development since 2004 with an open bug-tracking site|and user forums. Development is sponsored by Los Alamos National Lab.	fb:part.networkx_nx_is_a_toolset_for_graph_creation|fb:part.manipulation|fb:part.analysis|fb:part.and_visualization_user_interface_is_through_scripting|fb:part.command_line_provided_by_python_nx_includes_a_several_algorithms|fb:part.metrics_and_graph_generators_visualization_is_provided_through_pylab_and_graphviz|fb:part.nx_is_an_open_source_project|fb:part.in_active_development_since_2004_with_an_open_bug_tracking_site|fb:part.and_user_forums_development_is_sponsored_by_los_alamos_national_lab
12	0	fb:cell.r	R	r	r	NN	O					R	fb:part.r
12	1	fb:cell.social_network_analysis_within_the_versatile_and_popular_r_environment	Social network analysis within the versatile and popular R environment	social|network|analysis|within|the|versatile|and|popular|r|environment	Social|network|analysis|within|the|versatile|and|popular|r|environment	NNP|NN|NN|IN|DT|JJ|CC|JJ|NN|NN	O|O|O|O|O|O|O|O|O|O	|||||||||				Social network analysis within the versatile and popular R environment	fb:part.social_network_analysis_within_the_versatile_and_popular_r_environment
12	2	fb:cell.r_will_read_in_almost_any_format_data_file	R will read in almost any format data file	r|will|read|in|almost|any|format|data|file	r|will|read|in|almost|any|format|datum|file	NN|VBD-AUX|VB|IN|RB|DT|NN|NNS|NN	O|O|O|O|O|O|O|O|O	||||||||				R will read in almost any format data file	fb:part.r_will_read_in_almost_any_format_data_file
12	3	fb:cell.r_has_write_capability_for_most_data_formats	R has write capability for most data formats	r|has|write|capability|for|most|data|formats	r|have|write|capability|for|most|datum|format	NN|VBD-AUX|VB|NN|IN|JJS|NNS|NNS	O|O|O|O|O|O|O|O	|||||||				R has write capability for most data formats	fb:part.r_has_write_capability_for_most_data_formats
12	4	fb:cell.windows_linux_mac	Windows, Linux, Mac	windows|,|linux|,|mac	Windows|,|Linux|,|Mac	NNP|,|NNP|,|NNP	MISC|O|O|O|O	||||				Windows|Linux|Mac	fb:part.windows|fb:part.linux|fb:part.mac
12	5	fb:cell.open_source	Open Source	open|source	Open|Source	NNP|NNP	MISC|MISC	|				Open Source	fb:part.open_source
12	6	fb:cell.r_contains_several_packages_relevant_for_social_network_analysis_igraph_is_a_generic_network_analysis_package_sna_performs_sociometric_analysis_of_networks_network_manipulates_and_displays_network_objects_tnet_performs_analysis_of_weighted_networks_two_mode_networks_and_longitudinal_networks_ergm_is_a_set_of_tools_to_analyze_and_simulate_networks_based_on_exponential_random_graph_models_exponential_random_graph_models_bergm_provides_tools_for_bayesian_analysis_for_exponential_random_graph_models_hergm_implements_hierarchical_exponential_random_graph_models_rsiena_allows_the_analyses_of_the_evolution_of_social_networks_using_dynamic_actor_oriented_models_latentnet_has_functions_for_network_latent_position_and_cluster_models_degreenet_provides_tools_for_statistical_modeling_of_network_degree_distributions_and_networksis_provides_tools_for_simulating_bipartite_networks_with_fixed_marginals	R contains several packages relevant for social network analysis: igraph is a generic network analysis package; sna performs sociometric analysis of networks; network manipulates and displays network objects; tnet performs analysis of weighted networks, two-mode networks, and longitudinal networks; ergm is a set of tools to analyze and simulate networks based on exponential random graph models exponential random graph models; Bergm provides tools for Bayesian analysis for exponential random graph models, hergm implements hierarchical exponential random graph models; 'RSiena' allows the analyses of the evolution of social networks using dynamic actor-oriented models; latentnet has functions for network latent position and cluster models; degreenet provides tools for statistical modeling of network degree distributions; and networksis provides tools for simulating bipartite networks with fixed marginals.	r|contains|several|packages|relevant|for|social|network|analysis|:|igraph|is|a|generic|network|analysis|package|;|sna|performs|sociometric|analysis|of|networks|;|network|manipulates|and|displays|network|objects|;|tnet|performs|analysis|of|weighted|networks|,|two|mode|networks|,|and|longitudinal|networks|;|ergm|is|a|set|of|tools|to|analyze|and|simulate|networks|based|on|exponential|random|graph|models|exponential|random|graph|models|;|bergm|provides|tools|for|bayesian|analysis|for|exponential|random|graph|models|,|hergm|implements|hierarchical|exponential|random|graph|models|;|`|rsiena|'|allows|the|analyses|of|the|evolution|of|social|networks|using|dynamic|actor|oriented|models|;|latentnet|has|functions|for|network|latent|position|and|cluster|models|;|degreenet|provides|tools|for|statistical|modeling|of|network|degree|distributions|;|and|networksis|provides|tools|for|simulating|bipartite|networks|with|fixed|marginals|.	r|contain|several|package|relevant|for|social|network|analysis|:|igraph|be|a|generic|network|analysis|package|;|sna|perform|sociometric|analysis|of|network|;|network|manipulate|and|display|network|object|;|tnet|perform|analysis|of|weighted|network|,|two|mode|network|,|and|longitudinal|network|;|ergm|be|a|set|of|tool|to|analyze|and|simulate|network|base|on|exponential|random|graph|model|exponential|random|graph|model|;|Bergm|provide|tool|for|bayesian|analysis|for|exponential|random|graph|model|,|hergm|implement|hierarchical|exponential|random|graph|model|;|`|rsiena|'|allow|the|analysis|of|the|evolution|of|social|network|use|dynamic|actor|orient|model|;|latentnet|have|function|for|network|latent|position|and|cluster|model|;|degreenet|provide|tool|for|statistical|modeling|of|network|degree|distribution|;|and|networksis|provide|tool|for|simulate|bipartite|network|with|fix|marginal|.	NN|VBZ|JJ|NNS|JJ|IN|JJ|NN|NN|:|NN|VBD-AUX|DT|JJ|NN|NN|NN|:|NN|VBZ|JJ|NN|IN|NNS|:|NN|VBZ|CC|VBZ|NN|NNS|:|NN|VBZ|NN|IN|JJ|NNS|,|CD|NN|NNS|,|CC|JJ|NNS|:|NN|VBD-AUX|DT|NN|IN|NNS|TO|VB|CC|VB|NNS|VBN|IN|JJ|JJ|NN|NNS|JJ|JJ|NN|NNS|:|NNP|VBZ|NNS|IN|JJ|NN|IN|JJ|JJ|NN|NNS|,|NN|VBZ|JJ|JJ|JJ|NN|NNS|:|``|NN|''|VBZ|DT|NNS|IN|DT|NN|IN|JJ|NNS|VBG|JJ|NN|VBN|NNS|:|NN|VBD-AUX|NNS|IN|NN|JJ|NN|CC|NN|NNS|:|NN|VBZ|NNS|IN|JJ|NN|IN|NN|NN|NNS|:|CC|NN|VBZ|NNS|IN|VBG|JJ|NNS|IN|VBN|NNS|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|NUMBER|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|MISC|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||||||2.0|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				R contains several packages relevant for social network analysis: igraph is a generic network analysis package; sna performs sociometric analysis of networks; network manipulates and displays network objects; tnet performs analysis of weighted networks|two-mode networks|and longitudinal networks; ergm is a set of tools to analyze and simulate networks based on exponential random graph models exponential random graph models; Bergm provides tools for Bayesian analysis for exponential random graph models|hergm implements hierarchical exponential random graph models; 'RSiena' allows the analyses of the evolution of social networks using dynamic actor-oriented models; latentnet has functions for network latent position and cluster models; degreenet provides tools for statistical modeling of network degree distributions; and networksis provides tools for simulating bipartite networks with fixed marginals.	fb:part.r_contains_several_packages_relevant_for_social_network_analysis_igraph_is_a_generic_network_analysis_package_sna_performs_sociometric_analysis_of_networks_network_manipulates_and_displays_network_objects_tnet_performs_analysis_of_weighted_networks|fb:part.two_mode_networks|fb:part.and_longitudinal_networks_ergm_is_a_set_of_tools_to_analyze_and_simulate_networks_based_on_exponential_random_graph_models_exponential_random_graph_models_bergm_provides_tools_for_bayesian_analysis_for_exponential_random_graph_models|fb:part.hergm_implements_hierarchical_exponential_random_graph_models_rsiena_allows_the_analyses_of_the_evolution_of_social_networks_using_dynamic_actor_oriented_models_latentnet_has_functions_for_network_latent_position_and_cluster_models_degreenet_provides_tools_for_statistical_modeling_of_network_degree_distributions_and_networksis_provides_tools_for_simulating_bipartite_networks_with_fixed_marginals
13	0	fb:cell.svat	SVAT	svat	svat	NN	O					SVAT	fb:part.svat
13	1	fb:cell.visual_analytics_for_investigation	Visual analytics for investigation	visual|analytics|for|investigation	visual|analytic|for|investigation	JJ|NNS|IN|NN	O|O|O|O	|||				Visual analytics for investigation	fb:part.visual_analytics_for_investigation
13	2	fb:cell.graphviz_dot_graphlet_gml_guess_gdf_leda_gml_networkx_graphml_net_nodexl_graphml_net_pajek_net_gml_sonivis_graphml_tulip_tlp_dot_ucinet_dl_yed_gml_gephi_gexf_edge_list_csv_databases_oracle_mssql_postgresql_mysql_webservices	GraphViz(.dot), Graphlet(.gml), GUESS(.gdf), LEDA(.gml), NetworkX(.graphml, .net), NodeXL(.graphml, .net), Pajek(.net, .gml), Sonivis(.graphml), Tulip(.tlp, .dot), UCINET(.dl), yEd(.gml), Gephi (.gexf), Edge list(.csv), databases - Oracle, MSSQL, PostgreSQL, MySQL, Webservices...	graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|guess|-lrb-|.|gdf|-rrb-|,|leda|-lrb-|.|gml|-rrb-|,|networkx|-lrb-|.|graphml|,|.|net|-rrb-|,|nodexl|-lrb-|.|graphml|,|.|net|-rrb-|,|pajek|-lrb-|.|net|,|.|gml|-rrb-|,|sonivis|-lrb-|.|graphml|-rrb-|,|tulip|-lrb-|.|tlp|,|.|dot|-rrb-|,|ucinet|-lrb-|.|dl|-rrb-|,|yed|-lrb-|.|gml|-rrb-|,|gephi|-lrb-|.|gexf|-rrb-|,|edge|list|-lrb-|.|csv|-rrb-|,|databases|-|oracle|,|mssql|,|postgresql|,|mysql|,|webservices|...	graphviz|-lrb-|.|dot|-rrb-|,|graphlet|-lrb-|.|gml|-rrb-|,|guess|-lrb-|.|gdf|-rrb-|,|leda|-lrb-|.|gml|-rrb-|,|networkx|-lrb-|.|graphml|,|.|net|-rrb-|,|nodexl|-lrb-|.|graphml|,|.|net|-rrb-|,|pajek|-lrb-|.|net|,|.|gml|-rrb-|,|Sonivis|-lrb-|.|graphml|-rrb-|,|Tulip|-lrb-|.|tlp|,|.|dot|-rrb-|,|UCINET|-lrb-|.|dl|-rrb-|,|yed|-lrb-|.|gml|-rrb-|,|Gephi|-lrb-|.|gexf|-rrb-|,|Edge|list|-lrb-|.|csv|-rrb-|,|database|-|Oracle|,|MSSQL|,|PostgreSQL|,|MySQL|,|Webservices|...	NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NN|-LRB-|.|NN|,|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|,|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|NNP|NN|-LRB-|.|NN|-RRB-|,|NNS|:|NNP|,|NNP|,|NNP|,|NNP|,|NNP|:	O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|LOCATION|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||				GraphViz(.dot)|Graphlet(.gml)|GUESS(.gdf)|LEDA(.gml)|NetworkX(.graphml|.net)|NodeXL(.graphml|.net)|Pajek(.net|.gml)|Sonivis(.graphml)|Tulip(.tlp|.dot)|UCINET(.dl)|yEd(.gml)|Gephi (.gexf)|Edge list(.csv)|databases - Oracle|MSSQL|PostgreSQL|MySQL|Webservices...	fb:part.graphviz_dot|fb:part.graphlet_gml|fb:part.guess_gdf|fb:part.leda_gml|fb:part.networkx_graphml|fb:part._net|fb:part.nodexl_graphml|fb:part._net|fb:part.pajek_net|fb:part._gml|fb:part.sonivis_graphml|fb:part.tulip_tlp|fb:part._dot|fb:part.ucinet_dl|fb:part.yed_gml|fb:part.gephi_gexf|fb:part.edge_list_csv|fb:part.databases_oracle|fb:part.mssql|fb:part.postgresql|fb:part.mysql|fb:part.webservices
13	3	fb:cell.guess_gdf_gephi_gexf_svg_png	GUESS(.gdf), Gephi(.gexf), .svg, .png	guess|-lrb-|.|gdf|-rrb-|,|gephi|-lrb-|.|gexf|-rrb-|,|.|svg|,|.|png	guess|-lrb-|.|gdf|-rrb-|,|Gephi|-lrb-|.|gexf|-rrb-|,|.|svg|,|.|png	NN|-LRB-|.|NN|-RRB-|,|NNP|-LRB-|.|NN|-RRB-|,|.|NN|,|.|NN	O|O|O|O|O|O|PERSON|O|O|O|O|O|O|O|O|O|O	||||||||||||||||				GUESS(.gdf)|Gephi(.gexf)|.svg|.png	fb:part.guess_gdf|fb:part.gephi_gexf_2|fb:part._svg|fb:part._png
13	4	fb:cell.any_system_supporting_java_1_6_and_opengl	Any system supporting Java 1.6 and OpenGL	any|system|supporting|java|1.6|and|opengl	any|system|support|Java|1.6|and|opengl	DT|NN|VBG|NNP|CD|CC|NN	O|O|O|MISC|NUMBER|O|O	||||1.6||	1.6			Any system supporting Java 1.6 and OpenGL	fb:part.any_system_supporting_java_1_6_and_opengl
13	5	fb:cell.closed_source_modules_open_source_modules_from_gephi	Closed source modules, Open Source modules from Gephi	closed|source|modules|,|open|source|modules|from|gephi	closed|source|module|,|Open|Source|module|from|Gephi	JJ|NN|NNS|,|NNP|NNP|NNS|IN|NNP	O|O|O|O|MISC|MISC|O|O|LOCATION	||||||||				Closed source modules|Open Source modules from Gephi	fb:part.closed_source_modules|fb:part.open_source_modules_from_gephi
13	6	fb:cell.commercial_tool_based_on_gephi	Commercial tool based on Gephi.	commercial|tool|based|on|gephi|.	commercial|tool|base|on|Gephi|.	JJ|NN|VBN|IN|NNP|.	O|O|O|O|PERSON|O	|||||				Commercial tool based on Gephi.	fb:part.commercial_tool_based_on_gephi
14	0	fb:cell.tulip	Tulip	tulip	Tulip	NNP	PERSON					Tulip	fb:part.tulip
14	1	fb:cell.social_network_analysis_tool	Social Network Analysis tool	social|network|analysis|tool	Social|Network|analysis|tool	NNP|NNP|NN|NN	O|O|O|O	|||				Social Network Analysis tool	fb:part.social_network_analysis_tool
14	2	fb:cell.tulip_format_tlp_graphviz_dot_gml_txt_adjacency_matrix	Tulip format (.tlp), GraphViz (.dot), GML, txt, adjacency matrix	tulip|format|-lrb-|.|tlp|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|gml|,|txt|,|adjacency|matrix	Tulip|format|-lrb-|.|tlp|-rrb-|,|graphviz|-lrb-|.|dot|-rrb-|,|gml|,|txt|,|adjacency|matrix	NNP|NN|-LRB-|.|NN|-RRB-|,|NN|-LRB-|.|NN|-RRB-|,|NN|,|NN|,|NN|NN	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||				Tulip format (.tlp)|GraphViz (.dot)|GML|txt|adjacency matrix	fb:part.tulip_format_tlp|fb:part.graphviz_dot_2|fb:part.gml|fb:part.txt|fb:part.adjacency_matrix
14	3	fb:cell._tlp_gml	.tlp, .gml	.|tlp|,|.|gml	.|tlp|,|.|gml	.|NN|,|.|NN	O|O|O|O|O	||||				.tlp|.gml	fb:part._tlp|fb:part._gml_2
14	4	fb:cell.windows_vista_xp_7_linux_mac_os	Windows Vista, XP, 7/ Linux / Mac OS	windows|vista|,|xp|,|7|\\/|linux|\\/|mac|os	Windows|Vista|,|xp|,|7|\\/|Linux|\\/|Mac|OS	NNP|NNP|,|NN|,|CD|:|NNP|:|NNP|NNP	MISC|MISC|O|O|O|NUMBER|O|O|O|O|O	|||||7.0|||||	7.0			Windows Vista|XP|7|Linux|Mac OS	fb:part.windows_vista|fb:part.xp|fb:part.7|fb:part.linux|fb:part.mac_os
14	5	fb:cell.lgpl	LGPL	lgpl	lgpl	NN	O					LGPL	fb:part.lgpl
14	6	fb:cell.tulip_is_an_information_visualization_framework_dedicated_to_the_analysis_and_visualization_of_relational_data_tulip_aims_to_provide_the_developer_with_a_complete_library_supporting_the_design_of_interactive_information_visualization_applications_for_relational_data_that_can_be_tailored_to_the_problems_he_or_she_is_addressing	Tulip is an information visualization framework dedicated to the analysis and visualization of relational data. Tulip aims to provide the developer with a complete library, supporting the design of interactive information visualization applications for relational data that can be tailored to the problems he or she is addressing.	tulip|is|an|information|visualization|framework|dedicated|to|the|analysis|and|visualization|of|relational|data|.|tulip|aims|to|provide|the|developer|with|a|complete|library|,|supporting|the|design|of|interactive|information|visualization|applications|for|relational|data|that|can|be|tailored|to|the|problems|he|or|she|is|addressing|.	Tulip|be|a|information|visualization|framework|dedicate|to|the|analysis|and|visualization|of|relational|datum|.|Tulip|aim|to|provide|the|developer|with|a|complete|library|,|support|the|design|of|interactive|information|visualization|application|for|relational|datum|that|can|be|tailor|to|the|problem|he|or|she|be|address|.	NNP|VBD-AUX|DT|NN|NN|NN|VBN|TO|DT|NN|CC|NN|IN|JJ|NNS|.|NNP|VBZ|TO|VB|DT|NN|IN|DT|JJ|NN|,|VBG|DT|NN|IN|JJ|NN|NN|NNS|IN|JJ|NNS|WDT|VBD-AUX|VBD-AUX|VBN|TO|DT|NNS|PRP|CC|PRP|VBD-AUX|VBG|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	||||||||||||||||||||||||||||||||||||||||||||||||||				Tulip is an information visualization framework dedicated to the analysis and visualization of relational data. Tulip aims to provide the developer with a complete library|supporting the design of interactive information visualization applications for relational data that can be tailored to the problems he or she is addressing.	fb:part.tulip_is_an_information_visualization_framework_dedicated_to_the_analysis_and_visualization_of_relational_data_tulip_aims_to_provide_the_developer_with_a_complete_library|fb:part.supporting_the_design_of_interactive_information_visualization_applications_for_relational_data_that_can_be_tailored_to_the_problems_he_or_she_is_addressing
15	0	fb:cell.visone	visone	visone	visone	NN	O					visone	fb:part.visone
15	1	fb:cell.visual_social_network_analyses_and_exploration	Visual Social Network Analyses and Exploration	visual|social|network|analyses|and|exploration	visual|Social|Network|analysis|and|exploration	JJ|NNP|NNP|NNS|CC|NN	O|O|O|O|O|O	|||||				Visual Social Network Analyses and Exploration	fb:part.visual_social_network_analyses_and_exploration
15	2	fb:cell.many_formats	many formats	many|formats	many|format	JJ|NNS	O|O	|				many formats	fb:part.many_formats
15	3	fb:cell.many_formats	many formats	many|formats	many|format	JJ|NNS	O|O	|				many formats	fb:part.many_formats
15	4	fb:cell.windows_linux_mac_os_java_based	Windows, Linux, Mac OS (Java based)	windows|,|linux|,|mac|os|-lrb-|java|based|-rrb-	Windows|,|Linux|,|Mac|OS|-lrb-|Java|base|-rrb-	NNP|,|NNP|,|NNP|NNP|-LRB-|NNP|VBN|-RRB-	MISC|O|O|O|O|O|O|MISC|O|O	|||||||||				Windows|Linux|Mac OS (Java based)	fb:part.windows|fb:part.linux|fb:part.mac_os_java_based
15	5	fb:cell.free_also_for_commercial_use	Free (also for commercial use)	free|-lrb-|also|for|commercial|use|-rrb-	Free|-lrb-|also|for|commercial|use|-rrb-	NNP|-LRB-|RB|IN|JJ|NN|-RRB-	O|O|O|O|O|O|O	||||||				Free (also for commercial use)	fb:part.free_also_for_commercial_use
15	6	fb:cell.visone_is_a_software_for_the_analysis_and_visualization_of_social_networks_it_is_currently_developed_by_algorithmics_group_at_the_university_of_konstanz	visone is a software for the analysis and visualization of social networks. It is currently developed by Algorithmics group at the University of Konstanz.	visone|is|a|software|for|the|analysis|and|visualization|of|social|networks|.|it|is|currently|developed|by|algorithmics|group|at|the|university|of|konstanz|.	visone|be|a|software|for|the|analysis|and|visualization|of|social|network|.|it|be|currently|develop|by|Algorithmics|group|at|the|University|of|Konstanz|.	NN|VBD-AUX|DT|NN|IN|DT|NN|CC|NN|IN|JJ|NNS|.|PRP|VBD-AUX|RB|VBN|IN|NNPS|NN|IN|DT|NNP|IN|NNP|.	O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|DATE|O|O|MISC|O|O|O|ORGANIZATION|ORGANIZATION|ORGANIZATION|O	|||||||||||||||PRESENT_REF||||||||||				visone is a software for the analysis and visualization of social networks. It is currently developed by Algorithmics group at the University of Konstanz.	fb:part.visone_is_a_software_for_the_analysis_and_visualization_of_social_networks_it_is_currently_developed_by_algorithmics_group_at_the_university_of_konstanz
16	0	fb:cell.wolfram_alpha	Wolfram Alpha	wolfram|alpha	Wolfram|Alpha	NNP|NNP	PERSON|PERSON	|				Wolfram Alpha	fb:part.wolfram_alpha
16	1	fb:cell.graph_analysis_time_series_analysis_categorical_data_analysis	Graph analysis, time series analysis, categorical data analysis	graph|analysis|,|time|series|analysis|,|categorical|data|analysis	graph|analysis|,|time|series|analysis|,|categorical|datum|analysis	NN|NN|,|NN|NN|NN|,|JJ|NN|NN	O|O|O|O|O|O|O|O|O|O	|||||||||				Graph analysis|time series analysis|categorical data analysis	fb:part.graph_analysis|fb:part.time_series_analysis|fb:part.categorical_data_analysis
16	2	fb:cell.facebook_api	Facebook API	facebook|api	Facebook|API	NNP|NNP	ORGANIZATION|ORGANIZATION	|				Facebook API	fb:part.facebook_api
16	3	fb:cell.many_formats	many formats	many|formats	many|format	JJ|NNS	O|O	|				many formats	fb:part.many_formats
16	4	fb:cell.web_service	Web service	web|service	web|service	NN|NN	O|O	|				Web service	fb:part.web_service
16	5	fb:cell.free	Free	free	Free	NNP	O					Free	fb:part.free
16	6	fb:cell.wolfram_alpha_is_a_general_computational_knowledge_engine_answering_queries_on_many_knowledge_domains_give_it_the_input_facebook_report_and_it_will_answer_queries_on_analysis_of_your_social_network_data	Wolfram Alpha is a general computational knowledge engine answering queries on many knowledge domains. Give it the input "Facebook report" and it will answer queries on analysis of your social network data,	wolfram|alpha|is|a|general|computational|knowledge|engine|answering|queries|on|many|knowledge|domains|.|give|it|the|input|``|facebook|report|''|and|it|will|answer|queries|on|analysis|of|your|social|network|data|,	Wolfram|Alpha|be|a|general|computational|knowledge|engine|answer|query|on|many|knowledge|domain|.|give|it|the|input|``|Facebook|report|''|and|it|will|answer|query|on|analysis|of|you|social|network|datum|,	NNP|NNP|VBD-AUX|DT|JJ|JJ|NN|NN|VBG|NNS|IN|JJ|NN|NNS|.|VB|PRP|DT|NN|``|NNP|NN|''|CC|PRP|VBD-AUX|VB|NNS|IN|NN|IN|PRP$|JJ|NN|NNS|,	PERSON|PERSON|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O|ORGANIZATION|O|O|O|O|O|O|O|O|O|O|O|O|O|O|O	|||||||||||||||||||||||||||||||||||				Wolfram Alpha is a general computational knowledge engine answering queries on many knowledge domains. Give it the input "Facebook report" and it will answer queries on analysis of your social network data,	fb:part.wolfram_alpha_is_a_general_computational_knowledge_engine_answering_queries_on_many_knowledge_domains_give_it_the_input_facebook_report_and_it_will_answer_queries_on_analysis_of_your_social_network_data
