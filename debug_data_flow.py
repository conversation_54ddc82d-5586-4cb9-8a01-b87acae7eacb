#!/usr/bin/env python3
"""
数据流程调试脚本 - 简化版本
用于验证和理解 Robust TableQA 项目的数据加载和处理流程
"""

import os
import sys
import json
from pathlib import Path
from easydict import EasyDict

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / "src"))

def create_sample_data():
    """创建示例数据用于调试"""
    sample_data = [
        {
            "question_id": "debug-001",
            "question": "What is the population of Tokyo?",
            "answers": ["13.96 million"],
            "table": {
                "header": ["City", "Population", "Country"],
                "rows": [
                    ["Tokyo", "13.96 million", "Japan"],
                    ["New York", "8.4 million", "USA"],
                    ["London", "9.0 million", "UK"]
                ],
                "name": "cities_table"
            }
        }
    ]
    
    # 确保数据目录存在
    data_dir = project_root / "datasets" / "debug"
    data_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存示例数据
    with open(data_dir / "test_small.json", "w") as f:
        json.dump(sample_data, f, indent=2)
    
    print(f"✅ 创建示例数据: {data_dir / 'test_small.json'}")
    return data_dir / "test_small.json"

def debug_basic_imports():
    """调试基础导入"""
    print("\n" + "="*50)
    print("🔍 步骤1: 基础导入调试")
    print("="*50)
    
    try:
        print("📦 测试基础导入...")
        
        # 测试 EasyDict
        test_dict = EasyDict({"test": "value"})
        print(f"  ✅ EasyDict: {test_dict.test}")
        
        # 测试数据加载器导入
        from data_loader_manager.data_loader_wrapper import DataLoaderWrapper
        print("  ✅ DataLoaderWrapper 导入成功")
        
        # 测试模块解析器导入
        from data_loader_manager.module_parser import ModuleParser
        print("  ✅ ModuleParser 导入成功")
        
        # 测试数据集导入
        from data_loader_manager.datasets.WikiTQ_dataset import WikiTQDataset
        print("  ✅ WikiTQDataset 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_module_parser():
    """调试模块解析器"""
    print("\n" + "="*50)
    print("🔍 步骤2: 模块解析器调试")
    print("="*50)
    
    try:
        from data_loader_manager.module_parser import ModuleParser
        
        # 创建解析器实例
        parser = ModuleParser()
        
        # 创建测试样本
        sample = EasyDict({
            "question": "What is the population of Tokyo?",
            "answers": ["13.96 million"],
            "table": {
                "header": ["City", "Population", "Country"],
                "rows": [
                    ["Tokyo", "13.96 million", "Japan"],
                    ["New York", "8.4 million", "USA"]
                ]
            }
        })
        
        print("📝 测试样本:")
        print(f"  - 问题: {sample.question}")
        print(f"  - 答案: {sample.answers}")
        print(f"  - 表格头: {sample.table.header}")
        
        # 测试问题输入解析
        print("\n🔍 测试 QuestionInput 解析器:")
        module_config = EasyDict({
            "type": "QuestionInput",
            "option": "default",
            "separation_tokens": {"start": "", "end": ""}
        })
        
        result = parser.QuestionInput(sample, module_config)
        print(f"  - 解析结果: {result}")
        
        # 测试表格输入解析
        print("\n🔍 测试 TextBasedTableInput 解析器:")
        module_config = EasyDict({
            "type": "TextBasedTableInput",
            "option": "default",
            "separation_tokens": {
                "header_start": "<HEADER>",
                "header_sep": "<HEADER_SEP>",
                "header_end": "<HEADER_END>",
                "row_start": "<ROW>",
                "row_sep": "<ROW_SEP>",
                "row_end": "<ROW_END>"
            }
        })
        
        result = parser.TextBasedTableInput(sample, module_config)
        print(f"  - 解析结果长度: {len(result.text_sequence)}")
        print(f"  - 解析结果预览: {result.text_sequence[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块解析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_data_flow_summary():
    """总结数据流程"""
    print("\n" + "="*50)
    print("🔍 步骤3: 数据流程总结")
    print("="*50)
    
    print("📋 数据流转过程:")
    print("  1️⃣ 原始数据 (.json/.arrow)")
    print("     ↓ [DataLoaderWrapper.build_dataset()]")
    print("  2️⃣ HuggingFace Dataset")
    print("     ↓ [预处理: split_table_by_*]")
    print("  3️⃣ 子表生成 (positive/negative)")
    print("     ↓ [Dataset.__getitem__()]")
    print("  4️⃣ 单个样本 (EasyDict)")
    print("     ↓ [DataLoader + collate_fn()]")
    print("  5️⃣ 批处理 (List[EasyDict])")
    print("     ↓ [ModuleParser.parse_modules()]")
    print("  6️⃣ 特征解析 (input/decoder_input/output)")
    print("     ↓ [后处理模块: 分词化]")
    print("  7️⃣ Tensor格式 (input_ids, attention_mask, labels)")
    print("     ↓ [模型输入]")
    print("  8️⃣ 模型处理")
    
    print("\n🎯 关键组件:")
    print("  - DataLoaderWrapper: 数据加载管理")
    print("  - ModuleParser: 样本级解析")
    print("  - Dataset: PyTorch数据集接口")
    print("  - collate_fn: 批处理函数")
    print("  - 分词器: 文本→数字转换")
    
    print("\n🔧 ITR特殊处理:")
    print("  - 表格分割: 生成子表")
    print("  - 正负样本: 基于答案坐标")
    print("  - 检索机制: 嵌入相似度")
    print("  - 多粒度: 行级/列级/混合")

def main():
    """主调试函数"""
    print("🚀 开始 Robust TableQA 数据流程调试")
    print("="*60)
    
    # 创建示例数据
    create_sample_data()
    
    # 步骤1: 基础导入测试
    if not debug_basic_imports():
        print("❌ 基础导入失败，终止调试")
        return
    
    # 步骤2: 模块解析器测试
    if not debug_module_parser():
        print("❌ 模块解析器测试失败，终止调试")
        return
    
    # 步骤3: 数据流程总结
    debug_data_flow_summary()
    
    print("\n" + "="*60)
    print("🎉 数据流程调试完成！")
    print("✅ 基础组件测试通过")
    print("📚 数据流程理解验证完成")
    print("="*60)

if __name__ == "__main__":
    main()
