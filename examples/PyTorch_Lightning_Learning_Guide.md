# PyTorch Lightning 学习指南

本指南包含了两个Jupyter Notebook教程，帮助您从零开始学习PyTorch Lightning。

## 📚 教程文件

### 1. `pytorch_lightning_quickstart.ipynb` - 快速入门
**适合人群**: 初学者，想要快速了解PyTorch Lightning基础概念

**内容包括**:
- 创建第一个Lightning模型
- 基础训练循环
- 添加验证步骤
- 核心概念解释

**预计学习时间**: 30-45分钟

### 2. `pytorch_lightning_tutorial.ipynb` - 完整教程
**适合人群**: 想要深入学习PyTorch Lightning的开发者

**内容包括**:
- PyTorch Lightning详细介绍
- LightningModule和LightningDataModule
- 回调函数系统
- 日志记录和监控
- 多GPU训练
- 高级特性（梯度累积、混合精度等）
- 完整项目示例
- 最佳实践

**预计学习时间**: 2-3小时

## 🚀 快速开始

### 环境准备
```bash
# 安装必要的包
pip install pytorch-lightning torch torchvision matplotlib seaborn wandb tensorboard torchmetrics
```

### 学习路径建议

#### 初学者路径
1. 先阅读本README了解基础概念
2. 运行 `pytorch_lightning_quickstart.ipynb`
3. 理解核心概念后，选择性学习完整教程的部分章节

#### 进阶路径
1. 直接学习 `pytorch_lightning_tutorial.ipynb`
2. 按章节顺序学习，每章节都有实际代码示例
3. 尝试修改代码参数，观察效果

## 🔑 核心概念

### PyTorch Lightning vs 原生PyTorch

| 特性 | 原生PyTorch | PyTorch Lightning |
|------|-------------|-------------------|
| 训练循环 | 手动编写 | 自动处理 |
| GPU/CPU切换 | 手动管理 | 自动处理 |
| 分布式训练 | 复杂配置 | 简单参数 |
| 日志记录 | 手动实现 | 内置支持 |
| 检查点 | 手动保存/加载 | 自动管理 |
| 代码组织 | 自由结构 | 标准化结构 |

### 主要组件

1. **LightningModule** 📦
   - 继承此类定义您的模型
   - 包含模型架构、训练逻辑、优化器配置

2. **LightningDataModule** 📊
   - 组织数据相关代码
   - 处理数据下载、预处理、DataLoader创建

3. **Trainer** 🎯
   - 控制训练过程的核心类
   - 处理训练循环、验证、测试、日志记录

4. **Callbacks** 🔄
   - 在训练过程特定时刻执行的函数
   - 如：模型检查点、早停、学习率调度

## 💡 关键方法

### LightningModule必须实现的方法
```python
def training_step(self, batch, batch_idx):
    # 定义单个训练步骤
    pass

def configure_optimizers(self):
    # 配置优化器和学习率调度器
    pass
```

### 可选但常用的方法
```python
def validation_step(self, batch, batch_idx):
    # 验证步骤
    pass

def test_step(self, batch, batch_idx):
    # 测试步骤
    pass

def forward(self, x):
    # 前向传播
    pass
```

## 🛠️ 实用技巧

### 1. 日志记录
```python
# 在training_step或validation_step中
self.log('train_loss', loss, prog_bar=True)  # 显示在进度条
self.log('val_acc', accuracy, on_epoch=True)  # 每个epoch记录一次
```

### 2. 超参数管理
```python
def __init__(self, learning_rate=1e-3, batch_size=32):
    super().__init__()
    self.save_hyperparameters()  # 自动保存所有参数
```

### 3. 多GPU训练
```python
trainer = Trainer(
    accelerator="gpu",
    devices=-1,  # 使用所有GPU
    strategy="ddp"  # 分布式数据并行
)
```

### 4. 混合精度训练
```python
trainer = Trainer(precision=16)  # 使用半精度
```

## 🎯 学习目标

完成这些教程后，您将能够：

- ✅ 理解PyTorch Lightning的核心概念和优势
- ✅ 创建和训练Lightning模型
- ✅ 使用DataModule组织数据代码
- ✅ 配置回调函数和日志记录
- ✅ 进行多GPU训练
- ✅ 应用高级特性如混合精度、梯度累积
- ✅ 组织完整的深度学习项目

## 📖 扩展学习资源

- [PyTorch Lightning官方文档](https://pytorch-lightning.readthedocs.io/)
- [PyTorch Lightning GitHub](https://github.com/PyTorchLightning/pytorch-lightning)
- [官方示例](https://github.com/PyTorchLightning/pytorch-lightning/tree/master/pl_examples)
- [社区教程](https://pytorch-lightning.readthedocs.io/en/stable/tutorials.html)

## 🤝 贡献和反馈

如果您发现教程中的问题或有改进建议，欢迎：
- 提出Issue
- 提交Pull Request
- 分享您的学习心得

## 📝 许可证

本教程遵循MIT许可证，可自由使用和分享。

---

**开始您的PyTorch Lightning学习之旅吧！** 🚀

建议从快速入门教程开始，然后根据需要深入学习完整教程的相关章节。记住，最好的学习方式是动手实践！
