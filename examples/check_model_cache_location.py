# 查看HuggingFace模型缓存位置
import os
from pathlib import Path
from transformers import DPRQuestionEncoderTokenizer

print("🔍 查看HuggingFace模型缓存位置")
print("=" * 50)

# 方法1: 查看默认缓存目录
def show_cache_locations():
    """显示所有可能的缓存位置"""
    
    # HuggingFace默认缓存目录
    default_cache = Path.home() / ".cache" / "huggingface"
    print(f"1. 默认缓存目录: {default_cache}")
    print(f"   存在: {default_cache.exists()}")
    
    # 环境变量指定的缓存目录
    hf_home = os.environ.get('HF_HOME')
    hf_cache = os.environ.get('HUGGINGFACE_HUB_CACHE')
    transformers_cache = os.environ.get('TRANSFORMERS_CACHE')
    
    print(f"\n2. 环境变量设置:")
    print(f"   HF_HOME: {hf_home}")
    print(f"   HUGGINGFACE_HUB_CACHE: {hf_cache}")
    print(f"   TRANSFORMERS_CACHE: {transformers_cache}")
    
    # 实际使用的缓存目录
    from transformers.utils import TRANSFORMERS_CACHE
    print(f"\n3. 实际缓存目录: {TRANSFORMERS_CACHE}")
    
    return TRANSFORMERS_CACHE

cache_dir = show_cache_locations()