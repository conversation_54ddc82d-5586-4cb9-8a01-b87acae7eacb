"""
matplotlib中文显示配置模块
解决中文乱码问题
"""

import matplotlib.pyplot as plt
import matplotlib
from matplotlib import font_manager
import platform
import os

def setup_chinese_fonts():
    """
    设置matplotlib中文字体显示
    """
    system = platform.system()
    
    if system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Songti SC', 'STSong', 'Heiti SC']
    elif system == "Windows":
        fonts = ['SimHei', 'Microsoft YaHei', 'SimSun']
    else:  # Linux
        fonts = ['DejaVu Sans', 'WenQuanYi Micro Hei', 'Noto Sans CJK SC']
    
    # 设置字体
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号
    
    print(f"✅ 已为 {system} 系统设置中文字体: {fonts[0]}")
    
    return fonts[0]

def test_chinese_display():
    """
    测试中文显示效果
    """
    import numpy as np
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(8, 6))
    
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    ax.plot(x, y, label='正弦波')
    ax.set_title('中文标题测试 - DPR 问题-文档相似度矩阵')
    ax.set_xlabel('横轴标签 (X轴)')
    ax.set_ylabel('纵轴标签 (Y轴)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("✅ 中文显示测试完成")

def get_available_fonts():
    """
    获取系统中可用的字体列表
    """
    fonts = font_manager.fontManager.ttflist
    font_names = [font.name for font in fonts]
    
    # 筛选可能支持中文的字体
    chinese_fonts = []
    keywords = ['Chinese', 'SimHei', 'Arial Unicode', 'Songti', 'Heiti', 'YaHei', 'WenQuanYi', 'Noto']
    
    for font_name in set(font_names):
        for keyword in keywords:
            if keyword in font_name:
                chinese_fonts.append(font_name)
                break
    
    return sorted(chinese_fonts)

def quick_setup():
    """
    一键设置中文显示
    """
    print("🚀 开始设置matplotlib中文显示...")
    
    # 设置字体
    font_name = setup_chinese_fonts()
    
    # 显示可用字体
    available_fonts = get_available_fonts()
    print(f"📝 系统可用的中文字体: {available_fonts}")
    
    # 验证设置
    current_font = plt.rcParams['font.sans-serif'][0]
    print(f"🎯 当前使用字体: {current_font}")
    
    print("\n💡 使用提示:")
    print("1. 在绘图代码之前运行此设置")
    print("2. 如果仍有乱码，尝试重启notebook kernel")
    print("3. 可以调用 test_chinese_display() 测试效果")
    
    return font_name

if __name__ == "__main__":
    # 直接运行时进行设置和测试
    quick_setup()
    test_chinese_display()
