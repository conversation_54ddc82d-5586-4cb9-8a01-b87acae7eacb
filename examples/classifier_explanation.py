# 演示分类头的作用
import torch
import torch.nn as nn

# 模拟BERT输出的[CLS]表示
cls_representation = torch.randn(1, 768)  # [batch_size=1, hidden_size=768]
print(f"[CLS]表示维度: {cls_representation.shape}")
print(f"[CLS]表示示例: {cls_representation[0, :5]}")  # 显示前5个值

# 分类头：768维 -> 1维
classifier = nn.Linear(768, 1)
print(f"\n分类头权重形状: {classifier.weight.shape}")  # [1, 768]
print(f"分类头偏置形状: {classifier.bias.shape}")      # [1]

# 计算相关性分数
relevance_score = classifier(cls_representation)
print(f"\n相关性分数: {relevance_score}")
print(f"分数维度: {relevance_score.shape}")

# 数学公式: score = W * cls_representation + b
# 其中 W 是 [1, 768] 的权重矩阵，b 是标量偏置