#%% md
# ColBERT 学习教程

本教程将带你深入了解ColBERT（Contextualized Late Interaction over BERT）的核心概念和实际应用。

## 📚 学习目标
1. 理解ColBERT的核心思想和架构
2. 掌握ColBERT的索引和搜索流程
3. 实践ColBERT在信息检索中的应用
4. 对比ColBERT与传统检索方法的优势
#%% md
## 🎯 1. ColBERT核心概念

### 什么是ColBERT？
ColBERT是一种高效的神经信息检索模型，它结合了：
- **BERT的强大语义理解能力**
- **Late Interaction的高效计算方式**

### 核心创新点
1. **Late Interaction**: 查询和文档分别编码，在最后阶段进行交互
2. **Token-level Matching**: 细粒度的token级别匹配
3. **高效索引**: 预计算文档表示，支持快速检索
#%%
# 安装必要的依赖
!pip install torch transformers datasets tqdm

import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer, AutoModel
import torch.nn.functional as F
from typing import List, Tuple
import json
#%% md
## 🏗️ 2. ColBERT架构详解

让我们从零开始实现一个简化版的ColBERT来理解其工作原理：
#%%
class SimpleColBERT:
    """简化版ColBERT实现，用于教学演示"""
    
    def __init__(self, model_name="AI-ModelScope/bert-base-uncased"):
        print("🚀 初始化SimpleColBERT...")
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.bert = AutoModel.from_pretrained(model_name)
        self.dim = 128  # ColBERT通常使用较小的维度
        
        # 线性投影层，将BERT输出投影到较小维度
        self.linear = torch.nn.Linear(self.bert.config.hidden_size, self.dim)
        
        print(f"✅ 模型加载完成！")
        print(f"   - BERT隐藏层维度: {self.bert.config.hidden_size}")
        print(f"   - ColBERT输出维度: {self.dim}")
    
    def encode_query(self, query: str) -> torch.Tensor:
        """编码查询"""
        print(f"🔍 编码查询: '{query}'")
        
        # 1. Tokenization
        inputs = self.tokenizer(
            query, 
            return_tensors="pt", 
            padding=True, 
            truncation=True, 
            max_length=32
        )
        
        # 2. BERT编码
        with torch.no_grad():
            outputs = self.bert(**inputs)
            hidden_states = outputs.last_hidden_state  # [1, seq_len, 768]
        
        # 3. 线性投影
        query_embeddings = self.linear(hidden_states)  # [1, seq_len, 128]
        
        # 4. L2归一化
        query_embeddings = F.normalize(query_embeddings, p=2, dim=-1)
        
        print(f"   - Token数量: {query_embeddings.shape[1]}")
        print(f"   - 嵌入维度: {query_embeddings.shape[2]}")
        
        return query_embeddings.squeeze(0).detach()  # [seq_len, 128]
    
    def encode_document(self, document: str) -> torch.Tensor:
        """编码文档"""
        print(f"📄 编码文档: '{document[:50]}...'")
        
        # 文档编码过程与查询类似
        inputs = self.tokenizer(document, return_tensors="pt", 
                               padding=True, truncation=True, max_length=180)
        
        with torch.no_grad():
            outputs = self.bert(**inputs)
            hidden_states = outputs.last_hidden_state
        
        doc_embeddings = self.linear(hidden_states)
        doc_embeddings = F.normalize(doc_embeddings, p=2, dim=-1)
        
        print(f"   - Token数量: {doc_embeddings.shape[1]}")
        
        return doc_embeddings.squeeze(0).detach()  # [seq_len, 128]
    
    def late_interaction(self, query_embeddings: torch.Tensor, 
                        doc_embeddings: torch.Tensor) -> float:
        """Late Interaction计算相似度"""
        print("🔄 执行Late Interaction...")
        
        # 计算所有query token与doc token的相似度矩阵
        # query_embeddings: [q_len, dim]
        # doc_embeddings: [d_len, dim]
        similarity_matrix = torch.matmul(query_embeddings, doc_embeddings.T)  # [q_len, d_len]
        
        print(f"   - 相似度矩阵形状: {similarity_matrix.shape}")
        
        # 对每个query token，找到与文档中最相似的token
        max_similarities = torch.max(similarity_matrix, dim=1)[0]  # [q_len]
        
        # 对所有query token的最大相似度求和
        final_score = torch.sum(max_similarities).item()
        
        print(f"   - 每个query token的最大相似度: {max_similarities.tolist()[:5]}...")
        print(f"   - 最终得分: {final_score:.4f}")
        
        return final_score
    
    def search(self, query: str, documents: List[str]) -> List[Tuple[int, float]]:
        """搜索最相关的文档"""
        print(f"\n🔍 开始搜索，共{len(documents)}个文档")
        print("=" * 50)
        
        # 编码查询
        query_embeddings = self.encode_query(query)
        
        scores = []
        
        # 对每个文档计算相似度
        for i, doc in enumerate(documents):
            print(f"\n📄 处理文档 {i+1}/{len(documents)}")
            
            # 编码文档
            doc_embeddings = self.encode_document(doc)
            
            # 计算相似度
            score = self.late_interaction(query_embeddings, doc_embeddings)
            scores.append((i, score))
        
        # 按分数排序
        scores.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\n🏆 搜索完成！")
        return scores

# 初始化模型
colbert = SimpleColBERT()
#%% md
## 🧪 3. 实践演示：ColBERT vs 传统方法

让我们用一个具体例子来看ColBERT的效果：
#%%
# 准备测试数据
query = "machine learning algorithms"

documents = [
    "Machine learning is a subset of artificial intelligence that focuses on algorithms.",
    "Deep learning uses neural networks with multiple layers for pattern recognition.",
    "Natural language processing helps computers understand human language.",
    "Computer vision enables machines to interpret and understand visual information.",
    "Supervised learning algorithms learn from labeled training data examples."
]

print("📋 测试数据准备完成")
print(f"查询: '{query}'")
print(f"文档数量: {len(documents)}")

print("\n文档列表:")
for i, doc in enumerate(documents):
    print(f"{i+1}. {doc}")
#%%
# 使用ColBERT进行搜索
results = colbert.search(query, documents)

print("\n🏆 ColBERT搜索结果:")
print("=" * 60)
for rank, (doc_id, score) in enumerate(results, 1):
    print(f"排名 {rank}: 文档{doc_id+1} (得分: {score:.4f})")
    print(f"内容: {documents[doc_id]}")
    print()
#%% md
## 📊 4. 可视化分析

让我们可视化ColBERT的工作过程：
#%%
def visualize_similarity_matrix(query: str, document: str, colbert_model):
    """可视化查询和文档之间的相似度矩阵"""
    
    # 编码查询和文档
    query_embeddings = colbert_model.encode_query(query)
    doc_embeddings = colbert_model.encode_document(document)
    
    # 计算相似度矩阵
    similarity_matrix = torch.matmul(query_embeddings, doc_embeddings.T)
    
    # 获取token
    query_tokens = colbert_model.tokenizer.tokenize(query)
    doc_tokens = colbert_model.tokenizer.tokenize(document)
    
    # 绘制热力图
    plt.figure(figsize=(12, 8))
    plt.imshow(similarity_matrix.numpy(), cmap='Blues', aspect='auto')
    plt.colorbar(label='Similarity Score')
    
    # 设置标签
    plt.xticks(range(len(doc_tokens)), doc_tokens, rotation=45, ha='right')
    plt.yticks(range(len(query_tokens)), query_tokens)
    
    plt.xlabel('Document Tokens')
    plt.ylabel('Query Tokens')
    plt.title('ColBERT Token-level Similarity Matrix')
    plt.tight_layout()
    plt.show()
    
    # 显示最高相似度
    max_similarities = torch.max(similarity_matrix, dim=1)[0]
    print("\n🔍 每个查询token的最大相似度:")
    for token, sim in zip(query_tokens, max_similarities):
        print(f"'{token}': {sim:.4f}")

# 可视化最相关的文档
best_doc_id = results[0][0]
visualize_similarity_matrix(query, documents[best_doc_id], colbert)
#%% md
## 🔄 5. 与传统方法对比

让我们实现一个简单的TF-IDF方法来对比：
#%%
from collections import Counter
import math

class SimpleTFIDF:
    """简单的TF-IDF实现用于对比"""
    
    def __init__(self):
        self.vocab = set()
        self.idf = {}
    
    def preprocess(self, text: str) -> List[str]:
        """简单的文本预处理"""
        return text.lower().split()
    
    def build_vocab(self, documents: List[str]):
        """构建词汇表和计算IDF"""
        doc_count = len(documents)
        word_doc_count = Counter()
        
        for doc in documents:
            words = set(self.preprocess(doc))
            self.vocab.update(words)
            for word in words:
                word_doc_count[word] += 1
        
        # 计算IDF
        for word in self.vocab:
            self.idf[word] = math.log(doc_count / word_doc_count[word])
    
    def get_tfidf_vector(self, text: str) -> dict:
        """计算文本的TF-IDF向量"""
        words = self.preprocess(text)
        word_count = Counter(words)
        doc_length = len(words)
        
        tfidf_vector = {}
        for word in self.vocab:
            tf = word_count[word] / doc_length if doc_length > 0 else 0
            tfidf_vector[word] = tf * self.idf.get(word, 0)
        
        return tfidf_vector
    
    def cosine_similarity(self, vec1: dict, vec2: dict) -> float:
        """计算余弦相似度"""
        dot_product = sum(vec1[word] * vec2[word] for word in self.vocab)
        norm1 = math.sqrt(sum(vec1[word] ** 2 for word in self.vocab))
        norm2 = math.sqrt(sum(vec2[word] ** 2 for word in self.vocab))
        
        if norm1 == 0 or norm2 == 0:
            return 0
        return dot_product / (norm1 * norm2)
    
    def search(self, query: str, documents: List[str]) -> List[Tuple[int, float]]:
        """TF-IDF搜索"""
        self.build_vocab(documents + [query])
        
        query_vector = self.get_tfidf_vector(query)
        scores = []
        
        for i, doc in enumerate(documents):
            doc_vector = self.get_tfidf_vector(doc)
            score = self.cosine_similarity(query_vector, doc_vector)
            scores.append((i, score))
        
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores

# TF-IDF搜索
tfidf = SimpleTFIDF()
tfidf_results = tfidf.search(query, documents)

print("📊 TF-IDF vs ColBERT 结果对比:")
print("=" * 60)
print(f"{'排名':<4} {'TF-IDF':<20} {'ColBERT':<20}")
print("-" * 60)

for rank in range(len(documents)):
    tfidf_doc = tfidf_results[rank][0] + 1
    tfidf_score = tfidf_results[rank][1]
    
    colbert_doc = results[rank][0] + 1
    colbert_score = results[rank][1]
    
    print(f"{rank+1:<4} Doc{tfidf_doc} ({tfidf_score:.3f}){'':<8} Doc{colbert_doc} ({colbert_score:.3f})")
#%% md
## 🎯 6. ColBERT的优势分析

让我们分析ColBERT相比传统方法的优势：
#%%
def analyze_advantages():
    """分析ColBERT的优势"""
    
    advantages = {
        "语义理解": {
            "ColBERT": "基于BERT的深度语义理解，能捕捉上下文信息",
            "TF-IDF": "基于词频统计，无法理解语义关系",
            "示例": "'ML'和'machine learning'在ColBERT中语义相近"
        },
        "细粒度匹配": {
            "ColBERT": "Token级别的精确匹配，每个查询词都能找到最佳对应",
            "TF-IDF": "文档级别的整体相似度计算",
            "示例": "查询中的每个词都能在文档中找到最相关的部分"
        },
        "效率优化": {
            "ColBERT": "Late Interaction设计，文档可预计算和索引",
            "Dense Retrieval": "需要在线计算查询-文档交互",
            "示例": "大规模检索时ColBERT更高效"
        },
        "鲁棒性": {
            "ColBERT": "对同义词、拼写变化等更鲁棒",
            "传统方法": "严格的词汇匹配，容易遗漏相关文档",
            "示例": "'algorithm'和'method'在语义上相关"
        }
    }
    
    print("🎯 ColBERT优势分析")
    print("=" * 80)
    
    for aspect, details in advantages.items():
        print(f"\n📌 {aspect}:")
        for key, value in details.items():
            if key != "示例":
                print(f"   {key}: {value}")
            else:
                print(f"   💡 {key}: {value}")

analyze_advantages()
#%% md
## 🔧 7. 实际应用场景

让我们看看ColBERT在不同场景下的应用：
#%%
def test_different_scenarios():
    """测试ColBERT在不同场景下的表现"""
    
    scenarios = [
        {
            "name": "同义词查询",
            "query": "AI techniques",
            "documents": [
                "Artificial intelligence methods are widely used in industry.",
                "Machine learning algorithms solve complex problems.",
                "Traditional programming approaches have limitations.",
                "Neural networks are powerful AI tools."
            ]
        },
        {
            "name": "概念匹配",
            "query": "deep neural networks",
            "documents": [
                "Convolutional neural networks excel at image recognition.",
                "Multi-layer perceptrons are basic neural architectures.",
                "Deep learning models have many hidden layers.",
                "Support vector machines are classical ML methods."
            ]
        },
        {
            "name": "上下文理解",
            "query": "python programming",
            "documents": [
                "Python is a popular programming language for data science.",
                "The python snake is found in tropical regions.",
                "Java and C++ are also programming languages.",
                "Coding in Python is beginner-friendly."
            ]
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🧪 测试场景: {scenario['name']}")
        print(f"查询: '{scenario['query']}'")
        print("-" * 50)
        
        results = colbert.search(scenario['query'], scenario['documents'])
        
        print("\n🏆 ColBERT排序结果:")
        for rank, (doc_id, score) in enumerate(results[:3], 1):
            print(f"{rank}. (得分: {score:.3f}) {scenario['documents'][doc_id]}")
        
        print("\n" + "="*80)

test_different_scenarios()
#%% md
## 📈 8. 性能分析和优化

让我们分析ColBERT的性能特点：
#%%
import time

def performance_analysis():
    """性能分析"""
    
    # 准备不同大小的文档集合
    base_docs = [
        "Machine learning is a subset of artificial intelligence.",
        "Deep learning uses neural networks with multiple layers.",
        "Natural language processing helps computers understand text.",
        "Computer vision enables machines to interpret images.",
        "Reinforcement learning trains agents through rewards."
    ]
    
    test_query = "artificial intelligence methods"
    
    print("📈 ColBERT性能分析")
    print("=" * 50)
    
    # 测试不同文档数量的性能
    for multiplier in [1, 2, 5]:
        test_docs = base_docs * multiplier
        
        start_time = time.time()
        results = colbert.search(test_query, test_docs)
        end_time = time.time()
        
        print(f"\n📊 文档数量: {len(test_docs)}")
        print(f"   搜索时间: {end_time - start_time:.3f}秒")
        print(f"   平均每文档: {(end_time - start_time) / len(test_docs) * 1000:.2f}毫秒")
    
    # 内存使用分析
    print(f"\n💾 内存使用分析:")
    print(f"   模型参数: ~110M (BERT-base)")
    print(f"   每个token嵌入: {colbert.dim * 4}字节 (float32)")
    print(f"   查询嵌入 (32 tokens): {32 * colbert.dim * 4 / 1024:.1f}KB")
    print(f"   文档嵌入 (180 tokens): {180 * colbert.dim * 4 / 1024:.1f}KB")

performance_analysis()
#%% md
## 🎓 9. 学习总结

通过本教程，我们学习了：
#%%
def learning_summary():
    """学习总结"""
    
    summary = {
        "核心概念": [
            "ColBERT = BERT + Late Interaction",
            "Token级别的细粒度匹配",
            "查询和文档分别编码，最后交互",
            "高效的预计算和索引机制"
        ],
        "技术优势": [
            "强大的语义理解能力",
            "高效的检索性能",
            "良好的可扩展性",
            "端到端的可训练性"
        ],
        "应用场景": [
            "大规模文档检索",
            "问答系统",
            "推荐系统",
            "语义搜索引擎"
        ],
        "实现要点": [
            "BERT编码 + 线性投影",
            "L2归一化确保稳定性",
            "MaxSim操作进行token匹配",
            "批处理优化提升效率"
        ]
    }
    
    print("🎓 ColBERT学习总结")
    print("=" * 60)
    
    for category, points in summary.items():
        print(f"\n📌 {category}:")
        for i, point in enumerate(points, 1):
            print(f"   {i}. {point}")
    
    print(f"\n🚀 下一步学习建议:")
    next_steps = [
        "深入研究ColBERTv2的改进",
        "学习PLAID等加速技术",
        "实践大规模索引构建",
        "探索多模态检索应用",
        "研究量化和压缩技术"
    ]
    
    for i, step in enumerate(next_steps, 1):
        print(f"   {i}. {step}")

learning_summary()
#%% md
## 🔗 10. 参考资源

### 论文
- [ColBERT: Efficient and Effective Passage Search via Contextualized Late Interaction over BERT](https://arxiv.org/abs/2004.12832)
- [ColBERTv2: Effective and Efficient Retrieval via Lightweight Late Interaction](https://arxiv.org/abs/2112.01488)

### 代码库
- [Stanford ColBERT GitHub](https://github.com/stanford-futuredata/ColBERT)

### 相关技术
- BERT: Bidirectional Encoder Representations from Transformers
- Dense Passage Retrieval (DPR)
- PLAID: An Efficient Engine for Late Interaction Retrieval

---

**恭喜你完成了ColBERT学习教程！** 🎉

现在你已经掌握了ColBERT的核心原理和实现方法，可以开始在实际项目中应用这项技术了！