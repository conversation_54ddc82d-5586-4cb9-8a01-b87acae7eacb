"""
ColBERTv2 延迟交互架构示例
演示ColBERT的核心思想和实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from transformers import BertModel, BertTokenizer
import numpy as np

class ColBERTv2(nn.Module):
    """
    ColBERTv2 模型实现
    采用延迟交互(Late Interaction)架构
    """
    
    def __init__(self, model_name="bert-base-uncased", dim=128):
        super().__init__()
        self.bert = BertModel.from_pretrained(model_name)
        self.tokenizer = BertTokenizer.from_pretrained(model_name)
        
        # 降维层：将BERT输出降维到指定维度
        self.linear = nn.Linear(self.bert.config.hidden_size, dim)
        self.dim = dim
        
    def encode_query(self, query, max_length=32):
        """
        编码查询，返回token-level表示
        """
        # 添加特殊标记
        query = f"[Q] {query}"
        
        encoding = self.tokenizer(
            query,
            truncation=True,
            padding=True,
            max_length=max_length,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = self.bert(**encoding)
            # 获取所有token的表示 [batch_size, seq_len, hidden_size]
            token_embeddings = outputs.last_hidden_state
            
            # 降维 [batch_size, seq_len, dim]
            token_embeddings = self.linear(token_embeddings)
            
            # L2归一化
            token_embeddings = F.normalize(token_embeddings, p=2, dim=-1)
            
            # 移除padding tokens
            attention_mask = encoding['attention_mask']
            token_embeddings = token_embeddings * attention_mask.unsqueeze(-1)
            
        return token_embeddings.squeeze(0), attention_mask.squeeze(0)
    
    def encode_document(self, document, max_length=512):
        """
        编码文档，返回token-level表示
        """
        # 添加特殊标记
        document = f"[D] {document}"
        
        encoding = self.tokenizer(
            document,
            truncation=True,
            padding=True,
            max_length=max_length,
            return_tensors="pt"
        )
        
        with torch.no_grad():
            outputs = self.bert(**encoding)
            token_embeddings = outputs.last_hidden_state
            
            # 降维和归一化
            token_embeddings = self.linear(token_embeddings)
            token_embeddings = F.normalize(token_embeddings, p=2, dim=-1)
            
            # 移除padding tokens
            attention_mask = encoding['attention_mask']
            token_embeddings = token_embeddings * attention_mask.unsqueeze(-1)
            
        return token_embeddings.squeeze(0), attention_mask.squeeze(0)
    
    def late_interaction(self, query_embeddings, query_mask, doc_embeddings, doc_mask):
        """
        延迟交互：MaxSim操作
        """
        # 只考虑非padding的tokens
        query_tokens = query_embeddings[query_mask.bool()]  # [q_len, dim]
        doc_tokens = doc_embeddings[doc_mask.bool()]        # [d_len, dim]
        
        if query_tokens.size(0) == 0 or doc_tokens.size(0) == 0:
            return torch.tensor(0.0)
        
        # 计算所有query-doc token对的相似度
        # [q_len, d_len]
        similarity_matrix = torch.matmul(query_tokens, doc_tokens.T)
        
        # 对每个query token，找到最大相似度
        max_similarities = torch.max(similarity_matrix, dim=1)[0]  # [q_len]
        
        # 求和得到最终分数
        score = torch.sum(max_similarities)
        
        return score
    
    def predict_relevance(self, query, document):
        """
        预测query-document相关性
        """
        # 编码query和document
        query_emb, query_mask = self.encode_query(query)
        doc_emb, doc_mask = self.encode_document(document)
        
        # 延迟交互
        score = self.late_interaction(query_emb, query_mask, doc_emb, doc_mask)
        
        return score.item()

def compare_architectures():
    """
    对比不同检索架构
    """
    print("🔍 检索架构对比：Cross-Encoder vs Bi-Encoder vs ColBERT")
    print("=" * 70)
    
    architectures = {
        "Cross-Encoder": {
            "交互方式": "早期交互 (Early Interaction)",
            "表示粒度": "整体表示",
            "计算复杂度": "O(Q×D) - 每对都要重新计算",
            "精度": "最高",
            "速度": "最慢",
            "可扩展性": "差",
            "预计算": "不支持"
        },
        "Bi-Encoder": {
            "交互方式": "无交互 (No Interaction)", 
            "表示粒度": "整体表示",
            "计算复杂度": "O(Q+D) - 可预计算",
            "精度": "中等",
            "速度": "最快",
            "可扩展性": "最好",
            "预计算": "完全支持"
        },
        "ColBERT": {
            "交互方式": "延迟交互 (Late Interaction)",
            "表示粒度": "Token级表示",
            "计算复杂度": "O(Q×D×L) - 但可部分预计算",
            "精度": "高",
            "速度": "中等",
            "可扩展性": "好",
            "预计算": "部分支持"
        }
    }
    
    for arch_name, features in architectures.items():
        print(f"\n📊 {arch_name}:")
        for feature, value in features.items():
            print(f"  {feature}: {value}")

def demonstrate_late_interaction():
    """
    演示延迟交互的工作原理
    """
    print("\n🧠 ColBERT 延迟交互演示")
    print("=" * 40)
    
    # 模拟token embeddings
    query_tokens = ["[Q]", "machine", "learning", "algorithms"]
    doc_tokens = ["[D]", "machine", "learning", "is", "a", "subset", "of", "AI"]
    
    print("Query tokens:", query_tokens)
    print("Document tokens:", doc_tokens)
    print()
    
    # 模拟相似度计算
    print("延迟交互过程:")
    print("1. 对每个query token，找到与document中最相似的token")
    print("2. 计算MaxSim分数")
    print("3. 求和得到最终相关性分数")
    print()
    
    # 示例相似度矩阵
    similarities = {
        "machine": {"machine": 1.0, "learning": 0.2, "is": 0.1, "a": 0.1, "subset": 0.1, "of": 0.1, "AI": 0.3},
        "learning": {"machine": 0.2, "learning": 1.0, "is": 0.1, "a": 0.1, "subset": 0.2, "of": 0.1, "AI": 0.4},
        "algorithms": {"machine": 0.3, "learning": 0.4, "is": 0.1, "a": 0.1, "subset": 0.2, "of": 0.1, "AI": 0.5}
    }
    
    total_score = 0
    for q_token in ["machine", "learning", "algorithms"]:
        max_sim = max(similarities[q_token].values())
        best_match = max(similarities[q_token], key=similarities[q_token].get)
        print(f"'{q_token}' -> 最佳匹配: '{best_match}' (相似度: {max_sim:.2f})")
        total_score += max_sim
    
    print(f"\n最终相关性分数: {total_score:.2f}")

def colbert_advantages():
    """
    ColBERT的优势分析
    """
    print("\n✨ ColBERT 的优势")
    print("=" * 30)
    
    advantages = [
        "🎯 精度高：token级交互比单向量相似度更精确",
        "⚡ 效率好：比Cross-Encoder快，比Bi-Encoder精确",
        "🔄 可扩展：支持部分预计算和索引优化",
        "🧩 细粒度：能够捕捉局部匹配信息",
        "🛠️ 灵活性：可以分析哪些tokens贡献了匹配分数"
    ]
    
    for advantage in advantages:
        print(advantage)
    
    print("\n🎯 适用场景:")
    scenarios = [
        "需要高精度的检索任务",
        "中等规模的文档库",
        "需要可解释性的匹配结果",
        "对速度和精度都有要求的应用"
    ]
    
    for scenario in scenarios:
        print(f"• {scenario}")

if __name__ == "__main__":
    print("ColBERTv2 延迟交互架构演示")
    print("=" * 60)
    
    # 架构对比
    compare_architectures()
    
    # 延迟交互演示
    demonstrate_late_interaction()
    
    # 优势分析
    colbert_advantages()
    
    print("\n🔗 与DPR的关系:")
    print("- DPR: 传统Bi-Encoder，单向量表示")
    print("- ColBERT: 改进的架构，token级表示 + 延迟交互")
    print("- ColBERT在精度和效率之间找到了更好的平衡点")
