"""
Jupyter Notebook 专用的缓存路径设置模块
解决notebook中环境变量读取问题
"""

import os
import sys
from pathlib import Path

def setup_cache_for_notebook(custom_path=None):
    """
    为Jupyter Notebook设置模型缓存路径
    
    Args:
        custom_path (str): 自定义缓存路径，如果为None则使用默认路径
    """
    
    if custom_path is None:
        # 默认缓存路径
        custom_path = "/Users/<USER>/Documents/Code/models_cache"
    
    # 确保路径存在
    cache_path = Path(custom_path)
    cache_path.mkdir(parents=True, exist_ok=True)
    
    # 设置环境变量
    os.environ["TRANSFORMERS_CACHE"] = str(cache_path)
    os.environ["HF_HOME"] = str(cache_path)
    os.environ["HF_HUB_CACHE"] = str(cache_path)
    
    print(f"✅ 缓存路径已设置为: {cache_path}")
    print(f"📁 目录是否存在: {cache_path.exists()}")
    
    return str(cache_path)

def verify_cache_setup():
    """
    验证缓存设置是否生效
    """
    print("\n🔍 环境变量验证:")
    cache_vars = ["TRANSFORMERS_CACHE", "HF_HOME", "HF_HUB_CACHE"]
    
    for var in cache_vars:
        value = os.environ.get(var)
        status = "✅" if value else "❌"
        print(f"{status} {var}: {value}")
    
    # 检查transformers库的实际缓存路径
    try:
        from transformers.utils import TRANSFORMERS_CACHE
        print(f"\n📦 Transformers实际使用路径: {TRANSFORMERS_CACHE}")
        return True
    except ImportError:
        print("⚠️  transformers库未安装")
        return False

def check_model_files(model_name="facebook/dpr-question_encoder-single-nq-base"):
    """
    检查指定模型是否已缓存
    
    Args:
        model_name (str): 模型名称
    """
    cache_dir = os.environ.get("TRANSFORMERS_CACHE")
    if not cache_dir:
        print("❌ 缓存路径未设置")
        return False
    
    # 转换模型名称为缓存目录格式
    model_cache_name = model_name.replace("/", "--")
    model_cache_path = Path(cache_dir) / f"models--{model_cache_name}"
    
    print(f"\n🔍 检查模型缓存:")
    print(f"模型: {model_name}")
    print(f"缓存路径: {model_cache_path}")
    print(f"是否存在: {'✅' if model_cache_path.exists() else '❌'}")
    
    if model_cache_path.exists():
        # 列出缓存文件
        files = list(model_cache_path.rglob("*"))
        print(f"缓存文件数量: {len(files)}")
        for file in files[:5]:  # 只显示前5个文件
            print(f"  📄 {file.name}")
        if len(files) > 5:
            print(f"  ... 还有 {len(files) - 5} 个文件")
    
    return model_cache_path.exists()

# 便捷函数：一键设置
def quick_setup(custom_path=None):
    """
    一键设置缓存路径并验证
    """
    print("🚀 开始设置Hugging Face模型缓存路径...")
    
    # 设置缓存路径
    cache_path = setup_cache_for_notebook(custom_path)
    
    # 验证设置
    verify_cache_setup()
    
    print("\n💡 使用提示:")
    print("1. 在导入transformers模型之前运行此设置")
    print("2. 如果已经导入过transformers，请重启notebook kernel")
    print("3. 模型下载后会保存在指定的缓存目录中")
    
    return cache_path

if __name__ == "__main__":
    # 如果直接运行此脚本
    quick_setup()
