#%% md
# PyTorch Lightning 快速入门

这是一个简化的PyTorch Lightning教程，帮助您快速上手。
#%%
# 导入必要的库
import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from pytorch_lightning import Trainer
from torch.utils.data import DataLoader
import torchvision
from torchvision import transforms

print(f"PyTorch Lightning version: {pl.__version__}")
#%% md
## 步骤1: 创建Lightning模型

继承`pl.LightningModule`并实现必要的方法：
#%%
class SimpleModel(pl.LightningModule):
    def __init__(self):
        super().__init__()
        self.layer = nn.Linear(28*28, 10)
    
    def forward(self, x):
        return self.layer(x.view(x.size(0), -1))
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        self.log('train_loss', loss)
        return loss
    
    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.001)

model = SimpleModel()
print("模型创建完成！")
#%% md
## 步骤2: 准备数据
#%%
# 数据变换
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.1307,), (0.3081,))
])

# 下载和加载数据
train_dataset = torchvision.datasets.MNIST(
    root='./data', train=True, download=True, transform=transform
)
train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)

print(f"训练数据集大小: {len(train_dataset)}")
#%% md
## 步骤3: 训练模型
#%%
# 创建训练器
trainer = Trainer(
    max_epochs=2,  # 只训练2个epoch用于演示
    accelerator='auto',
    devices='auto'
)

# 开始训练
print("开始训练...")
trainer.fit(model, train_loader)
print("训练完成！")
#%% md
## 步骤4: 添加验证（可选）
#%%
class ImprovedModel(pl.LightningModule):
    def __init__(self):
        super().__init__()
        self.layer = nn.Linear(28*28, 10)
    
    def forward(self, x):
        return self.layer(x.view(x.size(0), -1))
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        self.log('train_loss', loss, prog_bar=True)
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        acc = (y_hat.argmax(dim=1) == y).float().mean()
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_acc', acc, prog_bar=True)
        return loss
    
    def configure_optimizers(self):
        return torch.optim.Adam(self.parameters(), lr=0.001)

# 准备验证数据
val_dataset = torchvision.datasets.MNIST(
    root='./data', train=False, download=True, transform=transform
)
val_loader = DataLoader(val_dataset, batch_size=64)

# 创建改进的模型和训练器
improved_model = ImprovedModel()
trainer = Trainer(max_epochs=3, accelerator='auto', devices='auto')

# 训练（包含验证）
print("开始训练（包含验证）...")
trainer.fit(improved_model, train_loader, val_loader)
print("训练完成！")
#%% md
## 关键概念总结

1. **LightningModule**: 继承这个类来定义您的模型
2. **training_step()**: 定义单个训练步骤
3. **validation_step()**: 定义单个验证步骤（可选）
4. **configure_optimizers()**: 配置优化器
5. **Trainer**: 控制训练过程的主要类
6. **self.log()**: 记录指标，`prog_bar=True`会在进度条中显示

## 优势
- 自动处理GPU/CPU切换
- 自动处理训练循环
- 内置日志记录
- 易于扩展到多GPU训练
- 减少样板代码

这就是PyTorch Lightning的基础用法！查看完整教程了解更多高级功能。