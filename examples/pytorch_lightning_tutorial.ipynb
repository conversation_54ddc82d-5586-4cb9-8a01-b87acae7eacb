#%% md
# PyTorch Lightning 完整学习教程

本教程将带您从零开始学习PyTorch Lightning，涵盖基础概念、核心组件和高级用法。

## 目录
1. [PyTorch Lightning 简介](#1-pytorch-lightning-简介)
2. [基础概念和核心组件](#2-基础概念和核心组件)
3. [创建第一个Lightning模型](#3-创建第一个lightning模型)
4. [数据处理 - DataModule](#4-数据处理---datamodule)
5. [训练和验证循环](#5-训练和验证循环)
6. [回调函数 (Callbacks)](#6-回调函数-callbacks)
7. [日志记录和监控](#7-日志记录和监控)
8. [多GPU训练](#8-多gpu训练)
9. [高级特性](#9-高级特性)
10. [实际项目示例](#10-实际项目示例)
#%% md
## 1. PyTorch Lightning 简介

PyTorch Lightning是一个轻量级的PyTorch包装器，它：
- 简化了深度学习代码的组织
- 自动处理训练循环、验证、测试
- 提供了多GPU、TPU支持
- 集成了日志记录和实验跟踪
- 减少了样板代码
#%%
# 安装必要的包
# !pip install pytorch-lightning torch torchvision matplotlib seaborn wandb tensorboard torchmetrics

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping, LearningRateMonitor
from pytorch_lightning.loggers import TensorBoardLogger, WandbLogger

import torchvision
from torchvision import transforms
from torch.utils.data import DataLoader, random_split

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Optional
import os

print(f"PyTorch version: {torch.__version__}")
print(f"PyTorch Lightning version: {pl.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA devices: {torch.cuda.device_count()}")
#%% md
## 2. 基础概念和核心组件

### 核心组件：
1. **LightningModule**: 定义模型、损失函数、优化器
2. **LightningDataModule**: 处理数据加载和预处理
3. **Trainer**: 控制训练过程
4. **Callbacks**: 在训练过程中执行特定操作
5. **Loggers**: 记录训练指标和可视化

### Lightning vs 原生PyTorch对比：

**原生PyTorch需要手动处理：**
- 训练循环
- 验证循环
- GPU/CPU切换
- 梯度清零
- 反向传播
- 优化器步骤
- 学习率调度
- 检查点保存

**Lightning自动处理这些！**
#%% md
## 3. 创建第一个Lightning模型

让我们从一个简单的图像分类模型开始：
#%%
class SimpleCNN(pl.LightningModule):
    def __init__(self, num_classes=10, learning_rate=1e-3, dropout_rate=0.5):
        super().__init__()
        
        # 保存超参数 - 这样可以在检查点中自动保存和恢复
        self.save_hyperparameters()
        
        # 定义网络结构
        self.conv1 = nn.Conv2d(1, 32, 3, 1)
        self.conv2 = nn.Conv2d(32, 64, 3, 1)
        self.dropout1 = nn.Dropout(0.25)
        self.dropout2 = nn.Dropout(dropout_rate)
        self.fc1 = nn.Linear(9216, 128)
        self.fc2 = nn.Linear(128, num_classes)
        
        # 用于计算准确率 - 使用torchmetrics
        try:
            from torchmetrics import Accuracy
            self.train_acc = Accuracy(task='multiclass', num_classes=num_classes)
            self.val_acc = Accuracy(task='multiclass', num_classes=num_classes)
            self.test_acc = Accuracy(task='multiclass', num_classes=num_classes)
        except ImportError:
            print("请安装torchmetrics: pip install torchmetrics")
            # 简单的准确率计算作为备选
            self.train_acc = None
            self.val_acc = None
            self.test_acc = None
        
    def forward(self, x):
        """前向传播 - 定义数据如何通过网络"""
        x = self.conv1(x)
        x = F.relu(x)
        x = self.conv2(x)
        x = F.relu(x)
        x = F.max_pool2d(x, 2)
        x = self.dropout1(x)
        x = torch.flatten(x, 1)
        x = self.fc1(x)
        x = F.relu(x)
        x = self.dropout2(x)
        x = self.fc2(x)
        return F.log_softmax(x, dim=1)
    
    def training_step(self, batch, batch_idx):
        """定义单个训练步骤"""
        x, y = batch
        logits = self(x)
        loss = F.nll_loss(logits, y)
        
        # 计算准确率
        if self.train_acc is not None:
            preds = torch.argmax(logits, dim=1)
            self.train_acc(preds, y)
            
            # 记录指标 - prog_bar=True 会在进度条中显示
            self.log('train_loss', loss, prog_bar=True)
            self.log('train_acc', self.train_acc, prog_bar=True)
        else:
            self.log('train_loss', loss, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        """定义单个验证步骤"""
        x, y = batch
        logits = self(x)
        loss = F.nll_loss(logits, y)
        
        if self.val_acc is not None:
            preds = torch.argmax(logits, dim=1)
            self.val_acc(preds, y)
            
            self.log('val_loss', loss, prog_bar=True)
            self.log('val_acc', self.val_acc, prog_bar=True)
        else:
            self.log('val_loss', loss, prog_bar=True)
        
        return loss
    
    def test_step(self, batch, batch_idx):
        """定义单个测试步骤"""
        x, y = batch
        logits = self(x)
        loss = F.nll_loss(logits, y)
        
        if self.test_acc is not None:
            preds = torch.argmax(logits, dim=1)
            self.test_acc(preds, y)
            
            self.log('test_loss', loss)
            self.log('test_acc', self.test_acc)
        else:
            self.log('test_loss', loss)
        
        return loss
    
    def configure_optimizers(self):
        """配置优化器和学习率调度器"""
        optimizer = torch.optim.Adam(self.parameters(), lr=self.hparams.learning_rate)
        
        # 可选：添加学习率调度器
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
        
        return {
            "optimizer": optimizer,
            "lr_scheduler": {
                "scheduler": scheduler,
                "monitor": "val_loss",  # 监控的指标
            },
        }

# 创建模型实例
model = SimpleCNN(num_classes=10, learning_rate=1e-3, dropout_rate=0.5)
print("模型创建成功！")
print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
print(f"超参数: {model.hparams}")
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 数据处理 - DataModule\n",
    "\n",
    "LightningDataModule是组织数据相关代码的最佳实践方式："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class MNISTDataModule(pl.LightningDataModule):\n",
    "    def __init__(self, data_dir: str = \"./data\", batch_size: int = 64, num_workers: int = 4):\n",
    "        super().__init__()\n",
    "        self.data_dir = data_dir\n",
    "        self.batch_size = batch_size\n",
    "        self.num_workers = num_workers\n",
    "        \n",
    "        # 定义数据变换\n",
    "        self.transform = transforms.Compose([\n",
    "            transforms.ToTensor(),\n",
    "            transforms.Normalize((0.1307,), (0.3081,))  # MNIST的均值和标准差\n",
    "        ])\n",
    "        \n",
    "        # 数据增强（仅用于训练）\n",
    "        self.train_transform = transforms.Compose([\n",
    "            transforms.RandomRotation(10),\n",
    "            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1)),\n",
    "            transforms.ToTensor(),\n",
    "            transforms.Normalize((0.1307,), (0.3081,))\n",
    "        ])\n",
    "    \n",
    "    def prepare_data(self):\n",
    "        \"\"\"下载数据（只在主进程中执行一次）\"\"\"\n",
    "        torchvision.datasets.MNIST(self.data_dir, train=True, download=True)\n",
    "        torchvision.datasets.MNIST(self.data_dir, train=False, download=True)\n",
    "    \n",
    "    def setup(self, stage: Optional[str] = None):\n",
    "        \"\"\"设置数据集（在每个进程中执行）\"\"\"\n",
    "        \n",
    "        # 训练和验证数据\n",
    "        if stage == \"fit\" or stage is None:\n",
    "            mnist_full = torchvision.datasets.MNIST(\n",
    "                self.data_dir, train=True, transform=self.train_transform\n",
    "            )\n",
    "            self.mnist_train, self.mnist_val = random_split(mnist_full, [55000, 5000])\n",
    "            \n",
    "            # 验证集使用不同的变换（无数据增强）\n",
    "            self.mnist_val.dataset = torchvision.datasets.MNIST(\n",
    "                self.data_dir, train=True, transform=self.transform\n",
    "            )\n",
    "        \n",
    "        # 测试数据\n",
    "        if stage == \"test\" or stage is None:\n",
    "            self.mnist_test = torchvision.datasets.MNIST(\n",
    "                self.data_dir, train=False, transform=self.transform\n",
    "            )\n",
    "    \n",
    "    def train_dataloader(self):\n",
    "        return DataLoader(\n",
    "            self.mnist_train, \n",
    "            batch_size=self.batch_size, \n",
    "            shuffle=True, \n",
    "            num_workers=self.num_workers,\n",
    "            persistent_workers=True if self.num_workers > 0 else False\n",
    "        )\n",
    "    \n",
    "    def val_dataloader(self):\n",
    "        return DataLoader(\n",
    "            self.mnist_val, \n",
    "            batch_size=self.batch_size, \n",
    "            shuffle=False, \n",
    "            num_workers=self.num_workers,\n",
    "            persistent_workers=True if self.num_workers > 0 else False\n",
    "        )\n",
    "    \n",
    "    def test_dataloader(self):\n",
    "        return DataLoader(\n",
    "            self.mnist_test, \n",
    "            batch_size=self.batch_size, \n",
    "            shuffle=False, \n",
    "            num_workers=self.num_workers,\n",
    "            persistent_workers=True if self.num_workers > 0 else False\n",
    "        )\n",
    "\n",
    "# 创建数据模块\n",
    "data_module = MNISTDataModule(batch_size=128, num_workers=2)\n",
    "print(\"数据模块创建成功！\")\n",
    "\n",
    "# 准备数据\n",
    "data_module.prepare_data()\n",
    "data_module.setup()\n",
    "\n",
    "print(f\"训练集大小: {len(data_module.mnist_train)}\")\n",
    "print(f\"验证集大小: {len(data_module.mnist_val)}\")\n",
    "print(f\"测试集大小: {len(data_module.mnist_test)}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 训练和验证循环\n",
    "\n",
    "现在让我们使用Trainer来训练模型："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 创建模型和数据模块\n",
    "model = SimpleCNN(num_classes=10, learning_rate=1e-3)\n",
    "data_module = MNISTDataModule(batch_size=128, num_workers=2)\n",
    "\n",
    "# 配置Trainer\n",
    "trainer = Trainer(\n",
    "    max_epochs=3,  # 为了演示，只训练3个epoch\n",
    "    accelerator=\"auto\",  # 自动选择GPU或CPU\n",
    "    devices=\"auto\",  # 自动选择设备数量\n",
    "    logger=True,  # 启用默认logger\n",
    "    enable_checkpointing=True,  # 启用检查点\n",
    "    enable_progress_bar=True,  # 显示进度条\n",
    "    enable_model_summary=True,  # 显示模型摘要\n",
    "    deterministic=True,  # 确保可重现性\n",
    ")\n",
    "\n",
    "print(\"开始训练...\")\n",
    "trainer.fit(model, data_module)\n",
    "\n",
    "print(\"\\n训练完成！\")\n",
    "print(f\"最佳验证损失: {trainer.callback_metrics.get('val_loss', 'N/A')}\")\n",
    "print(f\"最佳验证准确率: {trainer.callback_metrics.get('val_acc', 'N/A')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 回调函数 (Callbacks)\n",
    "\n",
    "回调函数允许您在训练过程的特定时刻执行自定义操作："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 定义回调函数\n",
    "callbacks = [\n",
    "    # 模型检查点 - 保存最佳模型\n",
    "    ModelCheckpoint(\n",
    "        monitor='val_loss',  # 监控的指标\n",
    "        mode='min',  # 最小化val_loss\n",
    "        save_top_k=1,  # 只保存最好的1个模型\n",
    "        save_last=True,  # 同时保存最后一个epoch的模型\n",
    "        filename='best-{epoch:02d}-{val_loss:.2f}',\n",
    "        auto_insert_metric_name=False\n",
    "    ),\n",
    "    \n",
    "    # 早停 - 防止过拟合\n",
    "    EarlyStopping(\n",
    "        monitor='val_loss',\n",
    "        patience=5,  # 5个epoch没有改善就停止\n",
    "        mode='min',\n",
    "        verbose=True\n",
    "    ),\n",
    "    \n",
    "    # 学习率监控\n",
    "    LearningRateMonitor(logging_interval='epoch')\n",
    "]\n",
    "\n",
    "# 自定义回调函数示例\n",
    "class CustomCallback(pl.Callback):\n",
    "    def on_train_start(self, trainer, pl_module):\n",
    "        print(\"🚀 训练开始！\")\n",
    "    \n",
    "    def on_train_epoch_end(self, trainer, pl_module):\n",
    "        # 在每个epoch结束时打印信息\n",
    "        current_epoch = trainer.current_epoch\n",
    "        train_loss = trainer.callback_metrics.get('train_loss', 0)\n",
    "        val_loss = trainer.callback_metrics.get('val_loss', 0)\n",
    "        print(f\"📊 Epoch {current_epoch}: train_loss={train_loss:.4f}, val_loss={val_loss:.4f}\")\n",
    "    \n",
    "    def on_train_end(self, trainer, pl_module):\n",
    "        print(\"✅ 训练完成！\")\n",
    "\n",
    "# 添加自定义回调\n",
    "callbacks.append(CustomCallback())\n",
    "\n",
    "print(\"回调函数配置完成！\")\n",
    "for i, callback in enumerate(callbacks):\n",
    "    print(f\"{i+1}. {type(callback).__name__}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 日志记录和监控\n",
    "\n",
    "PyTorch Lightning支持多种日志记录方式："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# TensorBoard Logger\n",
    "tb_logger = TensorBoardLogger(\n",
    "    save_dir=\"logs/\",\n",
    "    name=\"mnist_experiment\",\n",
    "    version=\"v1\"\n",
    ")\n",
    "\n",
    "# 如果您有Weights & Biases账户，可以使用WandbLogger\n",
    "# wandb_logger = WandbLogger(\n",
    "#     project=\"pytorch-lightning-tutorial\",\n",
    "#     name=\"mnist-experiment\"\n",
    "# )\n",
    "\n",
    "# 使用回调和日志记录器训练模型\n",
    "model = SimpleCNN(num_classes=10, learning_rate=1e-3)\n",
    "data_module = MNISTDataModule(batch_size=128, num_workers=2)\n",
    "\n",
    "trainer = Trainer(\n",
    "    max_epochs=5,\n",
    "    callbacks=callbacks,\n",
    "    logger=tb_logger,  # 使用TensorBoard记录\n",
    "    accelerator=\"auto\",\n",
    "    devices=\"auto\",\n",
    "    log_every_n_steps=50,  # 每50步记录一次\n",
    ")\n",
    "\n",
    "print(\"开始带有回调和日志记录的训练...\")\n",
    "# trainer.fit(model, data_module)  # 取消注释以运行训练\n",
    "\n",
    "print(\"\\n要查看TensorBoard日志，运行:\")\n",
    "print(\"tensorboard --logdir=logs/\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. 多GPU训练\n",
    "\n",
    "PyTorch Lightning让多GPU训练变得非常简单："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 多GPU训练配置\n",
    "def create_multi_gpu_trainer():\n",
    "    \"\"\"创建多GPU训练器\"\"\"\n",
    "    \n",
    "    if torch.cuda.device_count() > 1:\n",
    "        print(f\"检测到 {torch.cuda.device_count()} 个GPU\")\n",
    "        \n",
    "        # 数据并行训练\n",
    "        trainer = Trainer(\n",
    "            max_epochs=10,\n",
    "            accelerator=\"gpu\",\n",
    "            devices=-1,  # 使用所有可用GPU\n",
    "            strategy=\"ddp\",  # 分布式数据并行\n",
    "            callbacks=callbacks,\n",
    "            logger=tb_logger,\n",
    "            precision=16,  # 混合精度训练\n",
    "            sync_batchnorm=True,  # 同步BatchNorm\n",
    "        )\n",
    "        \n",
    "    elif torch.cuda.device_count() == 1:\n",
    "        print(\"检测到 1 个GPU\")\n",
    "        trainer = Trainer(\n",
    "            max_epochs=10,\n",
    "            accelerator=\"gpu\",\n",
    "            devices=1,\n",
    "            callbacks=callbacks,\n",
    "            logger=tb_logger,\n",
    "            precision=16,  # 混合精度训练\n",
    "        )\n",
    "    else:\n",
    "        print(\"未检测到GPU，使用CPU\")\n",
    "        trainer = Trainer(\n",
    "            max_epochs=10,\n",
    "            accelerator=\"cpu\",\n",
    "            callbacks=callbacks,\n",
    "            logger=tb_logger,\n",
    "        )\n",
    "    \n",
    "    return trainer\n",
    "\n",
    "# 创建多GPU训练器\n",
    "multi_gpu_trainer = create_multi_gpu_trainer()\n",
    "print(f\"训练器配置: {multi_gpu_trainer.accelerator}, 设备数: {multi_gpu_trainer.num_devices}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 9. 高级特性\n",
    "\n",
    "### 9.1 梯度累积和梯度裁剪"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 高级训练配置\n",
    "advanced_trainer = Trainer(\n",
    "    max_epochs=10,\n",
    "    \n",
    "    # 梯度累积 - 模拟更大的batch size\n",
    "    accumulate_grad_batches=4,  # 每4个batch累积一次梯度\n",
    "    \n",
    "    # 梯度裁剪 - 防止梯度爆炸\n",
    "    gradient_clip_val=1.0,\n",
    "    gradient_clip_algorithm=\"norm\",\n",
    "    \n",
    "    # 混合精度训练\n",
    "    precision=16,\n",
    "    \n",
    "    # 验证频率\n",
    "    val_check_interval=0.5,  # 每半个epoch验证一次\n",
    "    \n",
    "    # 限制训练/验证批次数（用于快速测试）\n",
    "    limit_train_batches=0.1,  # 只使用10%的训练数据\n",
    "    limit_val_batches=0.1,    # 只使用10%的验证数据\n",
    "    \n",
    "    # 快速开发运行\n",
    "    fast_dev_run=False,  # 设为True可快速测试一个batch\n",
    "    \n",
    "    callbacks=callbacks,\n",
    "    logger=tb_logger,\n",
    ")\n",
    "\n",
    "print(\"高级训练器配置完成！\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 9.2 模型推理和预测"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 模型推理示例\n",
    "def demonstrate_inference():\n",
    "    \"\"\"演示模型推理\"\"\"\n",
    "    \n",
    "    # 创建一个简单的模型用于演示\n",
    "    model = SimpleCNN(num_classes=10, learning_rate=1e-3)\n",
    "    model.eval()  # 设置为评估模式\n",
    "    \n",
    "    # 创建一些假数据\n",
    "    batch_size = 4\n",
    "    dummy_input = torch.randn(batch_size, 1, 28, 28)\n",
    "    \n",
    "    # 推理\n",
    "    with torch.no_grad():\n",
    "        predictions = model(dummy_input)\n",
    "        predicted_classes = torch.argmax(predictions, dim=1)\n",
    "    \n",
    "    print(f\"输入形状: {dummy_input.shape}\")\n",
    "    print(f\"预测概率形状: {predictions.shape}\")\n",
    "    print(f\"预测类别: {predicted_classes.tolist()}\")\n",
    "    \n",
    "    return model, dummy_input, predictions\n",
    "\n",
    "# 运行推理演示\n",
    "model, input_data, preds = demonstrate_inference()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 9.3 检查点保存和加载"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 检查点操作\n",
    "def checkpoint_operations():\n",
    "    \"\"\"演示检查点保存和加载\"\"\"\n",
    "    \n",
    "    # 创建模型\n",
    "    model = SimpleCNN(num_classes=10, learning_rate=1e-3)\n",
    "    \n",
    "    # 手动保存检查点\n",
    "    checkpoint_path = \"manual_checkpoint.ckpt\"\n",
    "    trainer = Trainer(max_epochs=1, enable_checkpointing=False)\n",
    "    \n",
    "    # 保存检查点\n",
    "    trainer.save_checkpoint(checkpoint_path)\n",
    "    print(f\"检查点已保存到: {checkpoint_path}\")\n",
    "    \n",
    "    # 从检查点加载模型\n",
    "    if os.path.exists(checkpoint_path):\n",
    "        loaded_model = SimpleCNN.load_from_checkpoint(checkpoint_path)\n",
    "        print(\"模型已从检查点加载\")\n",
    "        print(f\"加载的超参数: {loaded_model.hparams}\")\n",
    "        \n",
    "        # 清理\n",
    "        os.remove(checkpoint_path)\n",
    "        print(\"临时检查点文件已删除\")\n",
    "    \n",
    "    return loaded_model if 'loaded_model' in locals() else model\n",
    "\n",
    "# 运行检查点操作演示\n",
    "# loaded_model = checkpoint_operations()  # 取消注释以运行"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 10. 实际项目示例\n",
    "\n",
    "让我们创建一个完整的项目示例，展示如何组织一个真实的深度学习项目："
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 完整项目示例\n",
    "class MNISTProject:\n",
    "    \"\"\"完整的MNIST项目类\"\"\"\n",
    "    \n",
    "    def __init__(self, config=None):\n",
    "        self.config = config or {\n",
    "            'batch_size': 128,\n",
    "            'learning_rate': 1e-3,\n",
    "            'max_epochs': 10,\n",
    "            'num_workers': 2,\n",
    "            'dropout_rate': 0.5,\n",
    "        }\n",
    "        \n",
    "        self.model = None\n",
    "        self.data_module = None\n",
    "        self.trainer = None\n",
    "    \n",
    "    def setup(self):\n",
    "        \"\"\"设置模型、数据和训练器\"\"\"\n",
    "        \n",
    "        # 创建模型\n",
    "        self.model = SimpleCNN(\n",
    "            num_classes=10,\n",
    "            learning_rate=self.config['learning_rate'],\n",
    "            dropout_rate=self.config['dropout_rate']\n",
    "        )\n",
    "        \n",
    "        # 创建数据模块\n",
    "        self.data_module = MNISTDataModule(\n",
    "            batch_size=self.config['batch_size'],\n",
    "            num_workers=self.config['num_workers']\n",
    "        )\n",
    "        \n",
    "        # 设置回调\n",
    "        callbacks = [\n",
    "            ModelCheckpoint(\n",
    "                monitor='val_acc',\n",
    "                mode='max',\n",
    "                save_top_k=1,\n",
    "                filename='best-model-{epoch:02d}-{val_acc:.2f}'\n",
    "            ),\n",
    "            EarlyStopping(\n",
    "                monitor='val_loss',\n",
    "                patience=3,\n",
    "                mode='min'\n",
    "            ),\n",
    "            LearningRateMonitor(logging_interval='epoch')\n",
    "        ]\n",
    "        \n",
    "        # 创建训练器\n",
    "        self.trainer = Trainer(\n",
    "            max_epochs=self.config['max_epochs'],\n",
    "            callbacks=callbacks,\n",
    "            logger=TensorBoardLogger('logs/', name='mnist_project'),\n",
    "            accelerator='auto',\n",
    "            devices='auto',\n",
    "            precision=16 if torch.cuda.is_available() else 32,\n",
    "        )\n",
    "        \n",
    "        print(\"项目设置完成！\")\n",
    "    \n",
    "    def train(self):\n",
    "        \"\"\"训练模型\"\"\"\n",
    "        if not all([self.model, self.data_module, self.trainer]):\n",
    "            self.setup()\n",
    "        \n",
    "        print(\"开始训练...\")\n",
    "        self.trainer.fit(self.model, self.data_module)\n",
    "        print(\"训练完成！\")\n",
    "    \n",
    "    def test(self):\n",
    "        \"\"\"测试模型\"\"\"\n",
    "        if not self.trainer:\n",
    "            raise ValueError(\"请先训练模型\")\n",
    "        \n",
    "        print(\"开始测试...\")\n",
    "        test_results = self.trainer.test(self.model, self.data_module)\n",
    "        print(f\"测试结果: {test_results}\")\n",
    "        return test_results\n",
    "    \n",
    "    def predict(self, data_loader=None):\n",
    "        \"\"\"进行预测\"\"\"\n",
    "        if not self.trainer:\n",
    "            raise ValueError(\"请先训练模型\")\n",
    "        \n",
    "        if data_loader is None:\n",
    "            data_loader = self.data_module.test_dataloader()\n",
    "        \n",
    "        predictions = self.trainer.predict(self.model, data_loader)\n",
    "        return predictions\n",
    "\n",
    "# 创建项目实例\n",
    "project = MNISTProject()\n",
    "project.setup()\n",
    "\n",
    "print(\"\\n项目已准备就绪！\")\n",
    "print(\"运行 project.train() 开始训练\")\n",
    "print(\"运行 project.test() 进行测试\")\n",
    "print(\"运行 project.predict() 进行预测\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 总结\n",
    "\n",
    "通过本教程，您已经学会了：\n",
    "\n",
    "1. **PyTorch Lightning的核心概念**\n",
    "   - LightningModule: 组织模型代码\n",
    "   - LightningDataModule: 组织数据代码\n",
    "   - Trainer: 控制训练过程\n",
    "\n",
    "2. **关键特性**\n",
    "   - 自动化训练循环\n",
    "   - 回调函数系统\n",
    "   - 多种日志记录方式\n",
    "   - 多GPU训练支持\n",
    "   - 检查点管理\n",
    "\n",
    "3. **最佳实践**\n",
    "   - 代码组织结构\n",
    "   - 超参数管理\n",
    "   - 实验跟踪\n",
    "   - 模型部署准备\n",
    "\n",
    "### 下一步建议：\n",
    "- 尝试在自己的数据集上应用这些概念\n",
    "- 探索更多的回调函数和日志记录器\n",
    "- 学习分布式训练和模型优化技术\n",
    "- 查看PyTorch Lightning官方文档获取更多高级功能\n",
    "\n",
    "### 有用的资源：\n",
    "- [PyTorch Lightning官方文档](https://pytorch-lightning.readthedocs.io/)\n",
    "- [PyTorch Lightning GitHub](https://github.com/PyTorchLightning/pytorch-lightning)\n",
    "- [社区示例](https://github.com/PyTorchLightning/pytorch-lightning/tree/master/pl_examples)"
   ]
  }
 ]
 },
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}