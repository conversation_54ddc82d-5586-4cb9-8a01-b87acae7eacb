#%% md
# 残差量化 (Residual Quantization) 详解

本笔记本详细介绍残差量化的原理、实现和在ColBERTv2中的应用。
#%%
! pip install seaborn scikit-learn 
#%%
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
import seaborn as sns



print("🎯 残差量化学习开始")
print("=" * 50)
#%% md
## 1. 什么是量化？

量化是将连续值映射到离散值的过程，目的是减少存储空间和计算复杂度。
#%%
def explain_basic_quantization():
    """解释基础量化概念"""
    
    print("📚 基础量化概念:")
    print("=" * 30)
    
    # 示例：将float32量化为int8
    original_values = np.array([0.1, 0.5, 0.8, 1.2, 1.7, 2.3, 2.8])
    
    # 简单线性量化
    min_val, max_val = original_values.min(), original_values.max()
    scale = (max_val - min_val) / 255  # int8范围: 0-255
    quantized = np.round((original_values - min_val) / scale).astype(np.uint8)
    
    # 反量化
    dequantized = quantized * scale + min_val
    
    print(f"原始值:     {original_values}")
    print(f"量化后:     {quantized}")
    print(f"反量化:     {dequantized}")
    print(f"量化误差:   {np.abs(original_values - dequantized)}")
    
    # 存储空间对比
    original_size = original_values.nbytes
    quantized_size = quantized.nbytes + 8  # +8 for scale and min_val
    
    print(f"\n💾 存储对比:")
    print(f"   原始大小: {original_size} bytes (float32)")
    print(f"   量化大小: {quantized_size} bytes (uint8 + metadata)")
    print(f"   压缩比: {original_size/quantized_size:.1f}x")

explain_basic_quantization()
#%% md
## 2. 向量量化 (Vector Quantization)

向量量化将高维向量映射到有限的代表向量集合（码本）。
#%%
class VectorQuantization:
    """向量量化演示"""
    
    def __init__(self, n_centroids=4):
        self.n_centroids = n_centroids
        self.centroids = None
        
    def fit(self, vectors):
        """训练码本（质心）"""
        kmeans = KMeans(n_clusters=self.n_centroids, random_state=42)
        kmeans.fit(vectors)
        self.centroids = kmeans.cluster_centers_
        return self
    
    def encode(self, vectors):
        """编码：找到最近的质心索引"""
        distances = np.linalg.norm(vectors[:, None] - self.centroids[None, :], axis=2)
        codes = np.argmin(distances, axis=1)
        return codes
    
    def decode(self, codes):
        """解码：根据索引返回质心向量"""
        return self.centroids[codes]
    
    def demonstrate(self):
        """演示向量量化过程"""
        
        # 生成示例数据
        np.random.seed(42)
        vectors = np.random.randn(100, 2)  # 100个2D向量
        
        # 训练量化器
        self.fit(vectors)
        
        # 量化过程
        codes = self.encode(vectors)
        reconstructed = self.decode(codes)
        
        # 计算量化误差
        mse = np.mean((vectors - reconstructed) ** 2)
        
        print(f"🔍 向量量化演示:")
        print(f"   - 原始向量数量: {len(vectors)}")
        print(f"   - 质心数量: {self.n_centroids}")
        print(f"   - 量化误差(MSE): {mse:.4f}")
        
        # 存储分析
        original_bits = len(vectors) * 2 * 32  # 2D float32
        quantized_bits = len(vectors) * np.log2(self.n_centroids) + self.n_centroids * 2 * 32
        
        print(f"\n💾 存储分析:")
        print(f"   - 原始存储: {original_bits} bits")
        print(f"   - 量化存储: {quantized_bits:.0f} bits")
        print(f"   - 压缩比: {original_bits/quantized_bits:.1f}x")
        
        return vectors, codes, reconstructed

# 演示向量量化
vq = VectorQuantization(n_centroids=4)
original, codes, reconstructed = vq.demonstrate()
#%% md
## 3. 残差量化的核心思想

残差量化的关键思想：**分层逼近**，每一层量化前一层的残差（误差）。
#%%
def explain_residual_concept():
    """解释残差量化的核心概念"""
    
    print("🧠 残差量化核心思想:")
    print("=" * 40)
    
    print("📝 基本流程:")
    steps = [
        "1. 第一层量化: 找到最接近的质心 c1",
        "2. 计算残差: r1 = x - c1", 
        "3. 第二层量化: 对残差r1进行量化，得到c2",
        "4. 计算新残差: r2 = r1 - c2",
        "5. 重复过程...",
        "6. 最终重构: x ≈ c1 + c2 + c3 + ..."
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print("\n🎯 核心优势:")
    advantages = [
        "逐层细化：每层都在减少量化误差",
        "更高精度：多层组合比单层更精确",
        "灵活控制：可以调整层数平衡精度和存储",
        "渐进式：可以根据需要增加更多层"
    ]
    
    for adv in advantages:
        print(f"   ✅ {adv}")

explain_residual_concept()
#%% md
## 4. 残差量化实现
#%%
class ResidualQuantization:
    """残差量化完整实现"""
    
    def __init__(self, dim=128, nbits=2, num_layers=1):
        self.dim = dim
        self.nbits = nbits
        self.num_centroids = 2 ** nbits  # 每层的质心数量
        self.num_layers = num_layers
        self.codebooks = []  # 存储每层的码本
        
        print(f"🔧 残差量化配置:")
        print(f"   - 向量维度: {dim}")
        print(f"   - 每层位数: {nbits} bits")
        print(f"   - 每层质心数: {self.num_centroids}")
        print(f"   - 量化层数: {num_layers}")
        print(f"   - 总编码位数: {nbits * num_layers} bits")
    
    def train(self, vectors):
        """训练残差量化器"""
        print(f"\n🎓 开始训练残差量化器...")
        
        current_vectors = vectors.copy()
        
        for layer in range(self.num_layers):
            print(f"   训练第 {layer+1} 层...")
            
            # 对当前向量进行K-means聚类
            kmeans = KMeans(n_clusters=self.num_centroids, random_state=42+layer)
            kmeans.fit(current_vectors)
            
            # 保存码本
            self.codebooks.append(kmeans.cluster_centers_)
            
            # 计算残差作为下一层的输入
            codes = kmeans.predict(current_vectors)
            reconstructed = kmeans.cluster_centers_[codes]
            current_vectors = current_vectors - reconstructed  # 残差
            
            # 计算当前层的量化误差
            mse = np.mean((current_vectors) ** 2)
            print(f"     第 {layer+1} 层残差MSE: {mse:.6f}")
        
        print("✅ 训练完成！")
        return self
    
    def encode(self, vectors):
        """编码向量为多层码字"""
        codes_list = []
        current_vectors = vectors.copy()
        
        for layer in range(self.num_layers):
            # 找到最近的质心
            distances = np.linalg.norm(
                current_vectors[:, None] - self.codebooks[layer][None, :], 
                axis=2
            )
            codes = np.argmin(distances, axis=1)
            codes_list.append(codes)
            
            # 计算残差
            reconstructed = self.codebooks[layer][codes]
            current_vectors = current_vectors - reconstructed
        
        return codes_list
    
    def decode(self, codes_list):
        """从多层码字重构向量"""
        reconstructed = np.zeros((len(codes_list[0]), self.dim))
        
        for layer in range(self.num_layers):
            codes = codes_list[layer]
            layer_contribution = self.codebooks[layer][codes]
            reconstructed += layer_contribution
        
        return reconstructed
    
    def demonstrate_compression(self, vectors):
        """演示压缩效果"""
        print(f"\n📊 压缩效果分析:")
        print("=" * 30)
        
        # 训练量化器
        self.train(vectors)
        
        # 编码和解码
        codes_list = self.encode(vectors)
        reconstructed = self.decode(codes_list)
        
        # 计算量化误差
        mse = np.mean((vectors - reconstructed) ** 2)
        
        # 存储分析
        num_vectors = len(vectors)
        original_size = num_vectors * self.dim * 4  # float32
        
        # 编码存储：每个向量需要 num_layers * nbits 位
        codes_size = num_vectors * self.num_layers * self.nbits / 8
        
        # 码本存储：每层需要存储 num_centroids 个 dim 维向量
        codebook_size = self.num_layers * self.num_centroids * self.dim * 4
        
        total_compressed_size = codes_size + codebook_size
        compression_ratio = original_size / total_compressed_size
        
        print(f"📈 结果统计:")
        print(f"   - 向量数量: {num_vectors}")
        print(f"   - 量化误差(MSE): {mse:.6f}")
        print(f"   - 原始大小: {original_size/1024:.1f} KB")
        print(f"   - 编码大小: {codes_size/1024:.1f} KB")
        print(f"   - 码本大小: {codebook_size/1024:.1f} KB")
        print(f"   - 总压缩大小: {total_compressed_size/1024:.1f} KB")
        print(f"   - 压缩比: {compression_ratio:.1f}x")
        print(f"   - 空间节省: {(1-1/compression_ratio)*100:.1f}%")
        
        return {
            'mse': mse,
            'compression_ratio': compression_ratio,
            'original_size': original_size,
            'compressed_size': total_compressed_size
        }

# 生成测试数据
np.random.seed(42)
test_vectors = np.random.randn(1000, 128).astype(np.float32)  # 1000个128维向量

print("🧪 单层残差量化演示:")
rq_1layer = ResidualQuantization(dim=128, nbits=2, num_layers=1)
result_1layer = rq_1layer.demonstrate_compression(test_vectors)
#%%
print("\n🧪 多层残差量化演示:")
rq_2layer = ResidualQuantization(dim=128, nbits=2, num_layers=2)
result_2layer = rq_2layer.demonstrate_compression(test_vectors)

print("\n🧪 更多层残差量化演示:")
rq_3layer = ResidualQuantization(dim=128, nbits=2, num_layers=3)
result_3layer = rq_3layer.demonstrate_compression(test_vectors)
#%% md
## 5. 不同配置的效果对比
#%%
def compare_configurations():
    """对比不同配置的效果"""
    
    print("⚖️ 不同配置效果对比:")
    print("=" * 50)
    
    configs = [
        {'layers': 1, 'nbits': 2, 'name': '1层2位'},
        {'layers': 2, 'nbits': 2, 'name': '2层2位'},
        {'layers': 3, 'nbits': 2, 'name': '3层2位'},
        {'layers': 1, 'nbits': 4, 'name': '1层4位'},
        {'layers': 2, 'nbits': 1, 'name': '2层1位'},
    ]
    
    results = []
    
    for config in configs:
        rq = ResidualQuantization(dim=128, nbits=config['nbits'], num_layers=config['layers'])
        result = rq.demonstrate_compression(test_vectors)
        result['config'] = config['name']
        result['total_bits'] = config['layers'] * config['nbits']
        results.append(result)
        print("\n" + "-"*30)
    
    # 汇总对比
    print("\n📊 配置对比汇总:")
    print(f"{'配置':12} {'总位数':8} {'MSE':12} {'压缩比':8} {'空间节省':10}")
    print("-" * 60)
    
    for result in results:
        print(f"{result['config']:12} {result['total_bits']:8} {result['mse']:12.6f} {result['compression_ratio']:8.1f} {(1-1/result['compression_ratio'])*100:9.1f}%")
    
    return results

comparison_results = compare_configurations()
#%% md
## 6. ColBERTv2中的残差量化应用
#%%
def explain_colbert_application():
    """解释在ColBERTv2中的应用"""
    
    print("🎯 ColBERTv2中的残差量化应用:")
    print("=" * 50)
    
    print("📋 应用场景:")
    scenarios = [
        "文档token嵌入的压缩存储",
        "大规模索引的内存优化", 
        "快速相似度计算的预处理",
        "分布式系统中的网络传输优化"
    ]
    
    for scenario in scenarios:
        print(f"   • {scenario}")
    
    print("\n🔧 技术细节:")
    details = {
        "默认配置": "2 bits per token (4个质心)",
        "压缩比": "通常达到8-16x压缩",
        "精度损失": "检索性能下降<2%",
        "内存节省": "索引大小减少80-90%",
        "计算加速": "量化计算比浮点快2-4x"
    }
    
    for key, value in details.items():
        print(f"   {key}: {value}")
    
    print("\n💡 关键优势:")
    advantages = [
        "保持Late Interaction的精度优势",
        "大幅减少存储和内存需求",
        "支持更大规模的文档集合",
        "加速索引构建和搜索过程",
        "降低部署和运维成本"
    ]
    
    for adv in advantages:
        print(f"   ✅ {adv}")

explain_colbert_application()
#%% md
## 7. 实际代码示例：ColBERTv2风格的残差量化
#%%
class ColBERTResidualQuantizer:
    """模拟ColBERTv2的残差量化实现"""
    
    def __init__(self, dim=128, nbits=2):
        self.dim = dim
        self.nbits = nbits
        self.num_centroids = 2 ** nbits
        self.centroids = None
        
    def train_centroids(self, embeddings):
        """训练量化质心"""
        print(f"🎓 训练 {self.num_centroids} 个质心...")
        
        # 将所有token嵌入展平
        flat_embeddings = embeddings.reshape(-1, self.dim)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=self.num_centroids, random_state=42)
        kmeans.fit(flat_embeddings)
        
        self.centroids = torch.from_numpy(kmeans.cluster_centers_).float()
        print(f"✅ 质心训练完成，形状: {self.centroids.shape}")
        
    def compress_embeddings(self, embeddings):
        """压缩嵌入向量"""
        batch_size, seq_len, dim = embeddings.shape
        
        # 展平处理
        flat_embeddings = embeddings.reshape(-1, dim)
        
        # 计算到各质心的距离
        distances = torch.cdist(flat_embeddings, self.centroids)
        
        # 找到最近的质心索引
        codes = torch.argmin(distances, dim=1)
        
        # 计算残差
        assigned_centroids = self.centroids[codes]
        residuals = flat_embeddings - assigned_centroids
        
        # 重新整形
        codes = codes.reshape(batch_size, seq_len)
        residuals = residuals.reshape(batch_size, seq_len, dim)
        
        return codes, residuals
    
    def decompress_embeddings(self, codes, residuals=None):
        """解压缩嵌入向量"""
        batch_size, seq_len = codes.shape
        
        # 根据codes获取质心
        reconstructed = self.centroids[codes.flatten()]
        reconstructed = reconstructed.reshape(batch_size, seq_len, self.dim)
        
        # 如果有残差，加上残差
        if residuals is not None:
            reconstructed += residuals
            
        return reconstructed
    
    def demonstrate_colbert_compression(self, doc_embeddings):
        """演示ColBERT风格的压缩"""
        print(f"\n🔍 ColBERT风格压缩演示:")
        print(f"   输入形状: {doc_embeddings.shape}")
        
        # 训练质心
        self.train_centroids(doc_embeddings)
        
        # 压缩
        codes, residuals = self.compress_embeddings(doc_embeddings)
        
        # 解压缩（仅使用质心）
        reconstructed_quantized = self.decompress_embeddings(codes)
        
        # 解压缩（质心+残差）
        reconstructed_full = self.decompress_embeddings(codes, residuals)
        
        # 计算误差
        mse_quantized = torch.mean((doc_embeddings - reconstructed_quantized) ** 2)
        mse_full = torch.mean((doc_embeddings - reconstructed_full) ** 2)
        
        print(f"\n📊 压缩结果:")
        print(f"   仅质心MSE: {mse_quantized:.6f}")
        print(f"   质心+残差MSE: {mse_full:.6f}")
        
        # 存储分析
        original_size = doc_embeddings.numel() * 4  # float32
        codes_size = codes.numel() * self.nbits / 8
        centroids_size = self.centroids.numel() * 4
        
        print(f"\n💾 存储分析:")
        print(f"   原始大小: {original_size/1024:.1f} KB")
        print(f"   编码大小: {codes_size/1024:.1f} KB")
        print(f"   质心大小: {centroids_size/1024:.1f} KB")
        print(f"   总压缩大小: {(codes_size + centroids_size)/1024:.1f} KB")
        print(f"   压缩比: {original_size/(codes_size + centroids_size):.1f}x")
        
        return codes, residuals, reconstructed_quantized

# 模拟ColBERT文档嵌入
torch.manual_seed(42)
doc_embeddings = torch.randn(50, 32, 128)  # 50个文档，每个32个token，128维

# 演示压缩
colbert_quantizer = ColBERTResidualQuantizer(dim=128, nbits=2)
codes, residuals, reconstructed = colbert_quantizer.demonstrate_colbert_compression(doc_embeddings)
#%% md
## 8. 总结与要点
#%%
def summarize_residual_quantization():
    """总结残差量化的要点"""
    
    print("🎯 残差量化核心要点总结:")
    print("=" * 50)
    
    key_points = {
        "🧠 核心思想": [
            "分层逼近：逐层量化残差误差",
            "质心编码：用有限质心代表连续向量",
            "误差累积：每层都在减少总体误差"
        ],
        "⚙️ 技术实现": [
            "K-means聚类训练质心码本",
            "最近邻搜索进行编码",
            "码本查找进行解码重构"
        ],
        "📈 性能优势": [
            "存储压缩：8-16x空间节省",
            "计算加速：整数运算更快",
            "精度保持：误差控制在可接受范围"
        ],
        "🎛️ 参数调节": [
            "nbits：控制每层质心数量(2^nbits)",
            "layers：控制量化层数",
            "dim：向量维度"
        ],
        "🚀 ColBERT应用": [
            "文档token嵌入压缩",
            "大规模索引优化",
            "内存和存储节省",
            "检索速度提升"
        ]
    }
    
    for category, points in key_points.items():
        print(f"\n{category}:")
        for point in points:
            print(f"   • {point}")
    
    print("\n🔮 学习建议:")
    suggestions = [
        "理解向量量化的基础概念",
        "掌握K-means聚类算法",
        "实践不同参数配置的效果",
        "了解在实际系统中的应用",
        "关注精度与压缩比的平衡"
    ]
    
    for suggestion in suggestions:
        print(f"   📚 {suggestion}")

summarize_residual_quantization()

print("\n" + "="*60)
print("🎓 残差量化学习完成！")
print("💡 现在你应该理解了ColBERTv2如何通过残差量化实现高效压缩")
print("="*60)