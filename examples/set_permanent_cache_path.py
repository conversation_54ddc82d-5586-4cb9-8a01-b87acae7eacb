"""
演示如何永久设置Hugging Face模型缓存路径的示例
"""

import os
import sys
from pathlib import Path

def method1_environment_variables():
    """
    方法1：通过环境变量设置（推荐）
    """
    print("=== 方法1：环境变量设置 ===")
    
    # 设置自定义缓存路径
    custom_cache_path = "/Users/<USER>/Documents/Code/models_cache"
    
    # 确保目录存在
    Path(custom_cache_path).mkdir(parents=True, exist_ok=True)
    
    # 设置环境变量
    os.environ["TRANSFORMERS_CACHE"] = custom_cache_path
    os.environ["HF_HOME"] = custom_cache_path
    os.environ["HF_HUB_CACHE"] = custom_cache_path
    
    print(f"已设置缓存路径为: {custom_cache_path}")
    
    # 验证设置
    from transformers import TRANSFORMERS_CACHE
    print(f"TRANSFORMERS_CACHE: {TRANSFORMERS_CACHE}")

def method2_config_file():
    """
    方法2：创建配置文件
    """
    print("\n=== 方法2：配置文件方式 ===")
    
    # 在项目中创建配置文件
    config_content = '''
# 在你的 ~/.bashrc 或 ~/.zshrc 中添加以下行：
export TRANSFORMERS_CACHE="/your/custom/path"
export HF_HOME="/your/custom/path"
export HF_HUB_CACHE="/your/custom/path"

# 然后执行：
# source ~/.bashrc  # 或 source ~/.zshrc
'''
    
    print("配置文件内容:")
    print(config_content)

def method3_programmatic_setup():
    """
    方法3：程序化设置
    """
    print("\n=== 方法3：程序化设置 ===")
    
    # 在导入transformers之前设置
    custom_path = "/Users/<USER>/Documents/Code/models_cache"
    
    # 设置环境变量
    os.environ["TRANSFORMERS_CACHE"] = custom_path
    
    # 现在导入transformers
    from transformers import DPRQuestionEncoder
    
    print(f"程序化设置缓存路径: {custom_path}")
    
    # 测试加载模型（会使用新的缓存路径）
    print("正在测试模型加载...")
    try:
        # 这里只是演示，实际使用时取消注释
        # model = DPRQuestionEncoder.from_pretrained(
        #     "facebook/dpr-question_encoder-single-nq-base"
        # )
        print("模型将会下载到指定的缓存目录")
    except Exception as e:
        print(f"模型加载测试: {e}")

def check_current_cache_location():
    """
    检查当前缓存位置
    """
    print("\n=== 当前缓存位置检查 ===")
    
    # 检查环境变量
    cache_vars = [
        "TRANSFORMERS_CACHE",
        "HF_HOME", 
        "HF_HUB_CACHE",
        "XDG_CACHE_HOME"
    ]
    
    for var in cache_vars:
        value = os.environ.get(var)
        print(f"{var}: {value}")
    
    # 检查默认路径
    try:
        from transformers import TRANSFORMERS_CACHE
        print(f"\n实际使用的缓存路径: {TRANSFORMERS_CACHE}")
    except ImportError:
        print("transformers库未安装")

def create_cache_setup_script():
    """
    创建缓存设置脚本
    """
    print("\n=== 创建设置脚本 ===")
    
    script_content = '''#!/bin/bash
# 设置Hugging Face模型缓存路径的脚本

# 设置自定义缓存路径
CUSTOM_CACHE_PATH="/Users/<USER>/Documents/Code/models_cache"

# 创建目录
mkdir -p "$CUSTOM_CACHE_PATH"

# 添加到bash配置文件
echo "export TRANSFORMERS_CACHE=$CUSTOM_CACHE_PATH" >> ~/.bashrc
echo "export HF_HOME=$CUSTOM_CACHE_PATH" >> ~/.bashrc
echo "export HF_HUB_CACHE=$CUSTOM_CACHE_PATH" >> ~/.bashrc

# 重新加载配置
source ~/.bashrc

echo "缓存路径已设置为: $CUSTOM_CACHE_PATH"
'''
    
    # 保存脚本
    script_path = "setup_cache_path.sh"
    with open(script_path, 'w') as f:
        f.write(script_content)
    
    # 设置执行权限
    os.chmod(script_path, 0o755)
    
    print(f"已创建设置脚本: {script_path}")
    print("运行方式: ./setup_cache_path.sh")

if __name__ == "__main__":
    print("Hugging Face 模型缓存路径永久设置示例\n")
    
    # 检查当前状态
    check_current_cache_location()
    
    # 演示不同方法
    method1_environment_variables()
    method2_config_file()
    method3_programmatic_setup()
    
    # 创建设置脚本
    create_cache_setup_script()
    
    print("\n=== 推荐方案 ===")
    print("1. 使用环境变量方式（方法1）最为稳定")
    print("2. 在项目开始时导入配置模块进行设置")
    print("3. 确保在导入transformers之前设置环境变量")
