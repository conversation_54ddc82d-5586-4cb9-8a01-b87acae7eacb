#!/usr/bin/env python3
"""
修复ColBERT在Python 3.13中的dataclass兼容性问题
"""

import os
import re
from pathlib import Path

def fix_settings_file():
    """修复settings.py文件"""
    settings_file = Path("src/ColBERT/colbert/infra/config/settings.py")
    
    if not settings_file.exists():
        print(f"❌ 文件不存在: {settings_file}")
        return False
    
    print(f"🔧 修复文件: {settings_file}")
    
    # 读取原文件
    with open(settings_file, 'r') as f:
        content = f.read()
    
    # 备份原文件
    backup_file = settings_file.with_suffix('.py.backup')
    with open(backup_file, 'w') as f:
        f.write(content)
    print(f"✅ 已备份到: {backup_file}")
    
    # 修复dataclass字段定义
    # 将 field_name: type = DefaultVal(value) 改为使用 field(default_factory=...)
    
    # 添加必要的导入
    if "from dataclasses import dataclass" in content and "field" not in content:
        content = content.replace(
            "from dataclasses import dataclass",
            "from dataclasses import dataclass, field"
        )
    
    # 修复所有使用DefaultVal的字段
    patterns = [
        (r'(\s+)(\w+):\s*([^=]+)\s*=\s*DefaultVal\(([^)]+)\)', 
         r'\1\2: \3 = field(default_factory=lambda: \4)'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    # 写入修复后的文件
    with open(settings_file, 'w') as f:
        f.write(content)
    
    print(f"✅ 已修复: {settings_file}")
    return True

def fix_core_config_file():
    """修复core_config.py文件"""
    core_config_file = Path("src/ColBERT/colbert/infra/config/core_config.py")
    
    if not core_config_file.exists():
        print(f"❌ 文件不存在: {core_config_file}")
        return False
    
    print(f"🔧 修复文件: {core_config_file}")
    
    # 读取原文件
    with open(core_config_file, 'r') as f:
        content = f.read()
    
    # 备份原文件
    backup_file = core_config_file.with_suffix('.py.backup')
    with open(backup_file, 'w') as f:
        f.write(content)
    print(f"✅ 已备份到: {backup_file}")
    
    # 修复DefaultVal类，使其不可变
    old_defaultval = '''@dataclass
class DefaultVal:
    val: Any'''
    
    new_defaultval = '''@dataclass(frozen=True)
class DefaultVal:
    val: Any'''
    
    content = content.replace(old_defaultval, new_defaultval)
    
    # 写入修复后的文件
    with open(core_config_file, 'w') as f:
        f.write(content)
    
    print(f"✅ 已修复: {core_config_file}")
    return True

def test_import():
    """测试修复后的导入"""
    print("\n🧪 测试ColBERT导入...")
    
    import sys
    sys.path.insert(0, "src")
    sys.path.insert(0, "src/ColBERT")
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer
        print("✅ ColBERT导入成功！")
        return True
    except Exception as e:
        print(f"❌ ColBERT导入失败: {e}")
        return False

def restore_backups():
    """恢复备份文件"""
    print("\n🔄 恢复备份文件...")
    
    backup_files = [
        "src/ColBERT/colbert/infra/config/settings.py.backup",
        "src/ColBERT/colbert/infra/config/core_config.py.backup"
    ]
    
    for backup_file in backup_files:
        backup_path = Path(backup_file)
        if backup_path.exists():
            original_path = backup_path.with_suffix('')
            with open(backup_path, 'r') as f:
                content = f.read()
            with open(original_path, 'w') as f:
                f.write(content)
            print(f"✅ 已恢复: {original_path}")

def main():
    """主函数"""
    print("🔧 修复ColBERT Python 3.13兼容性问题")
    print("=" * 50)
    
    try:
        # 方法1: 修复dataclass字段定义
        print("方法1: 修复dataclass字段定义")
        success1 = fix_settings_file()
        success2 = fix_core_config_file()
        
        if success1 and success2:
            if test_import():
                print("\n🎉 修复成功！ColBERT现在可以正常导入了。")
                return True
            else:
                print("\n⚠️  修复后仍有问题，尝试其他方法...")
        
        # 如果方法1失败，恢复备份并尝试其他方法
        restore_backups()
        
        # 方法2: 创建一个简单的运行脚本，降级Python版本检查
        print("\n方法2: 创建兼容性运行脚本")
        
        compat_script = '''#!/usr/bin/env python3
"""
ColBERT兼容性运行脚本
"""
import sys
import os
from pathlib import Path

# 添加路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir / "src"))
sys.path.insert(0, str(current_dir / "src" / "ColBERT"))

# 设置环境变量
os.environ['PYTHONPATH'] = f"{current_dir}/src:{current_dir}/src/ColBERT"
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

# 临时禁用dataclass的严格检查（如果可能）
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 尝试monkey patch dataclass
try:
    import dataclasses
    original_dataclass = dataclasses.dataclass
    
    def patched_dataclass(*args, **kwargs):
        # 移除可能导致问题的参数
        kwargs.pop('slots', None)
        kwargs.pop('weakref_slot', None)
        return original_dataclass(*args, **kwargs)
    
    dataclasses.dataclass = patched_dataclass
except:
    pass

if __name__ == "__main__":
    # 运行主程序
    sys.argv[0] = str(current_dir / "src" / "main.py")
    exec(open(current_dir / "src" / "main.py").read())
'''
        
        with open("run_compatible.py", "w") as f:
            f.write(compat_script)
        os.chmod("run_compatible.py", 0o755)
        
        print("✅ 创建兼容性脚本: run_compatible.py")
        print("\n💡 使用方法:")
        print("  python run_compatible.py --help")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        print("\n💡 手动解决方案:")
        print("1. 降级Python版本到3.11或3.12")
        print("2. 或者使用conda创建Python 3.11环境:")
        print("   conda create -n tableqa python=3.11")
        print("   conda activate tableqa")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户中断")
    except Exception as e:
        print(f"\n💥 出现异常: {e}")
        import traceback
        traceback.print_exc()
