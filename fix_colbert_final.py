#!/usr/bin/env python3
"""
最终解决ColBERT导入问题的脚本
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """运行命令"""
    print(f"🔧 执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, check=check, 
                              capture_output=True, text=True)
        if result.stdout.strip():
            print(f"输出: {result.stdout.strip()}")
        if result.stderr.strip() and result.returncode != 0:
            print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def check_environment():
    """检查环境"""
    print("🔍 检查环境...")
    
    # 检查项目结构
    required_paths = [
        "src/main.py",
        "src/ColBERT",
        "src/data_loader_manager"
    ]
    
    for path in required_paths:
        if not Path(path).exists():
            print(f"❌ 缺少必要文件/目录: {path}")
            return False
        print(f"✅ 找到: {path}")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装依赖...")
    
    # 基础依赖
    basic_deps = [
        "gitpython",
        "transformers==4.22.1", 
        "datasets==2.6.1",
        "jsonnet",
        "easydict",
        "tqdm",
        "pytorch-lightning==1.8.2"
    ]
    
    for dep in basic_deps:
        print(f"安装 {dep}...")
        if not run_command(f"pip install {dep}", check=False):
            print(f"⚠️  {dep} 安装可能失败，继续...")

def fix_colbert_installation():
    """修复ColBERT安装"""
    print("\n🔧 修复ColBERT安装...")
    
    colbert_path = Path("src/ColBERT")
    
    # 方法1: 重新安装ColBERT
    print("方法1: 重新安装ColBERT...")
    success = run_command("pip uninstall colbert -y", check=False)
    success = run_command("pip install -e .", cwd=colbert_path, check=False)
    
    if success:
        print("✅ ColBERT重新安装成功")
    else:
        print("⚠️  ColBERT重新安装失败，尝试其他方法...")
    
    # 方法2: 直接复制ColBERT到site-packages
    print("方法2: 直接复制ColBERT模块...")
    try:
        import site
        site_packages = site.getsitepackages()[0]
        target_path = Path(site_packages) / "colbert"
        source_path = colbert_path / "colbert"
        
        if target_path.exists():
            shutil.rmtree(target_path)
        
        shutil.copytree(source_path, target_path)
        print(f"✅ ColBERT模块复制到: {target_path}")
        
    except Exception as e:
        print(f"❌ 复制失败: {e}")
    
    # 方法3: 创建.pth文件
    print("方法3: 创建.pth文件...")
    try:
        import site
        site_packages = site.getsitepackages()[0]
        pth_file = Path(site_packages) / "colbert_path.pth"
        
        with open(pth_file, 'w') as f:
            f.write(str(colbert_path.absolute()) + '\n')
        
        print(f"✅ 创建.pth文件: {pth_file}")
        
    except Exception as e:
        print(f"❌ 创建.pth文件失败: {e}")

def test_colbert_import():
    """测试ColBERT导入"""
    print("\n🧪 测试ColBERT导入...")
    
    # 添加路径到sys.path
    current_dir = Path.cwd()
    paths_to_add = [
        str(current_dir / "src"),
        str(current_dir / "src" / "ColBERT")
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer, DocTokenizer, tensorize_triples
        print("✅ ColBERT tokenization导入成功")
        return True
    except ImportError as e:
        print(f"❌ ColBERT导入失败: {e}")
        return False

def test_project_import():
    """测试项目导入"""
    print("\n📦 测试项目导入...")
    
    try:
        # 先测试data_loader_manager
        import data_loader_manager
        print("✅ data_loader_manager导入成功")
        return True
    except ImportError as e:
        print(f"❌ data_loader_manager导入失败: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("\n📝 创建启动脚本...")
    
    # Python启动脚本
    python_script = '''#!/usr/bin/env python3
"""
Robust TableQA 启动脚本
"""
import sys
import os
from pathlib import Path

# 添加必要路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir / "src"))
sys.path.insert(0, str(current_dir / "src" / "ColBERT"))

# 设置环境变量
os.environ['PYTHONPATH'] = f"{current_dir}/src:{current_dir}/src/ColBERT"
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

# 导入并运行主程序
if __name__ == "__main__":
    sys.argv[0] = str(current_dir / "src" / "main.py")
    exec(open(current_dir / "src" / "main.py").read())
'''
    
    # Bash启动脚本
    bash_script = '''#!/bin/bash
# Robust TableQA 启动脚本

# 设置环境变量
export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"
export PYTORCH_ENABLE_MPS_FALLBACK=1

# 运行项目
python src/main.py "$@"
'''
    
    try:
        # 创建Python启动脚本
        with open("run_tableqa.py", "w") as f:
            f.write(python_script)
        os.chmod("run_tableqa.py", 0o755)
        print("✅ 创建Python启动脚本: run_tableqa.py")
        
        # 创建Bash启动脚本
        with open("run_tableqa.sh", "w") as f:
            f.write(bash_script)
        os.chmod("run_tableqa.sh", 0o755)
        print("✅ 创建Bash启动脚本: run_tableqa.sh")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {e}")
        return False

def final_test():
    """最终测试"""
    print("\n🎯 最终测试...")
    
    # 测试Python启动脚本
    print("测试Python启动脚本...")
    result = run_command("python run_tableqa.py --help", check=False)
    
    if result:
        print("✅ Python启动脚本测试成功")
        return True
    else:
        print("❌ Python启动脚本测试失败")
        
        # 测试Bash启动脚本
        print("测试Bash启动脚本...")
        result = run_command("./run_tableqa.sh --help", check=False)
        
        if result:
            print("✅ Bash启动脚本测试成功")
            return True
        else:
            print("❌ 所有启动脚本测试失败")
            return False

def main():
    """主函数"""
    print("🚀 最终解决ColBERT导入问题")
    print("=" * 60)
    
    # 1. 检查环境
    if not check_environment():
        print("❌ 环境检查失败")
        return False
    
    # 2. 安装依赖
    install_dependencies()
    
    # 3. 修复ColBERT安装
    fix_colbert_installation()
    
    # 4. 测试ColBERT导入
    if not test_colbert_import():
        print("❌ ColBERT导入测试失败")
        # 继续，可能通过启动脚本能解决
    
    # 5. 测试项目导入
    if not test_project_import():
        print("⚠️  项目导入测试失败，但启动脚本可能能解决")
    
    # 6. 创建启动脚本
    if not create_startup_script():
        print("❌ 创建启动脚本失败")
        return False
    
    # 7. 最终测试
    if final_test():
        print("\n🎉 修复完成！")
        print("\n✅ 使用方法:")
        print("  方法1: python run_tableqa.py [参数]")
        print("  方法2: ./run_tableqa.sh [参数]")
        print("  方法3: 手动设置环境变量后运行 python src/main.py [参数]")
        
        print("\n💡 环境变量设置:")
        print('  export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"')
        
        return True
    else:
        print("\n❌ 最终测试失败")
        print("请尝试手动设置环境变量:")
        print('export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"')
        print("然后运行: python src/main.py --help")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 修复未完全成功")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户中断")
    except Exception as e:
        print(f"\n💥 出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
