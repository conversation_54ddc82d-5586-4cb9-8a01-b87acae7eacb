# 解决 ColBERT 导入错误

## 问题
```
ModuleNotFoundError: No module named 'colbert'
```

## 快速解决方案

### 方案1: 安装ColBERT模块（推荐）

```bash
# 进入ColBERT目录
cd src/ColBERT

# 安装ColBERT
pip install -e .

# 返回项目根目录
cd ../..
```

### 方案2: 设置Python路径

```bash
# 在项目根目录执行
export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"

# 然后运行项目
python src/main.py
```

### 方案3: 使用自动化脚本

```bash
# 运行自动化设置脚本
python setup_robust_tableqa_m4.py
```

## 完整的Mac M4环境设置

### 1. 基础依赖安装

```bash
# 安装PyTorch (M4优化)
pip install torch torchvision torchaudio

# 安装PyTorch Lightning (项目指定版本)
pip install pytorch-lightning==1.8.2

# 安装其他核心依赖
pip install transformers==4.22.1 datasets==2.6.1 jsonnet easydict tqdm
```

### 2. 安装ColBERT

```bash
cd src/ColBERT
pip install -e .
cd ../..
```

### 3. 安装其他依赖

```bash
# 安装剩余依赖
pip install tfrecord frozendict wandb==0.13.4
pip install faiss-cpu  # M4上使用CPU版本
pip install bitarray spacy ujson gitpython ninja
pip install absl-py tensorboard scipy scikit-learn

# 尝试安装torch-scatter (可能需要特殊处理)
pip install torch-scatter
```

### 4. 验证安装

创建测试文件 `test_imports.py`:

```python
#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加路径
sys.path.insert(0, str(Path.cwd() / "src"))
sys.path.insert(0, str(Path.cwd() / "src" / "ColBERT"))

def test_imports():
    print("🧪 测试导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    try:
        import pytorch_lightning as pl
        print(f"✅ Lightning: {pl.__version__}")
    except ImportError as e:
        print(f"❌ Lightning: {e}")
        return False
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer
        print("✅ ColBERT导入成功")
    except ImportError as e:
        print(f"❌ ColBERT: {e}")
        return False
    
    try:
        from data_loader_manager import *
        print("✅ 项目模块导入成功")
    except ImportError as e:
        print(f"❌ 项目模块: {e}")
        return False
    
    print("🎉 所有导入测试通过！")
    return True

if __name__ == "__main__":
    test_imports()
```

运行测试:
```bash
python test_imports.py
```

### 5. 创建运行脚本

创建 `run.sh`:

```bash
#!/bin/bash
# 设置环境变量
export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"
export PYTORCH_ENABLE_MPS_FALLBACK=1

# 运行项目
python src/main.py "$@"
```

使用方法:
```bash
chmod +x run.sh
./run.sh [你的参数]
```

## 常见问题解决

### 1. torch-scatter安装失败

```bash
# 方案1: 使用CPU版本
pip install torch-scatter -f https://data.pyg.org/whl/torch-1.10.0+cpu.html

# 方案2: 跳过torch-scatter，项目可能仍可运行
# 如果遇到相关错误再处理
```

### 2. faiss安装问题

```bash
# M4上使用CPU版本
pip install faiss-cpu

# 避免使用faiss-gpu (M4不支持CUDA)
```

### 3. 版本兼容性问题

如果遇到版本冲突，使用项目推荐的版本:

```bash
# 卸载现有版本
pip uninstall pytorch-lightning torch

# 安装项目兼容版本
pip install torch==1.10.1 torchvision==0.11.2
pip install pytorch-lightning==1.8.2
```

### 4. MPS相关问题

```bash
# 设置MPS回退
export PYTORCH_ENABLE_MPS_FALLBACK=1

# 或在代码中设置
import os
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
```

## 验证成功标志

如果设置成功，您应该能够：

1. ✅ 成功导入colbert模块
2. ✅ 成功导入项目的data_loader_manager
3. ✅ 运行 `python src/main.py --help` 不报错
4. ✅ 看到项目的帮助信息

## 下一步

环境设置成功后，您可以：

1. 运行项目的示例配置
2. 测试训练流程
3. 准备部署到服务器

记住在服务器上使用相应的GPU版本依赖！
