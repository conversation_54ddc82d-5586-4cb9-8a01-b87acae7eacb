#!/usr/bin/env python3
"""
修复缺失的依赖
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    print(f"📦 安装 {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def test_import(module_name, package_name=None):
    """测试模块导入"""
    if package_name is None:
        package_name = module_name
    
    try:
        __import__(module_name)
        print(f"✅ {package_name} 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {package_name} 导入失败: {e}")
        return False

def main():
    print("🔧 修复缺失的依赖")
    print("=" * 30)
    
    # 需要安装的依赖列表
    dependencies = [
        ("git", "gitpython"),  # (模块名, 包名)
        ("transformers", "transformers"),
        ("datasets", "datasets"),
        ("jsonnet", "jsonnet"),
        ("easydict", "easydict"),
        ("wandb", "wandb"),
        ("tensorboard", "tensorboard"),
        ("scipy", "scipy"),
        ("sklearn", "scikit-learn"),
        ("spacy", "spacy"),
        ("ujson", "ujson"),
        ("bitarray", "bitarray"),
        ("ninja", "ninja"),
        ("absl", "absl-py"),
    ]
    
    missing_deps = []
    
    # 检查哪些依赖缺失
    print("🔍 检查依赖...")
    for module_name, package_name in dependencies:
        if not test_import(module_name, package_name):
            missing_deps.append(package_name)
    
    if not missing_deps:
        print("\n🎉 所有依赖都已安装！")
        return True
    
    # 安装缺失的依赖
    print(f"\n📦 需要安装 {len(missing_deps)} 个依赖...")
    failed_installs = []
    
    for package in missing_deps:
        if not install_package(package):
            failed_installs.append(package)
    
    # 测试ColBERT导入
    print("\n🧪 测试ColBERT导入...")
    sys.path.insert(0, "src")
    sys.path.insert(0, "src/ColBERT")
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer
        print("✅ ColBERT导入成功")
        
        # 测试项目导入
        try:
            import data_loader_manager
            print("✅ 项目模块导入成功")
        except ImportError as e:
            print(f"⚠️  项目模块导入失败: {e}")
            print("这可能是正常的，如果还有其他依赖未安装")
        
        print("\n🎉 修复完成！")
        print("现在可以运行: python src/main.py --help")
        return True
        
    except ImportError as e:
        print(f"❌ ColBERT导入仍然失败: {e}")
        
        if failed_installs:
            print(f"\n⚠️  以下依赖安装失败: {', '.join(failed_installs)}")
            print("请手动安装:")
            for pkg in failed_installs:
                print(f"  pip install {pkg}")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 修复未完全成功，请检查上述错误")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户中断")
    except Exception as e:
        print(f"\n💥 出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
