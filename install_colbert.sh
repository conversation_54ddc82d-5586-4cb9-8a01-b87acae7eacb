#!/bin/bash

echo "🚀 快速安装ColBERT模块"
echo "======================"

# 检查是否在正确目录
if [ ! -f "src/main.py" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    echo "当前目录应包含 src/main.py 文件"
    exit 1
fi

# 检查ColBERT目录
if [ ! -d "src/ColBERT" ]; then
    echo "❌ ColBERT目录不存在: src/ColBERT"
    exit 1
fi

echo "📁 找到ColBERT目录"

# 进入ColBERT目录
cd src/ColBERT

echo "📦 安装必要依赖..."
pip install gitpython

echo "📦 安装ColBERT模块..."
pip install -e .

if [ $? -eq 0 ]; then
    echo "✅ ColBERT安装成功"
else
    echo "❌ ColBERT安装失败"
    exit 1
fi

# 返回项目根目录
cd ../..

echo "🧪 测试导入..."

# 测试导入
python3 -c "
import sys
sys.path.insert(0, 'src')
sys.path.insert(0, 'src/ColBERT')

try:
    from colbert.modeling.tokenization import QueryTokenizer
    print('✅ ColBERT导入成功')
except ImportError as e:
    print(f'❌ ColBERT导入失败: {e}')
    sys.exit(1)

try:
    import data_loader_manager
    print('✅ 项目模块导入成功')
except ImportError as e:
    print(f'❌ 项目模块导入失败: {e}')
    print('这可能是正常的，如果其他依赖未安装')
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 安装完成！"
    echo ""
    echo "现在可以运行项目了:"
    echo "  python src/main.py --help"
    echo ""
    echo "或者设置环境变量:"
    echo "  export PYTHONPATH=\"\$PYTHONPATH:\$(pwd)/src:\$(pwd)/src/ColBERT\""
    echo "  python src/main.py --help"
else
    echo "❌ 测试失败"
    exit 1
fi
