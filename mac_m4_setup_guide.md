# Mac M4 芯片 PyTorch Lightning 设置指南

## 🍎 Mac M4 专用配置

### 1. 环境准备

```bash
# 检查芯片类型
system_profiler SPHardwareDataType | grep "Chip"

# 确保使用最新的Homebrew (如果没有安装)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Python (推荐使用Homebrew版本，对M4优化更好)
brew install python@3.11
```

### 2. 创建虚拟环境

```bash
# 创建项目目录
mkdir pytorch_lightning_test
cd pytorch_lightning_test

# 创建虚拟环境
python3.11 -m venv venv_m4
source venv_m4/bin/activate

# 升级pip
pip install --upgrade pip
```

### 3. 安装PyTorch (M4优化版本)

```bash
# 安装支持Apple Silicon的PyTorch
pip install torch torchvision torchaudio

# 或者指定版本
pip install torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0

# 安装PyTorch Lightning
pip install pytorch-lightning

# 安装其他必要包
pip install torchmetrics matplotlib jupyter notebook
```

### 4. 验证MPS (Metal Performance Shaders) 支持

创建测试脚本验证GPU加速：

```python
import torch
import pytorch_lightning as pl

def test_mps_support():
    print("🔍 Mac M4 PyTorch 配置检查")
    print("=" * 40)
    
    # 基本信息
    print(f"PyTorch版本: {torch.__version__}")
    print(f"PyTorch Lightning版本: {pl.__version__}")
    
    # MPS支持检查
    if torch.backends.mps.is_available():
        print("✅ MPS (Metal Performance Shaders) 可用")
        print("✅ 可以使用GPU加速")
        
        # 测试MPS设备
        device = torch.device("mps")
        x = torch.randn(1000, 1000, device=device)
        y = torch.randn(1000, 1000, device=device)
        z = torch.mm(x, y)
        print(f"✅ MPS计算测试成功，结果形状: {z.shape}")
        
    else:
        print("❌ MPS不可用，将使用CPU")
    
    # CPU信息
    print(f"CPU核心数: {torch.get_num_threads()}")
    
    return torch.backends.mps.is_available()

if __name__ == "__main__":
    test_mps_support()
```

### 5. M4优化的Lightning模型示例

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import DataLoader, TensorDataset

class M4OptimizedModel(pl.LightningModule):
    def __init__(self, input_size=784, hidden_size=128, num_classes=10):
        super().__init__()
        self.save_hyperparameters()
        
        # 网络结构
        self.layers = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, num_classes)
        )
        
        # 用于计算准确率
        from torchmetrics import Accuracy
        self.train_acc = Accuracy(task='multiclass', num_classes=num_classes)
        self.val_acc = Accuracy(task='multiclass', num_classes=num_classes)
    
    def forward(self, x):
        return self.layers(x.view(x.size(0), -1))
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        
        # 计算准确率
        preds = torch.argmax(y_hat, dim=1)
        self.train_acc(preds, y)
        
        # 记录指标
        self.log('train_loss', loss, prog_bar=True)
        self.log('train_acc', self.train_acc, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        
        preds = torch.argmax(y_hat, dim=1)
        self.val_acc(preds, y)
        
        self.log('val_loss', loss, prog_bar=True)
        self.log('val_acc', self.val_acc, prog_bar=True)
        
        return loss
    
    def configure_optimizers(self):
        # Adam优化器在M4上表现很好
        optimizer = torch.optim.Adam(self.parameters(), lr=1e-3)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
        return [optimizer], [scheduler]

def create_dummy_data(num_samples=1000, input_size=784, num_classes=10):
    """创建测试数据"""
    X = torch.randn(num_samples, input_size)
    y = torch.randint(0, num_classes, (num_samples,))
    return TensorDataset(X, y)

def test_m4_training():
    """测试M4上的训练"""
    print("🚀 开始M4训练测试...")
    
    # 创建数据
    train_dataset = create_dummy_data(1000)
    val_dataset = create_dummy_data(200)
    
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=64)
    
    # 创建模型
    model = M4OptimizedModel()
    
    # 配置训练器 - 针对M4优化
    trainer = pl.Trainer(
        max_epochs=3,
        accelerator="mps" if torch.backends.mps.is_available() else "cpu",
        devices=1,
        enable_progress_bar=True,
        enable_model_summary=True,
        log_every_n_steps=10,
    )
    
    print(f"使用设备: {trainer.accelerator}")
    
    # 开始训练
    trainer.fit(model, train_loader, val_loader)
    
    print("✅ M4训练测试完成！")
    
    return model, trainer

if __name__ == "__main__":
    # 运行测试
    model, trainer = test_m4_training()
```

## 🧪 快速测试脚本

创建一个简单的测试文件 `test_m4.py`：

```python
#!/usr/bin/env python3
import torch
import pytorch_lightning as pl

def quick_test():
    print("🍎 Mac M4 PyTorch Lightning 快速测试")
    print("=" * 50)
    
    # 版本信息
    print(f"PyTorch: {torch.__version__}")
    print(f"Lightning: {pl.__version__}")
    
    # MPS测试
    if torch.backends.mps.is_available():
        print("✅ MPS可用 - 将使用GPU加速")
        device = "mps"
    else:
        print("⚠️  MPS不可用 - 使用CPU")
        device = "cpu"
    
    # 简单计算测试
    x = torch.randn(100, 100, device=device)
    y = torch.mm(x, x.t())
    print(f"✅ 计算测试成功: {y.shape}")
    
    # Lightning模型测试
    class SimpleModel(pl.LightningModule):
        def __init__(self):
            super().__init__()
            self.layer = torch.nn.Linear(10, 1)
        
        def forward(self, x):
            return self.layer(x)
        
        def training_step(self, batch, batch_idx):
            return torch.tensor(0.0)
        
        def configure_optimizers(self):
            return torch.optim.Adam(self.parameters())
    
    model = SimpleModel()
    trainer = pl.Trainer(max_epochs=1, enable_checkpointing=False, logger=False)
    print("✅ Lightning模型创建成功")
    
    print("\n🎉 所有测试通过！您的M4环境配置正确。")

if __name__ == "__main__":
    quick_test()
```

## 📝 运行步骤

1. **设置环境**：
```bash
# 克隆或进入项目目录
cd /path/to/your/project

# 创建虚拟环境
python3 -m venv venv_m4
source venv_m4/bin/activate

# 安装依赖
pip install torch torchvision pytorch-lightning torchmetrics jupyter
```

2. **运行快速测试**：
```bash
python test_m4.py
```

3. **如果测试通过，运行完整示例**：
```bash
python m4_lightning_example.py
```

## ⚡ M4性能优化建议

1. **使用MPS加速**：确保在Trainer中设置 `accelerator="mps"`
2. **批次大小**：M4内存统一架构，可以使用较大的batch size
3. **数据加载**：设置 `num_workers=0` 或较小值，避免多进程开销
4. **混合精度**：可以尝试 `precision="16-mixed"` 提升性能

## 🐛 常见问题解决

如果遇到问题：

1. **MPS不可用**：
   - 确保macOS >= 12.3
   - 更新到最新的PyTorch版本

2. **内存错误**：
   - 减小batch_size
   - 使用梯度累积

3. **性能不佳**：
   - 确保使用MPS设备
   - 检查是否有不必要的CPU-GPU数据传输

运行测试脚本，如果一切正常，您就可以将代码上传到服务器了！
