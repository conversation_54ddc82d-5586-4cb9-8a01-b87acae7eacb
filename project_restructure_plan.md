# Robust TableQA 项目重构计划

## 目标
将混合的 ITR 和 LI-RAGE 实验分离到独立的文件夹中，使每个实验可以独立运行。

## 新的项目结构

```
robust-tableqa-main/
├── README.md                          # 主项目说明
├── requirements.txt                   # 共享依赖
├── setup_robust_tableqa_m4.py        # 共享安装脚本
├── 
├── shared/                            # 共享组件
│   ├── src/
│   │   ├── data_loader_manager/       # 共享数据加载器
│   │   │   ├── data_loader_wrapper.py
│   │   │   ├── module_parser.py
│   │   │   └── data_loader_for_tableqa.py
│   │   ├── utils/                     # 共享工具
│   │   └── trainers/
│   │       └── base_executor.py       # 基础执行器
│   └── configs/
│       └── base_env.jsonnet           # 基础配置
│
├── ITR_experiments/                   # ITR 实验 (ACL 2023 - Inner Table Retriever)
│   ├── README.md                      # ITR 实验说明
│   ├── run_itr_experiments.sh         # ITR 实验脚本
│   ├── src/
│   │   ├── main.py                    # ITR 主程序
│   │   ├── models/
│   │   │   └── rag/
│   │   │       ├── itr_rag.py         # ITR 模型
│   │   │       └── itr_rag_reduce.py  # ITR 缩减模型
│   │   ├── trainers/
│   │   │   ├── ITR_rag_executor.py    # ITR 执行器
│   │   │   └── ITR_DPR_executor.py    # ITR DPR 执行器
│   │   └── data_loader_manager/
│   │       └── datasets/
│   │           ├── WikiTQ_dataset.py  # ITR 数据集 (ITRWikiTQDataset)
│   │           ├── WikiSQL_dataset.py # ITR 数据集 (ITRWikiSQLDataset)
│   │           └── NQTables_dataset.py
│   ├── configs/                       # ITR 配置文件
│   │   ├── wtq/
│   │   │   ├── tapex_ITR_*.jsonnet
│   │   │   └── dpr_ITR_*.jsonnet
│   │   ├── wikisql/
│   │   │   ├── tapex_ITR_*.jsonnet
│   │   │   └── dpr_ITR_*.jsonnet
│   │   └── nq_tables/
│   │       └── dpr_ITR_*.jsonnet
│   └── experiments/                   # ITR 实验结果
│
├── LIRAGE_experiments/                # LI-RAGE 实验 (ACL 2023 - Late Interaction RAG)
│   ├── README.md                      # LI-RAGE 实验说明
│   ├── run_lirage_experiments.sh      # LI-RAGE 实验脚本
│   ├── src/
│   │   ├── main.py                    # LI-RAGE 主程序
│   │   ├── ColBERT/                   # ColBERT 模块 (完整迁移)
│   │   ├── models/
│   │   │   └── rag/
│   │   │       └── rag_model.py       # RAG 模型
│   │   ├── trainers/
│   │   │   ├── RAG_executor.py        # RAG 执行器
│   │   │   ├── ColBERT_executor.py    # ColBERT 执行器
│   │   │   └── DPR_executor.py        # DPR 执行器
│   │   └── data_loader_manager/
│   │       └── datasets/
│   │           ├── E2EWTQ_dataset.py  # RAG 数据集 (RAGE2EWTQDataset)
│   │           ├── NQTables_dataset.py # RAG 数据集
│   │           └── WikiTQ_dataset.py  # 标准数据集
│   ├── configs/                       # LI-RAGE 配置文件
│   │   ├── e2e_wtq/
│   │   │   ├── colbert.jsonnet
│   │   │   ├── colbert_rag.jsonnet
│   │   │   ├── dpr.jsonnet
│   │   │   └── rag.jsonnet
│   │   └── nq_tables/
│   │       ├── colbert.jsonnet
│   │       ├── colbert_rag.jsonnet
│   │       ├── dpr.jsonnet
│   │       └── rag.jsonnet
│   └── experiments/                   # LI-RAGE 实验结果
│
└── datasets/                          # 共享数据集
    ├── TableQA_data/
    ├── WikiTableQuestions/
    ├── WikiSQL/
    └── NQ-Tables/
```

## 分离策略

### 1. 共享组件 (shared/)
- 基础数据加载器和解析器
- 通用工具函数
- 基础执行器类
- 基础配置文件

### 2. ITR 实验独立化
- 所有 ITR 相关的模型、执行器、数据集
- ITR 特有的配置文件
- ITR 特有的预处理函数
- 独立的主程序和运行脚本

### 3. LI-RAGE 实验独立化  
- ColBERT 模块完整迁移
- RAG 相关的模型、执行器、数据集
- LI-RAGE 特有的配置文件
- 独立的主程序和运行脚本

### 4. 数据集共享
- 原始数据集保持共享
- 预处理后的数据按实验分类存储

## 优势

1. **清晰分离**: 每个实验有独立的代码空间
2. **独立运行**: 可以单独运行某个实验而不受另一个影响
3. **易于维护**: 修改一个实验不会影响另一个
4. **便于理解**: 研究人员可以专注于感兴趣的实验
5. **减少依赖**: 每个实验只包含必要的组件
