#!/usr/bin/env python3
"""
快速修复ColBERT导入问题
"""

import sys
import os
import subprocess
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令"""
    print(f"🔧 执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print("❌ 失败")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def setup_paths():
    """设置Python路径"""
    current_dir = Path.cwd()
    src_dir = current_dir / "src"
    colbert_dir = src_dir / "ColBERT"
    
    if src_dir.exists():
        sys.path.insert(0, str(src_dir))
        print(f"✅ 添加路径: {src_dir}")
    
    if colbert_dir.exists():
        sys.path.insert(0, str(colbert_dir))
        print(f"✅ 添加路径: {colbert_dir}")
    
    return src_dir.exists() and colbert_dir.exists()

def install_colbert():
    """安装ColBERT"""
    print("\n📦 安装ColBERT...")
    
    colbert_path = Path("src/ColBERT")
    if not colbert_path.exists():
        print(f"❌ ColBERT路径不存在: {colbert_path}")
        return False
    
    # 安装ColBERT
    success = run_command("pip install -e .", cwd=colbert_path)
    return success

def test_basic_imports():
    """测试基础导入"""
    print("\n🧪 测试基础导入...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        return False
    
    try:
        import pytorch_lightning as pl
        print(f"✅ Lightning: {pl.__version__}")
    except ImportError as e:
        print(f"❌ Lightning: {e}")
        return False
    
    return True

def test_colbert():
    """测试ColBERT导入"""
    print("\n🔍 测试ColBERT...")
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer
        print("✅ ColBERT QueryTokenizer导入成功")
        return True
    except ImportError as e:
        print(f"❌ ColBERT导入失败: {e}")
        return False

def test_project():
    """测试项目导入"""
    print("\n📦 测试项目导入...")
    
    try:
        import data_loader_manager
        print("✅ data_loader_manager导入成功")
        return True
    except ImportError as e:
        print(f"❌ data_loader_manager导入失败: {e}")
        return False

def create_run_script():
    """创建运行脚本"""
    print("\n📝 创建运行脚本...")
    
    script_content = '''#!/bin/bash
# 设置环境变量
export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"
export PYTORCH_ENABLE_MPS_FALLBACK=1

# 运行项目
python src/main.py "$@"
'''
    
    try:
        with open("run.sh", "w") as f:
            f.write(script_content)
        os.chmod("run.sh", 0o755)
        print("✅ 创建运行脚本: run.sh")
        return True
    except Exception as e:
        print(f"❌ 创建脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速修复ColBERT导入问题")
    print("=" * 50)
    
    # 检查项目结构
    if not Path("src/main.py").exists():
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    # 1. 设置路径
    print("1️⃣ 设置Python路径...")
    if not setup_paths():
        print("❌ 路径设置失败")
        return False
    
    # 2. 测试基础导入
    print("\n2️⃣ 测试基础导入...")
    if not test_basic_imports():
        print("❌ 基础导入失败，请先安装PyTorch和Lightning")
        print("运行: pip install torch pytorch-lightning==1.8.2")
        return False
    
    # 3. 安装ColBERT
    print("\n3️⃣ 安装ColBERT...")
    if not install_colbert():
        print("❌ ColBERT安装失败")
        return False
    
    # 4. 测试ColBERT
    print("\n4️⃣ 测试ColBERT...")
    if not test_colbert():
        print("❌ ColBERT测试失败")
        return False
    
    # 5. 测试项目导入
    print("\n5️⃣ 测试项目导入...")
    if not test_project():
        print("❌ 项目导入失败")
        return False
    
    # 6. 创建运行脚本
    print("\n6️⃣ 创建运行脚本...")
    create_run_script()
    
    # 成功
    print("\n🎉 修复完成！")
    print("\n✅ 现在可以运行项目了:")
    print("  方法1: python src/main.py --help")
    print("  方法2: ./run.sh --help")
    
    print("\n💡 如果仍有问题，请设置环境变量:")
    print("  export PYTHONPATH=\"$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT\"")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 修复失败，请检查上述错误信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户中断")
    except Exception as e:
        print(f"\n💥 出现异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
