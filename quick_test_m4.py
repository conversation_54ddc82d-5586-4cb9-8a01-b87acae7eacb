#!/usr/bin/env python3
"""
Mac M4 快速测试脚本 - 5分钟验证环境
"""

import torch
import pytorch_lightning as pl

def main():
    print("🍎 Mac M4 快速测试 (5分钟)")
    print("=" * 40)
    
    # 1. 版本检查
    print(f"✅ PyTorch: {torch.__version__}")
    print(f"✅ Lightning: {pl.__version__}")
    
    # 2. MPS检查
    if torch.backends.mps.is_available():
        print("✅ MPS GPU加速可用")
        device = "mps"
    else:
        print("⚠️  使用CPU (MPS不可用)")
        device = "cpu"
    
    # 3. 简单计算测试
    print(f"🧮 测试计算 ({device})...")
    x = torch.randn(100, 100, device=device)
    y = torch.mm(x, x.t())
    print(f"✅ 计算成功: {y.shape}")
    
    # 4. Lightning模型测试
    print("⚡ 测试Lightning...")
    
    class QuickModel(pl.LightningModule):
        def __init__(self):
            super().__init__()
            self.layer = torch.nn.Linear(10, 1)
        
        def forward(self, x):
            return self.layer(x)
        
        def training_step(self, batch, batch_idx):
            x, y = batch
            loss = torch.nn.functional.mse_loss(self(x), y)
            return loss
        
        def configure_optimizers(self):
            return torch.optim.Adam(self.parameters())
    
    # 创建模型和数据
    model = QuickModel()
    x = torch.randn(100, 10)
    y = torch.randn(100, 1)
    dataset = torch.utils.data.TensorDataset(x, y)
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=32)
    
    # 创建训练器
    trainer = pl.Trainer(
        max_epochs=1,
        accelerator=device,
        devices=1,
        enable_progress_bar=False,
        enable_checkpointing=False,
        logger=False
    )
    
    print("🚀 开始快速训练...")
    trainer.fit(model, dataloader)
    
    print("\n🎉 所有测试通过！")
    print("✅ 您的M4环境配置正确")
    print("✅ 可以部署到服务器了")
    
    print(f"\n💡 推荐服务器配置:")
    print(f"   - PyTorch >= {torch.__version__}")
    print(f"   - Lightning >= {pl.__version__}")
    print(f"   - 使用 accelerator='gpu' (如果有GPU)")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("💡 请检查环境安装或运行完整测试脚本")
