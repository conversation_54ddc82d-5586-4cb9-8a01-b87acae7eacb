#!/usr/bin/env python3
"""
ColBERT兼容性运行脚本
"""
import sys
import os
from pathlib import Path

# 添加路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir / "src"))
sys.path.insert(0, str(current_dir / "src" / "ColBERT"))

# 设置环境变量
os.environ['PYTHONPATH'] = f"{current_dir}/src:{current_dir}/src/ColBERT"
os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'

# 临时禁用dataclass的严格检查（如果可能）
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 尝试monkey patch dataclass
try:
    import dataclasses
    original_dataclass = dataclasses.dataclass
    
    def patched_dataclass(*args, **kwargs):
        # 移除可能导致问题的参数
        kwargs.pop('slots', None)
        kwargs.pop('weakref_slot', None)
        return original_dataclass(*args, **kwargs)
    
    dataclasses.dataclass = patched_dataclass
except:
    pass

if __name__ == "__main__":
    # 运行主程序
    sys.argv[0] = str(current_dir / "src" / "main.py")
    exec(open(current_dir / "src" / "main.py").read())
