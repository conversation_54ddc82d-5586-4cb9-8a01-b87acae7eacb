#!/usr/bin/env python3
"""
直接运行Robust TableQA，自动处理ColBERT路径问题
"""

import sys
import os
from pathlib import Path

def setup_environment():
    """设置环境"""
    # 获取当前目录
    current_dir = Path(__file__).parent.absolute()
    
    # 添加必要路径到sys.path
    paths_to_add = [
        str(current_dir / "src"),
        str(current_dir / "src" / "ColBERT")
    ]
    
    for path in paths_to_add:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # 设置环境变量
    pythonpath = os.environ.get('PYTHONPATH', '')
    new_paths = ':'.join(paths_to_add)
    if pythonpath:
        os.environ['PYTHONPATH'] = f"{new_paths}:{pythonpath}"
    else:
        os.environ['PYTHONPATH'] = new_paths
    
    # 设置MPS回退（Mac M4）
    os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
    
    print(f"✅ 已添加路径到PYTHONPATH:")
    for path in paths_to_add:
        print(f"  - {path}")

def test_imports():
    """测试关键导入"""
    print("\n🧪 测试关键导入...")
    
    try:
        from colbert.modeling.tokenization import QueryTokenizer
        print("✅ ColBERT导入成功")
    except ImportError as e:
        print(f"❌ ColBERT导入失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 运行Robust TableQA (自动修复ColBERT路径)")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 测试导入
    if not test_imports():
        print("❌ 导入测试失败，但仍尝试运行...")
    
    # 运行主程序
    print("\n🎯 启动主程序...")
    main_script = Path("src/main.py")
    
    if not main_script.exists():
        print(f"❌ 主脚本不存在: {main_script}")
        return False
    
    # 修改sys.argv[0]为主脚本路径
    sys.argv[0] = str(main_script)
    
    try:
        # 执行主脚本
        exec(open(main_script).read(), {'__name__': '__main__'})
        return True
    except Exception as e:
        print(f"❌ 运行主程序失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  程序被用户中断")
    except Exception as e:
        print(f"\n💥 出现异常: {e}")
        import traceback
        traceback.print_exc()
