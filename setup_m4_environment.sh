#!/bin/bash

# Mac M4 PyTorch Lightning 环境设置脚本

echo "🍎 Mac M4 PyTorch Lightning 环境设置"
echo "======================================"

# 检查是否为Apple Silicon
if [[ $(uname -m) != "arm64" ]]; then
    echo "⚠️  警告: 此脚本专为Apple Silicon (M1/M2/M3/M4) 设计"
    echo "当前架构: $(uname -m)"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查Python版本
echo "🐍 检查Python版本..."
python3 --version

# 创建项目目录
PROJECT_DIR="pytorch_lightning_m4_test"
echo "📁 创建项目目录: $PROJECT_DIR"
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 创建虚拟环境
echo "🔧 创建虚拟环境..."
python3 -m venv venv_m4

# 激活虚拟环境
echo "⚡ 激活虚拟环境..."
source venv_m4/bin/activate

# 升级pip
echo "📦 升级pip..."
pip install --upgrade pip

# 安装PyTorch (Apple Silicon优化版本)
echo "🔥 安装PyTorch..."
pip install torch torchvision torchaudio

# 安装PyTorch Lightning
echo "⚡ 安装PyTorch Lightning..."
pip install pytorch-lightning

# 安装其他有用的包
echo "📚 安装其他依赖..."
pip install torchmetrics matplotlib jupyter notebook seaborn pandas

# 创建requirements.txt
echo "📝 创建requirements.txt..."
pip freeze > requirements.txt

echo ""
echo "✅ 环境设置完成！"
echo ""
echo "🚀 下一步:"
echo "1. 激活环境: source venv_m4/bin/activate"
echo "2. 运行测试: python test_m4_lightning.py"
echo "3. 如果测试通过，就可以部署到服务器了！"
echo ""
echo "📁 项目位置: $(pwd)"
