#!/usr/bin/env python3
"""
Mac M4 Robust TableQA 项目环境设置脚本
解决ColBERT模块和其他依赖问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """运行命令并处理错误"""
    print(f"🔧 执行: {cmd}")
    try:
        result = subprocess.run(
            cmd, shell=True, cwd=cwd, check=check,
            capture_output=True, text=True
        )
        if result.stdout:
            print(f"✅ 输出: {result.stdout.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ 错误: {e}")
        if e.stderr:
            print(f"错误详情: {e.stderr}")
        if check:
            raise
        return e

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def install_colbert():
    """安装ColBERT模块"""
    print("\n📦 安装ColBERT模块...")
    
    colbert_path = Path("src/ColBERT")
    if not colbert_path.exists():
        print(f"❌ ColBERT路径不存在: {colbert_path}")
        return False
    
    # 切换到ColBERT目录并安装
    original_cwd = os.getcwd()
    try:
        os.chdir(colbert_path)
        
        # 安装ColBERT
        result = run_command("pip install -e .", check=False)
        if result.returncode != 0:
            print("⚠️  标准安装失败，尝试强制安装...")
            run_command("pip install -e . --no-deps", check=False)
        
        print("✅ ColBERT安装完成")
        return True
        
    except Exception as e:
        print(f"❌ ColBERT安装失败: {e}")
        return False
    finally:
        os.chdir(original_cwd)

def install_pytorch_for_m4():
    """为M4芯片安装优化的PyTorch"""
    print("\n🔥 安装M4优化的PyTorch...")
    
    # 检查是否为Apple Silicon
    result = run_command("uname -m", check=False)
    if result.returncode == 0 and "arm64" in result.stdout:
        print("✅ 检测到Apple Silicon (M4)")
    else:
        print("⚠️  未检测到Apple Silicon，使用通用版本")
    
    # 安装PyTorch
    pytorch_cmd = "pip install torch torchvision torchaudio"
    run_command(pytorch_cmd)
    
    print("✅ PyTorch安装完成")

def install_project_dependencies():
    """安装项目依赖"""
    print("\n📚 安装项目依赖...")
    
    # 基础依赖
    dependencies = [
        "pytorch-lightning==1.8.2",
        "transformers==4.22.1",
        "datasets==2.6.1",
        "jsonnet",
        "easydict",
        "tqdm",
        "tfrecord",
        "frozendict",
        "wandb==0.13.4",
        "faiss-cpu",  # M4上使用CPU版本
        "bitarray",
        "spacy",
        "ujson",
        "gitpython",
        "ninja",
        "absl-py",
        "tensorboard",
        "scipy",
        "scikit-learn",
        "torch-scatter",  # 可能需要特殊处理
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        result = run_command(f"pip install {dep}", check=False)
        if result.returncode != 0:
            print(f"⚠️  {dep} 安装失败，跳过...")

def install_torch_scatter_m4():
    """为M4安装torch-scatter"""
    print("\n⚡ 安装torch-scatter (M4优化)...")
    
    try:
        # 首先尝试直接安装
        result = run_command("pip install torch-scatter", check=False)
        if result.returncode == 0:
            print("✅ torch-scatter安装成功")
            return True
        
        # 如果失败，尝试从源码安装
        print("⚠️  直接安装失败，尝试其他方法...")
        
        # 获取PyTorch版本
        import torch
        torch_version = torch.__version__.split('+')[0]  # 移除+cu118等后缀
        
        # 尝试使用wheel
        wheel_cmd = f"pip install torch-scatter -f https://data.pyg.org/whl/torch-{torch_version}+cpu.html"
        result = run_command(wheel_cmd, check=False)
        
        if result.returncode == 0:
            print("✅ torch-scatter (wheel) 安装成功")
            return True
        else:
            print("⚠️  torch-scatter安装失败，项目可能仍可运行")
            return False
            
    except Exception as e:
        print(f"❌ torch-scatter安装错误: {e}")
        return False

def setup_python_path():
    """设置Python路径"""
    print("\n🛤️  设置Python路径...")
    
    current_dir = Path.cwd()
    src_dir = current_dir / "src"
    colbert_dir = src_dir / "ColBERT"
    
    # 创建.pth文件来添加路径
    import site
    site_packages = site.getsitepackages()[0]
    pth_file = Path(site_packages) / "robust_tableqa.pth"
    
    try:
        with open(pth_file, 'w') as f:
            f.write(f"{src_dir}\n")
            f.write(f"{colbert_dir}\n")
        
        print(f"✅ Python路径已添加到: {pth_file}")
        return True
    except Exception as e:
        print(f"⚠️  无法创建.pth文件: {e}")
        print("请手动添加以下路径到PYTHONPATH:")
        print(f"  export PYTHONPATH=$PYTHONPATH:{src_dir}:{colbert_dir}")
        return False

def test_installation():
    """测试安装是否成功"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试基础导入
        import torch
        import pytorch_lightning as pl
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ Lightning: {pl.__version__}")
        
        # 测试MPS
        if torch.backends.mps.is_available():
            print("✅ MPS GPU加速可用")
        else:
            print("⚠️  MPS不可用，使用CPU")
        
        # 测试ColBERT导入
        try:
            from colbert.modeling.tokenization import QueryTokenizer
            print("✅ ColBERT导入成功")
        except ImportError as e:
            print(f"❌ ColBERT导入失败: {e}")
            return False
        
        # 测试项目主要模块
        sys.path.insert(0, str(Path.cwd() / "src"))
        try:
            from data_loader_manager import *
            print("✅ 项目模块导入成功")
        except ImportError as e:
            print(f"❌ 项目模块导入失败: {e}")
            return False
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_run_script():
    """创建运行脚本"""
    print("\n📝 创建运行脚本...")
    
    script_content = '''#!/bin/bash
# Robust TableQA 运行脚本 (Mac M4)

# 设置环境变量
export PYTHONPATH="$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT"

# 设置MPS后端（如果可用）
export PYTORCH_ENABLE_MPS_FALLBACK=1

# 运行主程序
python src/main.py "$@"
'''
    
    try:
        with open("run_tableqa.sh", "w") as f:
            f.write(script_content)
        
        # 添加执行权限
        os.chmod("run_tableqa.sh", 0o755)
        
        print("✅ 运行脚本已创建: run_tableqa.sh")
        print("使用方法: ./run_tableqa.sh [参数]")
        return True
        
    except Exception as e:
        print(f"❌ 创建运行脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🍎 Mac M4 Robust TableQA 环境设置")
    print("=" * 60)
    
    # 检查当前目录
    if not Path("src/main.py").exists():
        print("❌ 请在项目根目录运行此脚本")
        print("当前目录应包含 src/main.py 文件")
        return False
    
    success_steps = []
    
    # 1. 检查Python版本
    if check_python_version():
        success_steps.append("Python版本检查")
    
    # 2. 安装PyTorch
    try:
        install_pytorch_for_m4()
        success_steps.append("PyTorch安装")
    except Exception as e:
        print(f"❌ PyTorch安装失败: {e}")
    
    # 3. 安装项目依赖
    try:
        install_project_dependencies()
        success_steps.append("项目依赖安装")
    except Exception as e:
        print(f"❌ 项目依赖安装失败: {e}")
    
    # 4. 安装torch-scatter
    try:
        install_torch_scatter_m4()
        success_steps.append("torch-scatter安装")
    except Exception as e:
        print(f"❌ torch-scatter安装失败: {e}")
    
    # 5. 安装ColBERT
    try:
        if install_colbert():
            success_steps.append("ColBERT安装")
    except Exception as e:
        print(f"❌ ColBERT安装失败: {e}")
    
    # 6. 设置Python路径
    try:
        setup_python_path()
        success_steps.append("Python路径设置")
    except Exception as e:
        print(f"❌ Python路径设置失败: {e}")
    
    # 7. 创建运行脚本
    try:
        create_run_script()
        success_steps.append("运行脚本创建")
    except Exception as e:
        print(f"❌ 运行脚本创建失败: {e}")
    
    # 8. 测试安装
    print("\n" + "=" * 60)
    if test_installation():
        success_steps.append("安装测试")
        print("🎉 环境设置完成！")
        print("\n📋 成功完成的步骤:")
        for step in success_steps:
            print(f"  ✅ {step}")
        
        print(f"\n🚀 使用方法:")
        print(f"  1. 直接运行: python src/main.py [参数]")
        print(f"  2. 使用脚本: ./run_tableqa.sh [参数]")
        print(f"  3. 设置环境变量: export PYTHONPATH=$PYTHONPATH:$(pwd)/src:$(pwd)/src/ColBERT")
        
        return True
    else:
        print("❌ 环境设置未完全成功")
        print("请检查上述错误信息并手动解决")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  安装被用户中断")
    except Exception as e:
        print(f"\n💥 安装过程中出现错误: {e}")
        print("请检查错误信息或手动安装依赖")
