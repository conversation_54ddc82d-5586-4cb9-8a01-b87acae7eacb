#!/usr/bin/env python3
"""
下载 Intel/ColBERT-NQ 模型并显示路径信息
"""

from modelscope import snapshot_download
import os

def download_colbert_nq():
    print("🚀 开始下载 Intel/ColBERT-NQ 模型...")
    print("   这可能需要几分钟时间，请耐心等待...")

    try:
        # 下载模型
        model_dir = snapshot_download('Intel/ColBERT-NQ')

        print(f"\n✅ 模型下载完成!")
        print(f"📁 模型路径: {model_dir}")
        print(f"📁 绝对路径: {os.path.abspath(model_dir)}")

        # 检查下载的文件
        print(f"\n📋 模型文件列表:")
        total_size = 0
        for root, dirs, files in os.walk(model_dir):
            level = root.replace(model_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                total_size += file_size

                if file_size > 1024*1024:  # > 1MB
                    size_str = f"{file_size/(1024*1024):.1f}MB"
                elif file_size > 1024:  # > 1KB
                    size_str = f"{file_size/1024:.1f}KB"
                else:
                    size_str = f"{file_size}B"
                print(f"{subindent}{file} ({size_str})")

        print(f"\n📊 总大小: {total_size/(1024*1024):.1f}MB")

        # 检查模型格式
        print(f"\n🔍 模型格式检查:")
        has_dnn = any(f.endswith('.dnn') for root, dirs, files in os.walk(model_dir) for f in files)
        has_pytorch_model = os.path.exists(os.path.join(model_dir, 'pytorch_model.bin'))
        has_config = os.path.exists(os.path.join(model_dir, 'config.json'))

        if has_dnn:
            print("   ✅ 检测到 .dnn 文件 (ColBERT v1 格式)")
        if has_pytorch_model:
            print("   ✅ 检测到 pytorch_model.bin (HuggingFace 格式)")
        if has_config:
            print("   ✅ 检测到 config.json (配置文件)")

        print(f"\n🎯 在 LI-RAGE 配置文件中使用此路径:")
        print(f'   "QueryEncoderModelVersion": "{model_dir}"')

        print(f"\n📝 或者在命令行中使用:")
        print(f'   --opts model_config.QueryEncoderModelVersion="{model_dir}"')

        return model_dir

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("\n🔧 可能的解决方案:")
        print("   1. 检查网络连接")
        print("   2. 安装 modelscope: pip install modelscope")
        print("   3. 检查磁盘空间")
        return None

def show_cache_info():
    """显示 ModelScope 缓存信息"""
    print("\n📁 ModelScope 缓存位置信息:")

    # 默认缓存目录
    home_dir = os.path.expanduser("~")
    default_cache = os.path.join(home_dir, ".cache", "modelscope")

    print(f"   默认缓存目录: {default_cache}")

    # 检查环境变量
    modelscope_cache = os.environ.get('MODELSCOPE_CACHE')
    if modelscope_cache:
        print(f"   环境变量设置: {modelscope_cache}")

    # 检查缓存目录是否存在
    if os.path.exists(default_cache):
        cache_size = sum(
            os.path.getsize(os.path.join(dirpath, filename))
            for dirpath, dirnames, filenames in os.walk(default_cache)
            for filename in filenames
        )
        print(f"   缓存大小: {cache_size/(1024*1024):.1f}MB")

        # 列出已下载的模型
        hub_dir = os.path.join(default_cache, "hub")
        if os.path.exists(hub_dir):
            models = [d for d in os.listdir(hub_dir) if os.path.isdir(os.path.join(hub_dir, d))]
            if models:
                print(f"   已下载的模型:")
                for model in models[:5]:  # 只显示前5个
                    print(f"     - {model}")
                if len(models) > 5:
                    print(f"     ... 还有 {len(models) - 5} 个模型")
    else:
        print(f"   缓存目录不存在 (首次使用)")

if __name__ == "__main__":
    print("🤖 Intel/ColBERT-NQ 模型下载工具")
    print("="*50)

    # 显示缓存信息
    show_cache_info()

    # 下载模型
    model_path = download_colbert_nq()

    if model_path:
        print("\n" + "="*50)
        print("🎉 下载完成! 现在可以在 LI-RAGE 实验中使用这个模型了。")
    else:
        print("\n" + "="*50)
        print("❌ 下载失败，请检查错误信息并重试。")


print("🚀 开始下载 Intel/ColBERT-NQ 模型...")

# 下载模型
model_dir = snapshot_download('Intel/ColBERT-NQ')

print(f"✅ 模型下载完成!")
print(f"📁 模型路径: {model_dir}")
print(f"📁 绝对路径: {os.path.abspath(model_dir)}")

# 检查下载的文件
print(f"\n📋 模型文件列表:")
for root, dirs, files in os.walk(model_dir):
    level = root.replace(model_dir, '').count(os.sep)
    indent = ' ' * 2 * level
    print(f"{indent}{os.path.basename(root)}/")
    subindent = ' ' * 2 * (level + 1)
    for file in files:
        file_path = os.path.join(root, file)
        file_size = os.path.getsize(file_path)
        if file_size > 1024*1024:  # > 1MB
            size_str = f"{file_size/(1024*1024):.1f}MB"
        else:
            size_str = f"{file_size/1024:.1f}KB"
        print(f"{subindent}{file} ({size_str})")

print(f"\n🎯 在配置文件中使用此路径:")
print(f'   "QueryEncoderModelVersion": "{model_dir}"')