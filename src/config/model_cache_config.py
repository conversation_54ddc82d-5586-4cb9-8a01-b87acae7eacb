"""
模型缓存配置模块
用于统一设置模型下载和缓存路径
"""

import os
from pathlib import Path

# 自定义缓存路径配置
CUSTOM_CACHE_DIR = "/Users/<USER>/Documents/Code/models_cache"  # 修改为你想要的路径

def setup_model_cache():
    """
    设置模型缓存路径
    """
    # 确保目录存在
    Path(CUSTOM_CACHE_DIR).mkdir(parents=True, exist_ok=True)
    
    # 设置环境变量
    os.environ["TRANSFORMERS_CACHE"] = CUSTOM_CACHE_DIR
    os.environ["HF_HOME"] = CUSTOM_CACHE_DIR
    os.environ["HF_HUB_CACHE"] = CUSTOM_CACHE_DIR
    
    print(f"模型缓存路径已设置为: {CUSTOM_CACHE_DIR}")

def get_cache_dir():
    """
    获取当前缓存目录
    """
    return os.environ.get("TRANSFORMERS_CACHE", "~/.cache/huggingface/hub")

# 自动执行设置
setup_model_cache()
