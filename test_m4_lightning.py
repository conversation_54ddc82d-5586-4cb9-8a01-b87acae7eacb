#!/usr/bin/env python3
"""
Mac M4 PyTorch Lightning 测试脚本
快速验证环境配置是否正确
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import pytorch_lightning as pl
from torch.utils.data import DataLoader, TensorDataset
import time

def check_environment():
    """检查M4环境配置"""
    print("🍎 Mac M4 PyTorch Lightning 环境检查")
    print("=" * 60)
    
    # 基本版本信息
    print(f"PyTorch版本: {torch.__version__}")
    print(f"PyTorch Lightning版本: {pl.__version__}")
    print(f"Python版本: {torch.version.python}")
    
    # MPS支持检查
    print(f"\n🔍 GPU加速支持:")
    if torch.backends.mps.is_available():
        print("✅ MPS (Metal Performance Shaders) 可用")
        print("✅ 支持GPU加速训练")
        device = "mps"
    else:
        print("❌ MPS不可用，将使用CPU")
        print("💡 确保macOS >= 12.3 且PyTorch >= 1.12")
        device = "cpu"
    
    # 内存和性能信息
    print(f"\n💾 系统信息:")
    print(f"CPU线程数: {torch.get_num_threads()}")
    
    return device

def performance_test(device):
    """性能测试"""
    print(f"\n⚡ 性能测试 (设备: {device})")
    print("-" * 40)
    
    # 矩阵乘法测试
    sizes = [500, 1000, 2000]
    
    for size in sizes:
        print(f"测试矩阵大小: {size}x{size}")
        
        # 创建测试数据
        x = torch.randn(size, size, device=device)
        y = torch.randn(size, size, device=device)
        
        # 计时
        start_time = time.time()
        z = torch.mm(x, y)
        torch.mps.synchronize() if device == "mps" else None  # 确保计算完成
        end_time = time.time()
        
        print(f"  耗时: {end_time - start_time:.4f}秒")
    
    print("✅ 性能测试完成")

class TestModel(pl.LightningModule):
    """测试用的Lightning模型"""
    
    def __init__(self, input_size=784, hidden_size=128, num_classes=10, learning_rate=1e-3):
        super().__init__()
        self.save_hyperparameters()
        
        # 简单的全连接网络
        self.network = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size // 2, num_classes)
        )
        
        # 准确率计算
        try:
            from torchmetrics import Accuracy
            self.train_acc = Accuracy(task='multiclass', num_classes=num_classes)
            self.val_acc = Accuracy(task='multiclass', num_classes=num_classes)
        except ImportError:
            print("⚠️  torchmetrics未安装，跳过准确率计算")
            self.train_acc = None
            self.val_acc = None
    
    def forward(self, x):
        return self.network(x.view(x.size(0), -1))
    
    def training_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        
        # 记录损失
        self.log('train_loss', loss, prog_bar=True)
        
        # 计算准确率（如果可用）
        if self.train_acc is not None:
            preds = torch.argmax(y_hat, dim=1)
            self.train_acc(preds, y)
            self.log('train_acc', self.train_acc, prog_bar=True)
        
        return loss
    
    def validation_step(self, batch, batch_idx):
        x, y = batch
        y_hat = self(x)
        loss = F.cross_entropy(y_hat, y)
        
        self.log('val_loss', loss, prog_bar=True)
        
        if self.val_acc is not None:
            preds = torch.argmax(y_hat, dim=1)
            self.val_acc(preds, y)
            self.log('val_acc', self.val_acc, prog_bar=True)
        
        return loss
    
    def configure_optimizers(self):
        optimizer = torch.optim.Adam(self.parameters(), lr=self.hparams.learning_rate)
        scheduler = torch.optim.lr_scheduler.StepLR(optimizer, step_size=5, gamma=0.5)
        return [optimizer], [scheduler]

def create_test_data(num_samples=1000, input_size=784, num_classes=10):
    """创建测试数据集"""
    # 生成随机数据
    X = torch.randn(num_samples, input_size)
    y = torch.randint(0, num_classes, (num_samples,))
    return TensorDataset(X, y)

def run_training_test(device):
    """运行训练测试"""
    print(f"\n🚀 Lightning训练测试 (设备: {device})")
    print("-" * 50)
    
    # 创建数据
    print("📊 创建测试数据...")
    train_dataset = create_test_data(2000, 784, 10)
    val_dataset = create_test_data(400, 784, 10)
    
    # 数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=128, 
        shuffle=True,
        num_workers=0  # M4上建议设为0
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=128, 
        shuffle=False,
        num_workers=0
    )
    
    # 创建模型
    print("🧠 创建模型...")
    model = TestModel(input_size=784, hidden_size=256, num_classes=10, learning_rate=1e-3)
    
    # 配置训练器
    trainer = pl.Trainer(
        max_epochs=3,  # 快速测试，只训练3个epoch
        accelerator=device,
        devices=1,
        enable_progress_bar=True,
        enable_model_summary=True,
        log_every_n_steps=5,
        enable_checkpointing=False,  # 测试时不保存检查点
        logger=False,  # 不使用logger
    )
    
    print(f"⚙️  训练器配置: {trainer.accelerator}")
    
    # 开始训练
    print("🎯 开始训练...")
    start_time = time.time()
    
    try:
        trainer.fit(model, train_loader, val_loader)
        end_time = time.time()
        
        print(f"✅ 训练完成！总耗时: {end_time - start_time:.2f}秒")
        
        # 显示最终指标
        if trainer.callback_metrics:
            print("📈 最终指标:")
            for key, value in trainer.callback_metrics.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Mac M4 PyTorch Lightning 完整测试")
    print("=" * 60)
    
    try:
        # 1. 环境检查
        device = check_environment()
        
        # 2. 性能测试
        performance_test(device)
        
        # 3. 训练测试
        success = run_training_test(device)
        
        # 4. 总结
        print("\n" + "=" * 60)
        if success:
            print("🎉 恭喜！所有测试通过，您的M4环境配置完美！")
            print("✅ 可以安全地将代码部署到服务器")
            print("\n💡 建议:")
            print("  - 在服务器上使用相同的PyTorch Lightning版本")
            print("  - 根据服务器GPU调整accelerator设置")
            print("  - 可以增加batch_size以充分利用服务器资源")
        else:
            print("❌ 测试未完全通过，请检查环境配置")
            print("💡 建议:")
            print("  - 确保PyTorch和Lightning版本兼容")
            print("  - 检查是否正确安装了所有依赖")
            print("  - 尝试重新创建虚拟环境")
        
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试过程中出现错误: {e}")
        print("请检查环境配置或依赖安装")

if __name__ == "__main__":
    main()
