#!/usr/bin/env python3
"""
项目重构验证脚本
检查ITR和LI-RAGE实验是否能够独立运行
"""

import os
import sys
from pathlib import Path

def check_directory_structure():
    """检查目录结构是否正确"""
    print("🔍 检查目录结构...")
    
    required_dirs = [
        "shared/src/data_loader_manager",
        "shared/src/trainers", 
        "shared/src/utils",
        "shared/configs",
        "ITR_experiments/src/models/rag",
        "ITR_experiments/src/trainers",
        "ITR_experiments/configs/wtq",
        "ITR_experiments/configs/wikisql",
        "LIRAGE_experiments/src/ColBERT",
        "LIRAGE_experiments/src/models/rag",
        "LIRAGE_experiments/src/trainers",
        "LIRAGE_experiments/configs/e2e_wtq",
        "LIRAGE_experiments/configs/nq_tables"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print("❌ 缺少以下目录:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
        return False
    else:
        print("✅ 目录结构检查通过")
        return True

def check_key_files():
    """检查关键文件是否存在"""
    print("\n🔍 检查关键文件...")
    
    key_files = {
        "共享组件": [
            "shared/src/data_loader_manager/data_loader_wrapper.py",
            "shared/src/data_loader_manager/module_parser.py",
            "shared/src/trainers/base_executor.py",
            "shared/configs/base_env.jsonnet"
        ],
        "ITR实验": [
            "ITR_experiments/README.md",
            "ITR_experiments/run_itr_experiments.sh",
            "ITR_experiments/src/main.py",
            "ITR_experiments/src/models/rag/itr_rag.py",
            "ITR_experiments/src/models/rag/itr_rag_reduce.py",
            "ITR_experiments/src/trainers/ITR_rag_executor.py"
        ],
        "LI-RAGE实验": [
            "LIRAGE_experiments/README.md", 
            "LIRAGE_experiments/run_lirage_experiments.sh",
            "LIRAGE_experiments/src/main.py",
            "LIRAGE_experiments/src/ColBERT/colbert/modeling/colbert.py",
            "LIRAGE_experiments/src/models/rag/rag_model.py",
            "LIRAGE_experiments/src/trainers/RAG_executor.py",
            "LIRAGE_experiments/src/trainers/ColBERT_executor.py"
        ]
    }
    
    all_good = True
    for category, files in key_files.items():
        print(f"\n📂 {category}:")
        missing_files = []
        for file_path in files:
            if Path(file_path).exists():
                print(f"   ✅ {file_path}")
            else:
                print(f"   ❌ {file_path}")
                missing_files.append(file_path)
                all_good = False
        
        if missing_files:
            print(f"   ⚠️  {category} 缺少 {len(missing_files)} 个文件")
    
    return all_good

def check_config_files():
    """检查配置文件分离情况"""
    print("\n🔍 检查配置文件分离...")
    
    itr_configs = list(Path("ITR_experiments/configs").rglob("*ITR*.jsonnet"))
    lirage_configs = list(Path("LIRAGE_experiments/configs").rglob("colbert*.jsonnet"))
    lirage_configs.extend(list(Path("LIRAGE_experiments/configs").rglob("rag.jsonnet")))
    
    print(f"📊 ITR配置文件: {len(itr_configs)} 个")
    for config in itr_configs[:5]:  # 显示前5个
        print(f"   - {config}")
    if len(itr_configs) > 5:
        print(f"   ... 还有 {len(itr_configs) - 5} 个")
    
    print(f"\n📊 LI-RAGE配置文件: {len(lirage_configs)} 个")
    for config in lirage_configs[:5]:  # 显示前5个
        print(f"   - {config}")
    if len(lirage_configs) > 5:
        print(f"   ... 还有 {len(lirage_configs) - 5} 个")
    
    return len(itr_configs) > 0 and len(lirage_configs) > 0

def check_scripts_executable():
    """检查运行脚本是否可执行"""
    print("\n🔍 检查运行脚本...")
    
    scripts = [
        "ITR_experiments/run_itr_experiments.sh",
        "LIRAGE_experiments/run_lirage_experiments.sh"
    ]
    
    all_executable = True
    for script in scripts:
        if Path(script).exists():
            if os.access(script, os.X_OK):
                print(f"   ✅ {script} (可执行)")
            else:
                print(f"   ⚠️  {script} (不可执行)")
                all_executable = False
        else:
            print(f"   ❌ {script} (不存在)")
            all_executable = False
    
    return all_executable

def test_import_paths():
    """测试导入路径是否正确"""
    print("\n🔍 测试导入路径...")
    
    # 测试ITR实验导入
    print("\n📂 ITR实验导入测试:")
    itr_src = Path("ITR_experiments/src")
    shared_src = Path("shared/src")
    
    if itr_src.exists() and shared_src.exists():
        sys.path.insert(0, str(itr_src))
        sys.path.insert(0, str(shared_src))
        
        try:
            # 测试共享组件导入
            from data_loader_manager.data_loader_wrapper import DataLoaderWrapper
            print("   ✅ 共享数据加载器导入成功")
            
            # 测试ITR特有组件导入 (如果存在)
            if (itr_src / "models" / "rag" / "itr_rag.py").exists():
                sys.path.insert(0, str(itr_src / "models" / "rag"))
                print("   ✅ ITR模型路径可访问")
            
        except ImportError as e:
            print(f"   ❌ ITR导入失败: {e}")
            return False
    
    # 测试LI-RAGE实验导入
    print("\n📂 LI-RAGE实验导入测试:")
    lirage_src = Path("LIRAGE_experiments/src")
    
    if lirage_src.exists():
        sys.path.insert(0, str(lirage_src))
        
        try:
            # 测试ColBERT导入
            if (lirage_src / "ColBERT").exists():
                print("   ✅ ColBERT模块路径可访问")
            
        except ImportError as e:
            print(f"   ❌ LI-RAGE导入失败: {e}")
            return False
    
    return True

def generate_summary():
    """生成重构总结"""
    print("\n" + "="*60)
    print("📊 项目重构总结")
    print("="*60)
    
    print("\n🎯 重构目标:")
    print("   ✅ 将ITR和LI-RAGE实验完全分离")
    print("   ✅ 每个实验可以独立运行")
    print("   ✅ 共享组件避免代码重复")
    print("   ✅ 清晰的文档和运行脚本")
    
    print("\n📁 新的项目结构:")
    print("   📂 shared/           - 共享组件")
    print("   📂 ITR_experiments/  - ITR实验 (单表格子表检索)")
    print("   📂 LIRAGE_experiments/ - LI-RAGE实验 (开放域晚交互RAG)")
    print("   📂 datasets/         - 共享数据集")
    
    print("\n🚀 如何使用:")
    print("   cd ITR_experiments && ./run_itr_experiments.sh help")
    print("   cd LIRAGE_experiments && ./run_lirage_experiments.sh help")
    
    print("\n📚 详细文档:")
    print("   - README_NEW.md (主项目说明)")
    print("   - ITR_experiments/README.md (ITR实验说明)")
    print("   - LIRAGE_experiments/README.md (LI-RAGE实验说明)")

def main():
    """主验证函数"""
    print("🚀 开始验证项目重构")
    print("="*60)
    
    checks = [
        ("目录结构", check_directory_structure),
        ("关键文件", check_key_files),
        ("配置文件", check_config_files),
        ("运行脚本", check_scripts_executable),
        ("导入路径", test_import_paths)
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 检查失败: {e}")
            results.append((name, False))
    
    # 显示检查结果
    print("\n" + "="*60)
    print("📋 检查结果汇总")
    print("="*60)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项检查通过")
    
    if passed == len(results):
        print("🎉 项目重构验证成功!")
        print("   两个实验现在可以独立运行了")
    else:
        print("⚠️  项目重构需要进一步调整")
        print("   请检查上述失败的项目")
    
    generate_summary()

if __name__ == "__main__":
    main()
